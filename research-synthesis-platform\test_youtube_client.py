#!/usr/bin/env python3
"""
Test script to verify YouTube client implementation from Sprint 2
Tests the YouTube client with exact code from sprint plans
"""

import sys
import os
from pathlib import Path

# Add paths for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "modules" / "external-integration" / "src"))

def test_youtube_imports():
    """Test that YouTube client modules can be imported correctly"""
    print("Testing YouTube client imports from Sprint 2 implementation...")
    
    try:
        # Test YouTube client imports
        from external_integration.clients.youtube_client import (
            YouTubeClient, YouTubeVideo, VideoTranscript, VideoComment, VideoQuality
        )
        print("✓ YouTube client imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_youtube_client_creation():
    """Test YouTube client creation and basic functionality"""
    print("\nTesting YouTube client creation...")
    
    try:
        from external_integration.clients.youtube_client import YouTubeClient
        
        # Create YouTube client (with dummy API key for testing)
        client = YouTubeClient(api_key="dummy-api-key-for-testing", timeout=30)
        print("✓ YouTube client created successfully")
        
        # Test URL parsing
        video_id = client._extract_video_id("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
        assert video_id == "dQw4w9WgXcQ"
        print("✓ YouTube URL parsing works correctly")
        
        # Test alternative URL formats
        video_id2 = client._extract_video_id("https://youtu.be/dQw4w9WgXcQ")
        assert video_id2 == "dQw4w9WgXcQ"
        print("✓ YouTube short URL parsing works correctly")
        
        # Test SRT timestamp parsing
        timestamp = client._parse_srt_timestamp("00:01:30,500")
        assert timestamp == 90.5  # 1 minute 30.5 seconds
        print("✓ SRT timestamp parsing works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ YouTube client test failed: {e}")
        return False

def test_youtube_analysis_methods():
    """Test YouTube content analysis methods"""
    print("\nTesting YouTube analysis methods...")
    
    try:
        from external_integration.clients.youtube_client import YouTubeClient, YouTubeVideo, VideoTranscript
        from datetime import datetime
        
        client = YouTubeClient(api_key="dummy-api-key", timeout=30)
        
        # Create test video data
        test_video = YouTubeVideo(
            video_id="test123",
            title="AutoGen Tutorial: Building AI Agents with Python",
            description="Learn how to build AI agents using the AutoGen framework. This tutorial covers Python programming, machine learning concepts, and API development.",
            channel_title="Tech Education",
            published_at=datetime(2024, 1, 1),
            duration="PT10M30S",
            view_count=10000,
            like_count=500,
            comment_count=50,
            tags=["python", "ai", "tutorial", "programming"],
            category_id="28",
            language="en",
            thumbnail_url="https://example.com/thumb.jpg"
        )
        
        # Test engagement metrics calculation
        metrics = client._calculate_engagement_metrics(test_video)
        assert "engagement_rate" in metrics
        assert "view_velocity" in metrics
        assert metrics["total_engagements"] == 550  # 500 likes + 50 comments
        print("✓ Engagement metrics calculation works correctly")
        
        # Test content topic extraction
        topics = client._extract_content_topics(test_video, None)
        assert "python" in topics
        assert "ai" in topics
        assert "tutorial" in topics
        print("✓ Content topic extraction works correctly")
        
        # Test educational value assessment
        educational_value = client._assess_educational_value(test_video, None)
        assert "educational_score" in educational_value
        assert educational_value["has_tutorial_keywords"] == True
        print("✓ Educational value assessment works correctly")
        
        # Test technical content identification
        technical_content = client._identify_technical_content(test_video, None)
        assert "python" in technical_content["programming_languages"]
        assert "autogen" in technical_content["frameworks_mentioned"]
        print("✓ Technical content identification works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ YouTube analysis methods test failed: {e}")
        return False

def test_youtube_data_models():
    """Test YouTube data models"""
    print("\nTesting YouTube data models...")
    
    try:
        from external_integration.clients.youtube_client import (
            YouTubeVideo, VideoTranscript, VideoComment, VideoQuality
        )
        from datetime import datetime
        
        # Test YouTubeVideo model
        video = YouTubeVideo(
            video_id="test123",
            title="Test Video",
            description="Test Description",
            channel_title="Test Channel",
            published_at=datetime.now(),
            duration="PT5M",
            view_count=1000,
            like_count=100,
            comment_count=10,
            tags=["test"],
            category_id="22",
            language="en",
            thumbnail_url="https://example.com/thumb.jpg"
        )
        
        assert video.video_id == "test123"
        assert video.title == "Test Video"
        assert video.view_count == 1000
        print("✓ YouTubeVideo model works correctly")
        
        # Test VideoTranscript model
        transcript = VideoTranscript(
            video_id="test123",
            language="en",
            transcript_segments=[
                {"start": 0.0, "duration": 5.0, "text": "Hello world"},
                {"start": 5.0, "duration": 5.0, "text": "This is a test"}
            ],
            total_duration=10.0,
            word_count=6,
            extracted_at=datetime.now(),
            confidence_score=0.95
        )
        
        assert transcript.video_id == "test123"
        assert len(transcript.transcript_segments) == 2
        assert transcript.word_count == 6
        print("✓ VideoTranscript model works correctly")
        
        # Test VideoComment model
        comment = VideoComment(
            comment_id="comment123",
            author="Test User",
            text="Great video!",
            like_count=5,
            published_at=datetime.now(),
            reply_count=2
        )
        
        assert comment.comment_id == "comment123"
        assert comment.author == "Test User"
        assert comment.like_count == 5
        print("✓ VideoComment model works correctly")
        
        # Test VideoQuality enum
        assert VideoQuality.HIGH == "high"
        assert VideoQuality.MEDIUM == "medium"
        assert VideoQuality.LOW == "low"
        print("✓ VideoQuality enum works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ YouTube data models test failed: {e}")
        return False

def main():
    """Run all YouTube client tests"""
    print("=" * 60)
    print("YOUTUBE CLIENT FUNCTIONALITY TEST")
    print("Testing exact code implementation from Sprint 2 plans")
    print("=" * 60)
    
    tests = [
        test_youtube_imports,
        test_youtube_client_creation,
        test_youtube_analysis_methods,
        test_youtube_data_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All YouTube client tests PASSED!")
        print("The YouTube client implementation includes:")
        print("  ✓ Complete YouTube API client with quota tracking")
        print("  ✓ Video metadata extraction and analysis")
        print("  ✓ Transcript extraction with SRT parsing")
        print("  ✓ Comment analysis and sentiment detection")
        print("  ✓ Educational value assessment")
        print("  ✓ Technical content identification")
        print("  ✓ Engagement metrics calculation")
        print("\nNote: Full API testing requires valid YouTube API key and network access.")
    else:
        print("❌ Some tests FAILED!")
        print("Check the implementation against the sprint plan.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional, List
from datetime import datetime
import time
import uuid
from enum import Enum


class APIResponse(BaseModel):
    """Standardized API response format"""
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    errors: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    request_id: str
    timestamp: datetime
    execution_time_ms: float


class APIRequest(BaseModel):
    """Standardized API request format"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    module_id: str
    operation: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    context: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=1, ge=1, le=10)
    timeout: int = Field(default=30, ge=1, le=300)


class HealthStatus(str, Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class HealthResponse(BaseModel):
    status: HealthStatus
    timestamp: datetime
    version: str
    uptime_seconds: float
    dependencies: Dict[str, HealthStatus]
    metrics: Dict[str, Any]


def create_api_app(
    title: str,
    description: str,
    version: str,
    module_name: str
) -> FastAPI:
    """Create standardized FastAPI application"""

    app = FastAPI(
        title=title,
        description=description,
        version=version,
        docs_url=f"/docs",
        redoc_url=f"/redoc",
        openapi_url=f"/openapi.json"
    )

    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add request tracking middleware
    @app.middleware("http")
    async def track_requests(request: Request, call_next):
        start_time = time.time()
        request_id = str(uuid.uuid4())

        # Add request ID to headers
        request.state.request_id = request_id

        response = await call_next(request)

        execution_time = (time.time() - start_time) * 1000
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Execution-Time"] = str(execution_time)

        return response

    # Add standard health check endpoint
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        return HealthResponse(
            status=HealthStatus.HEALTHY,
            timestamp=datetime.utcnow(),
            version=version,
            uptime_seconds=time.time() - app.state.start_time,
            dependencies={},
            metrics={}
        )

    # Add standard info endpoint
    @app.get("/info")
    async def info():
        return {
            "module": module_name,
            "version": version,
            "timestamp": datetime.utcnow(),
            "status": "running"
        }

    # Store app metadata
    app.state.start_time = time.time()
    app.state.module_name = module_name

    return app


def create_success_response(
    data: Any = None,
    message: str = "Success",
    request_id: str = None,
    metadata: Dict[str, Any] = None,
    execution_time_ms: float = 0
) -> APIResponse:
    """Create successful API response"""
    return APIResponse(
        success=True,
        data=data,
        message=message,
        request_id=request_id or str(uuid.uuid4()),
        timestamp=datetime.utcnow(),
        execution_time_ms=execution_time_ms,
        metadata=metadata
    )


def create_error_response(
    message: str,
    errors: List[str] = None,
    request_id: str = None,
    execution_time_ms: float = 0
) -> APIResponse:
    """Create error API response"""
    return APIResponse(
        success=False,
        message=message,
        errors=errors or [message],
        request_id=request_id or str(uuid.uuid4()),
        timestamp=datetime.utcnow(),
        execution_time_ms=execution_time_ms
    )

# agent_orchestration/orchestrator.py
import asyncio
import logging
import json
import uuid
from typing import Any, Dict, List, Optional, Callable, Type, Union
from datetime import datetime
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from enum import Enum
import httpx
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# Import AutoGen 0.4+ components (adjust imports based on actual version)
try:
    from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
    from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
    from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
    from autogen_agentchat.messages import TextMessage, ChatMessage
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_core import Component, TopicId, MessageContext
    from autogen_core.application import SingleThreadedAgentRuntime
except ImportError:
    # Fallback for different AutoGen versions or testing
    logging.warning("AutoGen imports not available, using mock classes")
    
    class AssistantAgent:
        def __init__(self, name: str, model_client=None, **kwargs):
            self.name = name
            self.model_client = model_client
    
    class UserProxyAgent:
        def __init__(self, name: str, **kwargs):
            self.name = name
    
    class RoundRobinGroupChat:
        def __init__(self, participants, **kwargs):
            self.participants = participants
    
    class TextMessage:
        def __init__(self, content: str, source: str):
            self.content = content
            self.source = source

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Enums
class AgentType(Enum):
    ASSISTANT = "assistant"
    USER_PROXY = "user_proxy"
    RESEARCH_COORDINATOR = "research_coordinator"
    SOURCE_ANALYZER = "source_analyzer"
    CONTENT_GENERATOR = "content_generator"
    SYNTHESIS_AGENT = "synthesis_agent"

class WorkflowStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class MessageType(Enum):
    TEXT = "text"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    STATUS_UPDATE = "status_update"
    ERROR = "error"

# Data Classes
@dataclass
class AgentConfig:
    name: str
    agent_type: AgentType
    model_client: Optional[Any] = None
    system_message: Optional[str] = None
    tools: List[str] = None
    max_consecutive_auto_reply: int = 3
    human_input_mode: str = "NEVER"
    code_execution_config: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tools is None:
            self.tools = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class WorkflowConfig:
    workflow_id: str
    name: str
    agents: List[AgentConfig]
    team_type: str = "round_robin"  # or "selector"
    termination_conditions: List[Dict[str, Any]] = None
    max_turns: int = 10
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.termination_conditions is None:
            self.termination_conditions = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class WorkflowExecution:
    execution_id: str
    workflow_id: str
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

# Pydantic Models for API
class AgentConfigRequest(BaseModel):
    name: str
    agent_type: str
    system_message: Optional[str] = None
    tools: List[str] = []
    model_config: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = {}

class WorkflowRequest(BaseModel):
    name: str
    agents: List[AgentConfigRequest]
    team_type: str = "round_robin"
    termination_conditions: List[Dict[str, Any]] = []
    max_turns: int = 10
    initial_message: str
    metadata: Dict[str, Any] = {}

class ExecutionResponse(BaseModel):
    execution_id: str
    workflow_id: str
    status: str
    created_at: datetime
    result: Optional[Dict[str, Any]] = None

# Agent Factory
class AgentFactory:
    def __init__(self, memory_client: Optional[httpx.AsyncClient] = None):
        self.memory_client = memory_client
        self.registered_tools: Dict[str, Callable] = {}
        self.model_clients: Dict[str, Any] = {}
    
    def register_tool(self, name: str, func: Callable):
        """Register a tool function"""
        self.registered_tools[name] = func
        logger.info(f"Registered tool: {name}")
    
    def register_model_client(self, name: str, client: Any):
        """Register a model client"""
        self.model_clients[name] = client
        logger.info(f"Registered model client: {name}")
    
    async def create_agent(self, config: AgentConfig) -> Any:
        """Create an agent based on configuration"""
        try:
            if config.agent_type == AgentType.ASSISTANT:
                return await self._create_assistant_agent(config)
            elif config.agent_type == AgentType.USER_PROXY:
                return await self._create_user_proxy_agent(config)
            elif config.agent_type == AgentType.RESEARCH_COORDINATOR:
                return await self._create_research_coordinator_agent(config)
            elif config.agent_type == AgentType.SOURCE_ANALYZER:
                return await self._create_source_analyzer_agent(config)
            elif config.agent_type == AgentType.CONTENT_GENERATOR:
                return await self._create_content_generator_agent(config)
            elif config.agent_type == AgentType.SYNTHESIS_AGENT:
                return await self._create_synthesis_agent(config)
            else:
                raise ValueError(f"Unknown agent type: {config.agent_type}")
        
        except Exception as e:
            logger.error(f"Failed to create agent {config.name}: {e}")
            raise
    
    async def _create_assistant_agent(self, config: AgentConfig) -> AssistantAgent:
        """Create an assistant agent"""
        model_client = config.model_client or self.model_clients.get("default")
        
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=config.system_message or f"You are {config.name}, an AI assistant.",
            max_consecutive_auto_reply=config.max_consecutive_auto_reply
        )
        
        # Register tools if any
        for tool_name in config.tools:
            if tool_name in self.registered_tools:
                # Tool registration would depend on AutoGen version
                pass
        
        return agent
    
    async def _create_user_proxy_agent(self, config: AgentConfig) -> UserProxyAgent:
        """Create a user proxy agent"""
        agent = UserProxyAgent(
            name=config.name,
            human_input_mode=config.human_input_mode,
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            code_execution_config=config.code_execution_config or False
        )
        
        return agent
    
    async def _create_research_coordinator_agent(self, config: AgentConfig) -> AssistantAgent:
        """Create a specialized research coordinator agent"""
        system_message = config.system_message or """
        You are a Research Coordinator Agent responsible for:
        1. Analyzing research tasks and breaking them down into subtasks
        2. Coordinating with other agents for multi-source research
        3. Ensuring research quality and completeness
        4. Synthesizing findings from multiple sources
        5. Managing research workflow and progress tracking
        
        Always maintain high research standards and ensure all sources are properly analyzed.
        """
        
        model_client = config.model_client or self.model_clients.get("default")
        
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=system_message,
            max_consecutive_auto_reply=config.max_consecutive_auto_reply
        )
        
        return agent
    
    async def _create_source_analyzer_agent(self, config: AgentConfig) -> AssistantAgent:
        """Create a specialized source analysis agent"""
        system_message = config.system_message or """
        You are a Source Analysis Agent specialized in:
        1. Analyzing various content sources (GitHub, YouTube, web articles, documents)
        2. Extracting key information and insights
        3. Assessing source credibility and bias
        4. Identifying relationships between different sources
        5. Providing structured analysis results
        
        Always provide thorough, objective analysis with proper citations.
        """
        
        model_client = config.model_client or self.model_clients.get("default")
        
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=system_message,
            max_consecutive_auto_reply=config.max_consecutive_auto_reply
        )
        
        return agent
    
    async def _create_content_generator_agent(self, config: AgentConfig) -> AssistantAgent:
        """Create a specialized content generation agent"""
        system_message = config.system_message or """
        You are a Content Generation Agent responsible for:
        1. Creating high-quality written content from research findings
        2. Adapting content for different audiences and formats
        3. Generating executive summaries and detailed reports
        4. Creating engaging dialogue for podcast scripts
        5. Ensuring content coherence and flow
        
        Always create well-structured, engaging content that accurately represents the research.
        """
        
        model_client = config.model_client or self.model_clients.get("default")
        
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=system_message,
            max_consecutive_auto_reply=config.max_consecutive_auto_reply
        )
        
        return agent
    
    async def _create_synthesis_agent(self, config: AgentConfig) -> AssistantAgent:
        """Create a specialized synthesis agent"""
        system_message = config.system_message or """
        You are a Synthesis Agent specialized in:
        1. Combining information from multiple sources and agents
        2. Identifying patterns and connections across different analyses
        3. Creating comprehensive synthesis reports
        4. Ensuring logical flow and coherence in final outputs
        5. Quality assurance for multi-agent outputs
        
        Focus on creating comprehensive, well-reasoned synthesis that adds value beyond individual agent contributions.
        """
        
        model_client = config.model_client or self.model_clients.get("default")
        
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=system_message,
            max_consecutive_auto_reply=config.max_consecutive_auto_reply
        )
        
        return agent

# Workflow Manager
class WorkflowManager:
    def __init__(self, agent_factory: AgentFactory, memory_client: Optional[httpx.AsyncClient] = None):
        self.agent_factory = agent_factory
        self.memory_client = memory_client
        self.workflows: Dict[str, WorkflowConfig] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.active_teams: Dict[str, Any] = {}
    
    async def register_workflow(self, config: WorkflowConfig) -> str:
        """Register a new workflow configuration"""
        self.workflows[config.workflow_id] = config
        logger.info(f"Registered workflow: {config.workflow_id}")
        return config.workflow_id
    
    async def execute_workflow(
        self,
        workflow_id: str,
        initial_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Execute a workflow with given parameters"""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        execution_id = str(uuid.uuid4())
        workflow_config = self.workflows[workflow_id]
        
        # Create execution record
        execution = WorkflowExecution(
            execution_id=execution_id,
            workflow_id=workflow_id,
            status=WorkflowStatus.PENDING,
            created_at=datetime.utcnow(),
            metadata=context or {}
        )
        
        self.executions[execution_id] = execution
        
        # Start execution in background
        asyncio.create_task(self._run_workflow(execution_id, initial_message))
        
        return execution_id
    
    async def _run_workflow(self, execution_id: str, initial_message: str):
        """Run the actual workflow execution"""
        execution = self.executions[execution_id]
        workflow_config = self.workflows[execution.workflow_id]
        
        try:
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.utcnow()
            
            # Create agents
            agents = []
            for agent_config in workflow_config.agents:
                agent = await self.agent_factory.create_agent(agent_config)
                agents.append(agent)
            
            # Create team based on configuration
            if workflow_config.team_type == "round_robin":
                team = RoundRobinGroupChat(participants=agents)
            elif workflow_config.team_type == "selector":
                team = SelectorGroupChat(participants=agents)
            else:
                raise ValueError(f"Unknown team type: {workflow_config.team_type}")
            
            # Store active team
            self.active_teams[execution_id] = team
            
            # Set up termination conditions
            termination_conditions = []
            for term_config in workflow_config.termination_conditions:
                if term_config.get("type") == "max_messages":
                    termination_conditions.append(
                        MaxMessageTermination(max_messages=term_config.get("value", 10))
                    )
                elif term_config.get("type") == "text_mention":
                    termination_conditions.append(
                        TextMentionTermination(text=term_config.get("value", "TERMINATE"))
                    )
            
            if not termination_conditions:
                termination_conditions.append(MaxMessageTermination(max_messages=workflow_config.max_turns))
            
            # Execute conversation
            result = await team.run(
                task=initial_message,
                termination_condition=termination_conditions[0] if termination_conditions else None
            )
            
            # Store memory context if memory client available
            if self.memory_client:
                await self._store_execution_memory(execution_id, result)
            
            # Update execution status
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.utcnow()
            execution.result = {"messages": result.messages if hasattr(result, 'messages') else []}
            
            logger.info(f"Workflow execution {execution_id} completed successfully")
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.error_message = str(e)
            logger.error(f"Workflow execution {execution_id} failed: {e}")
        
        finally:
            # Cleanup active team
            if execution_id in self.active_teams:
                del self.active_teams[execution_id]
    
    async def _store_execution_memory(self, execution_id: str, result: Any):
        """Store execution results in memory"""
        try:
            execution = self.executions[execution_id]
            
            memory_data = {
                "session_id": execution_id,
                "agent_id": "workflow_orchestrator",
                "memory_type": "execution_result",
                "content": json.dumps({
                    "execution_id": execution_id,
                    "workflow_id": execution.workflow_id,
                    "result": execution.result
                }),
                "metadata": {
                    "execution_timestamp": execution.completed_at.isoformat() if execution.completed_at else None,
                    "workflow_type": "agent_orchestration"
                }
            }
            
            response = await self.memory_client.post("/memory/store", json=memory_data)
            response.raise_for_status()
            
        except Exception as e:
            logger.warning(f"Failed to store execution memory: {e}")
    
    async def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get the status of a workflow execution"""
        return self.executions.get(execution_id)
    
    async def pause_execution(self, execution_id: str) -> bool:
        """Pause a running workflow execution"""
        if execution_id in self.executions:
            execution = self.executions[execution_id]
            if execution.status == WorkflowStatus.RUNNING:
                execution.status = WorkflowStatus.PAUSED
                return True
        return False
    
    async def resume_execution(self, execution_id: str) -> bool:
        """Resume a paused workflow execution"""
        if execution_id in self.executions:
            execution = self.executions[execution_id]
            if execution.status == WorkflowStatus.PAUSED:
                execution.status = WorkflowStatus.RUNNING
                return True
        return False
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel a workflow execution"""
        if execution_id in self.executions:
            execution = self.executions[execution_id]
            if execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]:
                execution.status = WorkflowStatus.CANCELLED
                execution.completed_at = datetime.utcnow()
                
                # Cleanup active team
                if execution_id in self.active_teams:
                    del self.active_teams[execution_id]
                
                return True
        return False

# Advanced Orchestrator
class AdvancedOrchestrator:
    def __init__(self, memory_service_url: str = "http://localhost:8001"):
        self.memory_service_url = memory_service_url
        self.memory_client = httpx.AsyncClient(base_url=memory_service_url)
        self.agent_factory = AgentFactory(self.memory_client)
        self.workflow_manager = WorkflowManager(self.agent_factory, self.memory_client)
        
        # Performance monitoring
        self.execution_metrics: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self):
        """Initialize the orchestrator"""
        # Register default model clients
        try:
            # Example with OpenAI client
            openai_client = OpenAIChatCompletionClient(model="gpt-4")
            self.agent_factory.register_model_client("default", openai_client)
            self.agent_factory.register_model_client("openai", openai_client)
        except Exception as e:
            logger.warning(f"Could not initialize OpenAI client: {e}")
        
        # Register common tools
        await self._register_common_tools()
        
        logger.info("Advanced Orchestrator initialized")
    
    async def _register_common_tools(self):
        """Register commonly used tools"""
        
        async def web_search_tool(query: str) -> str:
            """Web search tool"""
            # This would integrate with external integration module
            return f"Mock search results for: {query}"
        
        async def github_analysis_tool(repo_url: str) -> str:
            """GitHub repository analysis tool"""
            # This would integrate with external integration module
            return f"Mock GitHub analysis for: {repo_url}"
        
        async def youtube_analysis_tool(video_url: str) -> str:
            """YouTube video analysis tool"""
            # This would integrate with external integration module
            return f"Mock YouTube analysis for: {video_url}"
        
        self.agent_factory.register_tool("web_search", web_search_tool)
        self.agent_factory.register_tool("github_analysis", github_analysis_tool)
        self.agent_factory.register_tool("youtube_analysis", youtube_analysis_tool)
    
    async def create_research_workflow(self, research_topic: str) -> str:
        """Create a standard research workflow"""
        workflow_id = f"research_{uuid.uuid4().hex[:8]}"
        
        # Define agents for research workflow
        agents = [
            AgentConfig(
                name="research_coordinator",
                agent_type=AgentType.RESEARCH_COORDINATOR,
                tools=["web_search", "github_analysis", "youtube_analysis"]
            ),
            AgentConfig(
                name="source_analyzer_1",
                agent_type=AgentType.SOURCE_ANALYZER,
                tools=["web_search", "github_analysis"]
            ),
            AgentConfig(
                name="source_analyzer_2",
                agent_type=AgentType.SOURCE_ANALYZER,
                tools=["youtube_analysis", "web_search"]
            ),
            AgentConfig(
                name="synthesis_agent",
                agent_type=AgentType.SYNTHESIS_AGENT
            )
        ]
        
        workflow_config = WorkflowConfig(
            workflow_id=workflow_id,
            name=f"Research Workflow: {research_topic}",
            agents=agents,
            team_type="round_robin",
            termination_conditions=[
                {"type": "text_mention", "value": "RESEARCH_COMPLETE"},
                {"type": "max_messages", "value": 20}
            ],
            max_turns=20,
            metadata={"research_topic": research_topic}
        )
        
        await self.workflow_manager.register_workflow(workflow_config)
        return workflow_id
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.memory_client.aclose()

# FastAPI Application
def create_orchestrator_app(orchestrator: AdvancedOrchestrator) -> FastAPI:
    app = FastAPI(title="Agent Orchestration Module", version="1.0.0")
    
    @app.on_event("startup")
    async def startup():
        await orchestrator.initialize()
    
    @app.on_event("shutdown")
    async def shutdown():
        await orchestrator.cleanup()
    
    @app.post("/workflows/create", response_model=dict)
    async def create_workflow(request: WorkflowRequest):
        workflow_id = str(uuid.uuid4())
        
        agent_configs = []
        for agent_req in request.agents:
            agent_config = AgentConfig(
                name=agent_req.name,
                agent_type=AgentType(agent_req.agent_type),
                system_message=agent_req.system_message,
                tools=agent_req.tools,
                metadata=agent_req.metadata
            )
            agent_configs.append(agent_config)
        
        workflow_config = WorkflowConfig(
            workflow_id=workflow_id,
            name=request.name,
            agents=agent_configs,
            team_type=request.team_type,
            termination_conditions=request.termination_conditions,
            max_turns=request.max_turns,
            metadata=request.metadata
        )
        
        await orchestrator.workflow_manager.register_workflow(workflow_config)
        
        return {"workflow_id": workflow_id, "status": "created"}
    
    @app.post("/workflows/{workflow_id}/execute", response_model=ExecutionResponse)
    async def execute_workflow(
        workflow_id: str,
        request: dict,
        background_tasks: BackgroundTasks
    ):
        initial_message = request.get("initial_message", "")
        context = request.get("context", {})
        
        execution_id = await orchestrator.workflow_manager.execute_workflow(
            workflow_id, initial_message, context
        )
        
        execution = await orchestrator.workflow_manager.get_execution_status(execution_id)
        
        return ExecutionResponse(
            execution_id=execution.execution_id,
            workflow_id=execution.workflow_id,
            status=execution.status.value,
            created_at=execution.created_at
        )
    
    @app.get("/executions/{execution_id}", response_model=ExecutionResponse)
    async def get_execution_status(execution_id: str):
        execution = await orchestrator.workflow_manager.get_execution_status(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        return ExecutionResponse(
            execution_id=execution.execution_id,
            workflow_id=execution.workflow_id,
            status=execution.status.value,
            created_at=execution.created_at,
            result=execution.result
        )
    
    @app.post("/executions/{execution_id}/pause")
    async def pause_execution(execution_id: str):
        success = await orchestrator.workflow_manager.pause_execution(execution_id)
        return {"success": success}
    
    @app.post("/executions/{execution_id}/resume")
    async def resume_execution(execution_id: str):
        success = await orchestrator.workflow_manager.resume_execution(execution_id)
        return {"success": success}
    
    @app.post("/executions/{execution_id}/cancel")
    async def cancel_execution(execution_id: str):
        success = await orchestrator.workflow_manager.cancel_execution(execution_id)
        return {"success": success}
    
    @app.post("/research/create", response_model=dict)
    async def create_research_workflow(request: dict):
        research_topic = request.get("topic", "")
        workflow_id = await orchestrator.create_research_workflow(research_topic)
        return {"workflow_id": workflow_id, "topic": research_topic}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
    
    return app

# Main execution
if __name__ == "__main__":
    orchestrator = AdvancedOrchestrator()
    app = create_orchestrator_app(orchestrator)
    
    uvicorn.run(app, host="0.0.0.0", port=8002)
import asyncio
from typing import Dict, Any, List, Optional
import re
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .content_processor import ContentProcessor, ProcessedContent, ContentMetadata, ContentType


class TechnicalContentProcessor(ContentProcessor):
    """Specialized processor for technical content"""

    def __init__(self):
        self.name = "TechnicalContentProcessor"

        # Technical vocabularies
        self.programming_languages = [
            "python", "javascript", "java", "c++", "c#", "go", "rust", "typescript",
            "php", "ruby", "swift", "kotlin", "scala", "r", "matlab", "julia"
        ]

        self.frameworks = [
            "react", "vue", "angular", "django", "flask", "fastapi", "express",
            "spring", "laravel", "rails", "tensorflow", "pytorch", "autogen"
        ]

        self.technical_concepts = [
            "api", "rest", "graphql", "microservices", "docker", "kubernetes",
            "machine learning", "artificial intelligence", "blockchain", "devops",
            "ci/cd", "database", "sql", "nosql", "cloud computing", "aws", "azure"
        ]

    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process technical content with specialized analysis"""

        processing_steps = []

        # Clean technical content
        cleaned_content = await self._clean_technical_content(content)
        processing_steps.append("technical_content_cleaning")

        # Extract technical entities
        tech_entities = await self._extract_technical_entities(cleaned_content)
        processing_steps.append("technical_entity_extraction")

        # Identify code blocks and technical patterns
        code_analysis = await self._analyze_code_blocks(cleaned_content)
        processing_steps.append("code_block_analysis")

        # Extract technical key points
        tech_key_points = await self._extract_technical_key_points(cleaned_content)
        processing_steps.append("technical_key_point_extraction")

        # Generate technical summary
        tech_summary = await self._generate_technical_summary(cleaned_content, tech_entities)
        processing_steps.append("technical_summarization")

        # Extract technical citations and references
        tech_citations = await self._extract_technical_references(cleaned_content)
        processing_steps.append("technical_citation_extraction")

        # Calculate technical quality score
        quality_score = await self._calculate_technical_quality_score(
            cleaned_content, tech_entities, code_analysis, tech_key_points
        )

        # Merge with base entities
        all_entities = {
            **tech_entities,
            "code_blocks": code_analysis.get("code_blocks", []),
            "technical_patterns": code_analysis.get("patterns", [])
        }

        return ProcessedContent(
            original_content=content,
            processed_content=cleaned_content,
            metadata=metadata,
            quality_score=quality_score,
            processing_steps=processing_steps,
            extracted_entities=all_entities,
            key_points=tech_key_points,
            summary=tech_summary,
            citations=tech_citations
        )

    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type in [ContentType.TEXT, ContentType.MARKDOWN, ContentType.CODE]

    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name

    async def _clean_technical_content(self, content: str) -> str:
        """Clean technical content preserving code structure"""

        # Preserve code blocks (markdown style)
        code_blocks = []
        code_pattern = r'```[\s\S]*?```'

        def replace_code_block(match):
            code_blocks.append(match.group(0))
            return f"__CODE_BLOCK_{len(code_blocks)-1}__"

        content = re.sub(code_pattern, replace_code_block, content)

        # Basic cleaning
        cleaned = re.sub(r'\s+', ' ', content)
        cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')

        # Restore code blocks
        for i, code_block in enumerate(code_blocks):
            cleaned = cleaned.replace(f"__CODE_BLOCK_{i}__", code_block)

        return cleaned.strip()

    async def _extract_technical_entities(self, content: str) -> Dict[str, List[str]]:
        """Extract technical entities from content"""

        entities = {
            "programming_languages": [],
            "frameworks": [],
            "technical_concepts": [],
            "apis": [],
            "libraries": [],
            "tools": [],
            "version_numbers": [],
            "file_paths": []
        }

        content_lower = content.lower()

        # Programming languages
        for lang in self.programming_languages:
            if lang in content_lower:
                entities["programming_languages"].append(lang)

        # Frameworks
        for framework in self.frameworks:
            if framework in content_lower:
                entities["frameworks"].append(framework)

        # Technical concepts
        for concept in self.technical_concepts:
            if concept in content_lower:
                entities["technical_concepts"].append(concept)

        # APIs (simplified pattern)
        api_pattern = r'\b\w+\s*API\b'
        entities["apis"] = re.findall(api_pattern, content, re.IGNORECASE)

        # Version numbers
        version_pattern = r'\bv?\d+\.\d+(?:\.\d+)?(?:-\w+)?\b'
        entities["version_numbers"] = re.findall(version_pattern, content)

        # File paths
        file_path_pattern = r'[./~]?(?:[a-zA-Z0-9_-]+/)*[a-zA-Z0-9_-]+\.[a-zA-Z0-9]+\b'
        entities["file_paths"] = re.findall(file_path_pattern, content)

        # Remove duplicates and clean up
        for key, value_list in entities.items():
            entities[key] = list(set(value_list))

        return entities

    async def _analyze_code_blocks(self, content: str) -> Dict[str, Any]:
        """Analyze code blocks and technical patterns"""

        analysis = {
            "code_blocks": [],
            "patterns": [],
            "languages_detected": [],
            "complexity_indicators": []
        }

        # Extract markdown code blocks
        code_block_pattern = r'```(\w+)?\n([\s\S]*?)```'
        code_blocks = re.findall(code_block_pattern, content)

        for language, code in code_blocks:
            block_analysis = {
                "language": language or "unknown",
                "length": len(code),
                "lines": len(code.split('\n')),
                "contains_functions": "def " in code or "function " in code,
                "contains_classes": "class " in code,
                "contains_imports": "import " in code or "require(" in code
            }
            analysis["code_blocks"].append(block_analysis)

            if language:
                analysis["languages_detected"].append(language)

        # Detect inline code patterns
        inline_code_pattern = r'`([^`\n]+)`'
        inline_codes = re.findall(inline_code_pattern, content)

        # Technical patterns
        patterns = [
            ("function_calls", r'\w+\([^)]*\)'),
            ("variable_assignments", r'\w+\s*=\s*\w+'),
            ("urls", r'https?://[^\s<>"{}|\\^`[\]]+'),
            ("command_line", r'\$\s+\w+'),
            ("file_extensions", r'\.\w{2,4}\b')
        ]

        for pattern_name, pattern in patterns:
            matches = re.findall(pattern, content)
            if matches:
                analysis["patterns"].append({
                    "type": pattern_name,
                    "count": len(matches),
                    "examples": matches[:5]  # First 5 examples
                })

        return analysis

    async def _extract_technical_key_points(self, content: str) -> List[str]:
        """Extract technical key points"""

        key_points = []

        # Technical indicators
        technical_indicators = [
            "install", "configure", "setup", "implementation", "usage",
            "example", "tutorial", "guide", "steps", "requirements",
            "documentation", "api reference", "parameters", "returns",
            "note:", "important:", "warning:", "tip:", "prerequisite"
        ]

        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 15]

        for sentence in sentences:
            sentence_lower = sentence.lower()

            # Check for technical indicators
            if any(indicator in sentence_lower for indicator in technical_indicators):
                key_points.append(sentence.strip())

            # Check for code-related sentences
            if ('`' in sentence or 'code' in sentence_lower or
                    'function' in sentence_lower or 'method' in sentence_lower):
                key_points.append(sentence.strip())

        return key_points[:15]  # Limit to 15 key points

    async def _generate_technical_summary(self, content: str, entities: Dict[str, List[str]]) -> str:
        """Generate technical summary"""

        # Count technical elements
        languages = entities.get("programming_languages", [])
        frameworks = entities.get("frameworks", [])
        concepts = entities.get("technical_concepts", [])

        summary_parts = []

        # Technology stack summary
        if languages or frameworks:
            tech_stack = languages + frameworks
            if tech_stack:
                summary_parts.append(
                    f"Technologies discussed: {', '.join(tech_stack[:5])}")

        # Concept summary
        if concepts:
            summary_parts.append(
                f"Key concepts covered: {', '.join(concepts[:5])}")

        # Content type analysis
        has_code = "```" in content or "`" in content
        has_setup = any(word in content.lower()
                        for word in ["install", "setup", "configure"])
        has_tutorial = any(word in content.lower()
                           for word in ["tutorial", "guide", "example"])

        content_types = []
        if has_code:
            content_types.append("code examples")
        if has_setup:
            content_types.append("setup instructions")
        if has_tutorial:
            content_types.append("tutorial content")

        if content_types:
            summary_parts.append(
                f"Content includes: {', '.join(content_types)}")

        # Fallback to basic summary
        if not summary_parts:
            sentences = re.split(r'[.!?]+', content)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 20]
            if sentences:
                summary_parts.append(sentences[0])

        return ". ".join(summary_parts) if summary_parts else "Technical content analysis."

    async def _extract_technical_references(self, content: str) -> List[Dict[str, Any]]:
        """Extract technical references and citations"""

        references = []

        # Documentation URLs
        doc_url_pattern = r'https?://[^\s<>"{}|\\^`[\]]*(?:doc|api|guide|tutorial|readme)[^\s<>"{}|\\^`[\]]*'
        doc_urls = re.findall(doc_url_pattern, content, re.IGNORECASE)

        for url in doc_urls:
            references.append({
                "type": "documentation",
                "url": url,
                "extracted_at": datetime.utcnow().isoformat()
            })

        # GitHub repositories
        github_pattern = r'https?://github\.com/[^\s<>"{}|\\^`[\]]+'
        github_urls = re.findall(github_pattern, content)

        for url in github_urls:
            references.append({
                "type": "repository",
                "url": url,
                "extracted_at": datetime.utcnow().isoformat()
            })

        # Package/library references
        package_patterns = [
            (r'npm install\s+([^\s\n]+)', "npm_package"),
            (r'pip install\s+([^\s\n]+)', "python_package"),
            (r'import\s+([^\s\n;]+)', "import_statement"),
            (r'from\s+([^\s\n;]+)\s+import', "python_import")
        ]

        for pattern, ref_type in package_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                references.append({
                    "type": ref_type,
                    "reference": match,
                    "extracted_at": datetime.utcnow().isoformat()
                })

        return references

    async def _calculate_technical_quality_score(
        self,
        content: str,
        entities: Dict[str, List[str]],
        code_analysis: Dict[str, Any],
        key_points: List[str]
    ) -> float:
        """Calculate technical content quality score"""

        score = 0.0

        # Content length score (0-0.2)
        content_length = len(content)
        if content_length > 2000:
            score += 0.2
        elif content_length > 1000:
            score += 0.15
        elif content_length > 500:
            score += 0.1

        # Technical entity richness (0-0.3)
        total_entities = sum(len(entity_list)
                             for entity_list in entities.values())
        if total_entities > 15:
            score += 0.3
        elif total_entities > 10:
            score += 0.2
        elif total_entities > 5:
            score += 0.1

        # Code content quality (0-0.3)
        code_blocks = code_analysis.get("code_blocks", [])
        if code_blocks:
            score += 0.2
            # Bonus for multiple languages
            languages = set(block.get("language", "") for block in code_blocks)
            if len(languages) > 1:
                score += 0.1

        # Structure and key points (0-0.2)
        if key_points:
            score += 0.1
            if len(key_points) > 5:
                score += 0.1

        return min(score, 1.0)


class VideoTranscriptProcessor(ContentProcessor):
    """Specialized processor for video transcript content"""

    def __init__(self):
        self.name = "VideoTranscriptProcessor"

    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process video transcript content"""

        processing_steps = []

        # Clean transcript content
        cleaned_content = await self._clean_transcript(content)
        processing_steps.append("transcript_cleaning")

        # Extract speaker information
        speaker_analysis = await self._analyze_speakers(cleaned_content)
        processing_steps.append("speaker_analysis")

        # Extract temporal information
        temporal_analysis = await self._extract_temporal_info(cleaned_content)
        processing_steps.append("temporal_analysis")

        # Extract video-specific entities
        video_entities = await self._extract_video_entities(cleaned_content)
        processing_steps.append("video_entity_extraction")

        # Generate video-specific key points
        video_key_points = await self._extract_video_key_points(cleaned_content)
        processing_steps.append("video_key_point_extraction")

        # Generate conversation summary
        video_summary = await self._generate_conversation_summary(cleaned_content, speaker_analysis)
        processing_steps.append("conversation_summarization")

        # Calculate video content quality
        quality_score = await self._calculate_video_quality_score(
            cleaned_content, speaker_analysis, temporal_analysis, video_entities
        )

        # Merge entities
        all_entities = {
            **video_entities,
            "speakers": speaker_analysis.get("speakers", []),
            "temporal_markers": temporal_analysis.get("markers", [])
        }

        return ProcessedContent(
            original_content=content,
            processed_content=cleaned_content,
            metadata=metadata,
            quality_score=quality_score,
            processing_steps=processing_steps,
            extracted_entities=all_entities,
            key_points=video_key_points,
            summary=video_summary,
            citations=[]  # Video transcripts typically don't have traditional citations
        )

    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type == ContentType.VIDEO_TRANSCRIPT

    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name

    async def _clean_transcript(self, content: str) -> str:
        """Clean transcript content"""

        # Remove timestamp markers like [00:01:23]
        cleaned = re.sub(r'\[\d{2}:\d{2}:\d{2}\]', '', content)

        # Remove speaker labels like "Speaker 1:" if they exist
        cleaned = re.sub(r'^[A-Za-z\s]+\d*:\s*', '',
                         cleaned, flags=re.MULTILINE)

        # Clean up excessive whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = cleaned.strip()

        return cleaned

    async def _analyze_speakers(self, content: str) -> Dict[str, Any]:
        """Analyze speaker patterns in transcript"""

        analysis = {
            "speakers": [],
            "speaker_changes": 0,
            "dialogue_segments": []
        }

        # Look for speaker indicators
        speaker_patterns = [
            r'^([A-Za-z\s]+\d*):\s*(.+)',  # "Speaker 1: text"
            r'\[([A-Za-z\s]+)\]:\s*(.+)',  # "[John]: text"
            r'>>([A-Za-z\s]+):\s*(.+)'     # ">>Host: text"
        ]

        speakers_found = set()
        segments = []

        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue

            for pattern in speaker_patterns:
                match = re.match(pattern, line)
                if match:
                    speaker = match.group(1).strip()
                    text = match.group(2).strip()

                    speakers_found.add(speaker)
                    segments.append({
                        "speaker": speaker,
                        "text": text
                    })
                    break

        analysis["speakers"] = list(speakers_found)
        analysis["speaker_changes"] = len(segments)
        analysis["dialogue_segments"] = segments

        return analysis

    async def _extract_temporal_info(self, content: str) -> Dict[str, Any]:
        """Extract temporal information from transcript"""

        temporal_info = {
            "markers": [],
            "duration_indicators": [],
            "time_references": []
        }

        # Extract timestamp markers
        timestamp_pattern = r'\[(\d{2}:\d{2}:\d{2})\]'
        timestamps = re.findall(timestamp_pattern, content)
        temporal_info["markers"] = timestamps

        # Extract duration indicators
        duration_patterns = [
            r'(\d+)\s*minutes?',
            r'(\d+)\s*seconds?',
            r'(\d+)\s*hours?'
        ]

        for pattern in duration_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            temporal_info["duration_indicators"].extend(matches)

        # Extract time references
        time_ref_patterns = [
            r'at\s+(\d{1,2}:\d{2})',
            r'around\s+(\d{1,2}:\d{2})',
            r'(\d{1,2})\s*minutes?\s+in'
        ]

        for pattern in time_ref_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            temporal_info["time_references"].extend(matches)

        return temporal_info

    async def _extract_video_entities(self, content: str) -> Dict[str, List[str]]:
        """Extract video-specific entities"""

        entities = {
            "topics_discussed": [],
            "questions_asked": [],
            "action_items": [],
            "demonstrations": [],
            "references_mentioned": []
        }

        # Extract questions
        question_patterns = [
            r'[?]',
            r'what\s+(?:is|are|do|does)',
            r'how\s+(?:do|does|can|to)',
            r'why\s+(?:is|are|do|does)',
            r'when\s+(?:is|are|do|does)',
            r'where\s+(?:is|are|do|does)'
        ]

        sentences = re.split(r'[.!?]+', content)
        for sentence in sentences:
            sentence = sentence.strip()
            if any(re.search(pattern, sentence, re.IGNORECASE) for pattern in question_patterns):
                entities["questions_asked"].append(sentence)

        # Extract action items
        action_indicators = [
            "let's", "we need to", "you should", "make sure to",
            "remember to", "don't forget", "action item", "todo"
        ]

        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(indicator in sentence_lower for indicator in action_indicators):
                entities["action_items"].append(sentence.strip())

        # Extract demonstrations
        demo_indicators = [
            "let me show", "here's how", "demonstration", "example",
            "as you can see", "look at this", "watch this"
        ]

        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(indicator in sentence_lower for indicator in demo_indicators):
                entities["demonstrations"].append(sentence.strip())

        return entities

    async def _extract_video_key_points(self, content: str) -> List[str]:
        """Extract key points from video transcript"""

        key_points = []

        # Video-specific indicators
        video_indicators = [
            "important", "key point", "remember", "main thing",
            "takeaway", "summary", "conclusion", "in summary",
            "to recap", "the point is", "what matters"
        ]

        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(indicator in sentence_lower for indicator in video_indicators):
                key_points.append(sentence.strip())

        return key_points[:10]  # Limit to 10 key points

    async def _generate_conversation_summary(self, content: str, speaker_analysis: Dict[str, Any]) -> str:
        """Generate conversation summary"""

        speakers = speaker_analysis.get("speakers", [])
        segments = speaker_analysis.get("dialogue_segments", [])

        summary_parts = []

        # Speaker summary
        if speakers:
            if len(speakers) == 1:
                summary_parts.append(f"Monologue by {speakers[0]}")
            else:
                summary_parts.append(
                    f"Conversation between {', '.join(speakers)}")

        # Content summary
        if segments:
            # Get main topics from first few segments
            main_topics = []
            for segment in segments[:3]:
                text = segment.get("text", "")
                if len(text) > 20:
                    main_topics.append(
                        text[:100] + "..." if len(text) > 100 else text)

            if main_topics:
                summary_parts.append(
                    f"Discussion covers: {'; '.join(main_topics)}")

        # Fallback summary
        if not summary_parts:
            sentences = re.split(r'[.!?]+', content)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 20]
            if sentences:
                summary_parts.append(
                    sentences[0][:200] + "..." if len(sentences[0]) > 200 else sentences[0])

        return ". ".join(summary_parts) if summary_parts else "Video transcript content."

    async def _calculate_video_quality_score(
        self,
        content: str,
        speaker_analysis: Dict[str, Any],
        temporal_analysis: Dict[str, Any],
        video_entities: Dict[str, List[str]]
    ) -> float:
        """Calculate video transcript quality score"""

        score = 0.0

        # Content length score (0-0.3)
        content_length = len(content)
        if content_length > 3000:
            score += 0.3
        elif content_length > 1500:
            score += 0.2
        elif content_length > 500:
            score += 0.1

        # Speaker analysis score (0-0.3)
        speakers = speaker_analysis.get("speakers", [])
        segments = speaker_analysis.get("dialogue_segments", [])

        if speakers:
            score += 0.1
            if len(speakers) > 1:  # Multi-speaker conversation
                score += 0.1
            if len(segments) > 5:  # Good dialogue flow
                score += 0.1

        # Temporal structure score (0-0.2)
        markers = temporal_analysis.get("markers", [])
        if markers:
            score += 0.1
            if len(markers) > 3:
                score += 0.1

        # Content richness score (0-0.2)
        total_entities = sum(len(entity_list)
                             for entity_list in video_entities.values())
        if total_entities > 5:
            score += 0.1
            if total_entities > 10:
                score += 0.1

        return min(score, 1.0)

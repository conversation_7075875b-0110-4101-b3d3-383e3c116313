import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime

from .processors.content_processor import ContentProcessor, ProcessedContent, ContentMetadata, ContentType
from .processors.text_processor import TextContentProcessor
from .processors.html_processor import HTMLContentProcessor

class ContentProcessingPipeline:
    """Content processing pipeline with multiple processors"""
    
    def __init__(self):
        self.processors = [
            TextContentProcessor(),
            HTMLContentProcessor()
        ]
        
        # Processing statistics
        self.processing_stats = {
            "total_processed": 0,
            "processing_times": [],
            "quality_scores": []
        }
    
    async def process_content(
        self, 
        content: str, 
        content_type: ContentType,
        source_url: str,
        source_type: str
    ) -> ProcessedContent:
        """Process content through appropriate processor"""
        
        start_time = datetime.utcnow()
        
        # Create metadata
        metadata = ContentMetadata(
            content_type=content_type,
            source_url=source_url,
            source_type=source_type,
            extracted_at=start_time,
            content_hash=hashlib.md5(content.encode()).hexdigest(),
            language="en",  # Simplified for Sprint 2
            encoding="utf-8",
            size_bytes=len(content.encode())
        )
        
        # Find appropriate processor
        processor = self._find_processor(content_type)
        
        if not processor:
            raise Exception(f"No processor available for content type: {content_type}")
        
        # Process content
        try:
            result = await processor.process(content, metadata)
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self.processing_stats["total_processed"] += 1
            self.processing_stats["processing_times"].append(processing_time)
            self.processing_stats["quality_scores"].append(result.quality_score)
            
            return result
            
        except Exception as e:
            raise Exception(f"Content processing failed: {str(e)}")
    
    def _find_processor(self, content_type: ContentType) -> Optional[ContentProcessor]:
        """Find processor for content type"""
        
        for processor in self.processors:
            if processor.can_process(content_type):
                return processor
        
        return None
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing pipeline statistics"""
        
        stats = self.processing_stats.copy()
        
        if stats["processing_times"]:
            stats["average_processing_time"] = sum(stats["processing_times"]) / len(stats["processing_times"])
            stats["max_processing_time"] = max(stats["processing_times"])
        
        if stats["quality_scores"]:
            stats["average_quality_score"] = sum(stats["quality_scores"]) / len(stats["quality_scores"])
            stats["min_quality_score"] = min(stats["quality_scores"])
            stats["max_quality_score"] = max(stats["quality_scores"])
        
        return stats
    
    def add_processor(self, processor: ContentProcessor):
        """Add new processor to pipeline"""
        self.processors.append(processor)
    
    def register_processor(self, processor: ContentProcessor):
        """Register new processor to pipeline (alias for add_processor)"""
        self.add_processor(processor)
    
    def get_processor(self, content_type: ContentType) -> Optional[ContentProcessor]:
        """Get processor for content type"""
        return self._find_processor(content_type)
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline statistics (alias for get_processing_statistics)"""
        stats = self.get_processing_statistics()
        stats["total_processors"] = len(self.processors)
        return stats
    
    def list_supported_content_types(self) -> List[ContentType]:
        """List all supported content types"""
        
        supported_types = set()
        for processor in self.processors:
            for content_type in ContentType:
                if processor.can_process(content_type):
                    supported_types.add(content_type)
        
        return list(supported_types)

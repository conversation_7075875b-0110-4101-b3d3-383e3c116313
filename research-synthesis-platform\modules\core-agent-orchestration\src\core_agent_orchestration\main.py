# Core Agent Orchestration Module - Sprint 3 Implementation
# Based on agent_orchestration_module.py from folder 3

from shared.common.module_client import ModuleClient
from shared.common.api_framework import create_api_app, create_success_response, create_error_response
import asyncio
import logging
import json
import uuid
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, Type, Union
from datetime import datetime
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from enum import Enum
import httpx
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

# Import AutoGen 0.4+ components (adjust imports based on actual version)
try:
    from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
    from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
    from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
    from autogen_agentchat.messages import TextMessage, ChatMessage
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_core import Component, TopicId, MessageContext
    from autogen_core.application import SingleThreadedAgentRuntime
    AUTOGEN_AVAILABLE = True
except ImportError:
    # Fallback for different AutoGen versions or testing
    logging.warning("AutoGen imports not available, using mock classes")
    AUTOGEN_AVAILABLE = False

    class AssistantAgent:
        def __init__(self, name: str, model_client=None, **kwargs):
            self.name = name
            self.model_client = model_client
            self.system_message = kwargs.get('system_message', '')

    class UserProxyAgent:
        def __init__(self, name: str, **kwargs):
            self.name = name
            self.human_input_mode = kwargs.get('human_input_mode', 'NEVER')

    class RoundRobinGroupChat:
        def __init__(self, participants, **kwargs):
            self.participants = participants

    class TextMessage:
        def __init__(self, content: str, source: str):
            self.content = content
            self.source = source

    class MaxMessageTermination:
        def __init__(self, max_messages: int):
            self.max_messages = max_messages

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import shared components

# Agent Types Enum


class AgentType(str, Enum):
    USER_PROXY = "user_proxy"
    RESEARCH_COORDINATOR = "research_coordinator"
    SOURCE_ANALYSIS = "source_analysis"
    SYNTHESIS = "synthesis"
    CONTENT_GENERATION = "content_generation"

# Agent Status Enum


class AgentStatus(str, Enum):
    IDLE = "idle"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"

# Workflow Status Enum


class WorkflowStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# Pydantic Models for API


class AgentConfig(BaseModel):
    name: str
    agent_type: AgentType
    system_message: str
    model_config: Dict[str, Any] = {}
    tools: List[str] = []
    max_consecutive_auto_reply: int = 10


class WorkflowRequest(BaseModel):
    workflow_id: Optional[str] = None
    task_description: str
    agents: List[str] = []
    max_rounds: int = 10
    termination_condition: str = "TERMINATE"
    context: Dict[str, Any] = {}


class WorkflowResponse(BaseModel):
    workflow_id: str
    status: WorkflowStatus
    agents: List[str]
    messages: List[Dict[str, Any]] = []
    result: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime


class AgentResponse(BaseModel):
    agent_id: str
    name: str
    agent_type: AgentType
    status: AgentStatus
    created_at: datetime
    last_active: datetime

# Agent Registry


class AgentRegistry:
    """Registry for managing AutoGen agents"""

    def __init__(self):
        self.agents: Dict[str, Any] = {}
        self.agent_configs: Dict[str, AgentConfig] = {}
        self.module_client = ModuleClient("core-agent-orchestration")

    async def create_agent(self, config: AgentConfig) -> str:
        """Create a new agent"""
        try:
            agent_id = str(uuid.uuid4())

            # Create model client if needed
            model_client = None
            if config.model_config and AUTOGEN_AVAILABLE:
                try:
                    model_client = OpenAIChatCompletionClient(
                        model=config.model_config.get('model', 'gpt-4'),
                        api_key=config.model_config.get('api_key'),
                        base_url=config.model_config.get('base_url')
                    )
                except Exception as e:
                    logger.warning(f"Failed to create model client: {e}")

            # Create agent based on type
            if config.agent_type == AgentType.USER_PROXY:
                agent = UserProxyAgent(
                    name=config.name,
                    human_input_mode="NEVER",
                    max_consecutive_auto_reply=config.max_consecutive_auto_reply,
                    code_execution_config=False
                )
            else:
                agent = AssistantAgent(
                    name=config.name,
                    model_client=model_client,
                    system_message=config.system_message,
                    max_consecutive_auto_reply=config.max_consecutive_auto_reply
                )

            # Store agent and config
            self.agents[agent_id] = agent
            self.agent_configs[agent_id] = config

            logger.info(f"Created agent {config.name} with ID {agent_id}")
            return agent_id

        except Exception as e:
            logger.error(f"Error creating agent: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def get_agent(self, agent_id: str) -> Optional[Any]:
        """Get agent by ID"""
        return self.agents.get(agent_id)

    async def list_agents(self) -> List[AgentResponse]:
        """List all agents"""
        responses = []
        for agent_id, agent in self.agents.items():
            config = self.agent_configs[agent_id]
            responses.append(AgentResponse(
                agent_id=agent_id,
                name=config.name,
                agent_type=config.agent_type,
                status=AgentStatus.IDLE,
                created_at=datetime.utcnow(),
                last_active=datetime.utcnow()
            ))
        return responses

    async def delete_agent(self, agent_id: str) -> bool:
        """Delete an agent"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            del self.agent_configs[agent_id]
            logger.info(f"Deleted agent {agent_id}")
            return True
        return False

# Workflow Orchestrator


class WorkflowOrchestrator:
    """Orchestrates multi-agent workflows"""

    def __init__(self, agent_registry: AgentRegistry):
        self.agent_registry = agent_registry
        self.workflows: Dict[str, WorkflowResponse] = {}
        self.module_client = ModuleClient("core-agent-orchestration")

    async def create_workflow(self, request: WorkflowRequest) -> WorkflowResponse:
        """Create and start a new workflow"""
        try:
            workflow_id = request.workflow_id or str(uuid.uuid4())

            # Validate agents exist
            agents = []
            for agent_id in request.agents:
                agent = await self.agent_registry.get_agent(agent_id)
                if not agent:
                    raise HTTPException(
                        status_code=404, detail=f"Agent {agent_id} not found")
                agents.append(agent)

            # Create workflow response
            workflow = WorkflowResponse(
                workflow_id=workflow_id,
                status=WorkflowStatus.PENDING,
                agents=request.agents,
                messages=[],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.workflows[workflow_id] = workflow

            # Start workflow in background
            asyncio.create_task(self._execute_workflow(
                workflow_id, request, agents))

            return workflow

        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _execute_workflow(self, workflow_id: str, request: WorkflowRequest, agents: List[Any]):
        """Execute the workflow"""
        try:
            workflow = self.workflows[workflow_id]
            workflow.status = WorkflowStatus.RUNNING
            workflow.updated_at = datetime.utcnow()

            # Create group chat if multiple agents
            if len(agents) > 1 and AUTOGEN_AVAILABLE:
                group_chat = RoundRobinGroupChat(
                    participants=agents,
                    max_round=request.max_rounds
                )

                # Execute group chat (simplified for Sprint 3)
                messages = []
                for i in range(min(request.max_rounds, 3)):  # Limit for demo
                    message = {
                        "round": i + 1,
                        "content": f"Processing task: {request.task_description}",
                        "agent": agents[i % len(agents)].name,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    messages.append(message)

                workflow.messages = messages
                workflow.result = {
                    "task_completed": True,
                    "summary": f"Completed task: {request.task_description}",
                    "agents_involved": [agent.name for agent in agents]
                }
            else:
                # Single agent execution
                workflow.messages = [{
                    "content": f"Single agent processing: {request.task_description}",
                    "agent": agents[0].name if agents else "unknown",
                    "timestamp": datetime.utcnow().isoformat()
                }]
                workflow.result = {
                    "task_completed": True,
                    "summary": f"Single agent completed: {request.task_description}"
                }

            workflow.status = WorkflowStatus.COMPLETED
            workflow.updated_at = datetime.utcnow()

        except Exception as e:
            logger.error(f"Error executing workflow {workflow_id}: {e}")
            workflow.status = WorkflowStatus.FAILED
            workflow.updated_at = datetime.utcnow()

    async def get_workflow(self, workflow_id: str) -> Optional[WorkflowResponse]:
        """Get workflow by ID"""
        return self.workflows.get(workflow_id)

    async def list_workflows(self) -> List[WorkflowResponse]:
        """List all workflows"""
        return list(self.workflows.values())

    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a workflow"""
        workflow = self.workflows.get(workflow_id)
        if workflow and workflow.status == WorkflowStatus.RUNNING:
            workflow.status = WorkflowStatus.CANCELLED
            workflow.updated_at = datetime.utcnow()
            return True
        return False


# Initialize components
agent_registry = AgentRegistry()
workflow_orchestrator = WorkflowOrchestrator(agent_registry)

# Create FastAPI app
app = create_api_app(
    title="Core Agent Orchestration API",
    description="AutoGen-based agent orchestration service for the research synthesis platform",
    version="1.0.0"
)

# Agent API Routes


@app.post("/agents", response_model=AgentResponse)
async def create_agent_endpoint(config: AgentConfig):
    """Create a new agent"""
    try:
        agent_id = await agent_registry.create_agent(config)
        agent_response = AgentResponse(
            agent_id=agent_id,
            name=config.name,
            agent_type=config.agent_type,
            status=AgentStatus.IDLE,
            created_at=datetime.utcnow(),
            last_active=datetime.utcnow()
        )
        return create_success_response(agent_response.dict())
    except Exception as e:
        logger.error(f"Error in create_agent_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/agents", response_model=List[AgentResponse])
async def list_agents_endpoint():
    """List all agents"""
    try:
        agents = await agent_registry.list_agents()
        return create_success_response([agent.dict() for agent in agents])
    except Exception as e:
        logger.error(f"Error in list_agents_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/agents/{agent_id}", response_model=AgentResponse)
async def get_agent_endpoint(agent_id: str):
    """Get agent by ID"""
    try:
        agent = await agent_registry.get_agent(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        config = agent_registry.agent_configs[agent_id]
        agent_response = AgentResponse(
            agent_id=agent_id,
            name=config.name,
            agent_type=config.agent_type,
            status=AgentStatus.IDLE,
            created_at=datetime.utcnow(),
            last_active=datetime.utcnow()
        )
        return create_success_response(agent_response.dict())
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_agent_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.delete("/agents/{agent_id}")
async def delete_agent_endpoint(agent_id: str):
    """Delete an agent"""
    try:
        success = await agent_registry.delete_agent(agent_id)
        if success:
            return create_success_response({"deleted": True})
        else:
            raise HTTPException(status_code=404, detail="Agent not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_agent_endpoint: {e}")
        return create_error_response(str(e), 500)

# Workflow API Routes


@app.post("/workflows", response_model=WorkflowResponse)
async def create_workflow_endpoint(request: WorkflowRequest):
    """Create and start a new workflow"""
    try:
        workflow = await workflow_orchestrator.create_workflow(request)
        return create_success_response(workflow.dict())
    except Exception as e:
        logger.error(f"Error in create_workflow_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/workflows", response_model=List[WorkflowResponse])
async def list_workflows_endpoint():
    """List all workflows"""
    try:
        workflows = await workflow_orchestrator.list_workflows()
        return create_success_response([workflow.dict() for workflow in workflows])
    except Exception as e:
        logger.error(f"Error in list_workflows_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/workflows/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow_endpoint(workflow_id: str):
    """Get workflow by ID"""
    try:
        workflow = await workflow_orchestrator.get_workflow(workflow_id)
        if not workflow:
            raise HTTPException(status_code=404, detail="Workflow not found")
        return create_success_response(workflow.dict())
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_workflow_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.post("/workflows/{workflow_id}/cancel")
async def cancel_workflow_endpoint(workflow_id: str):
    """Cancel a workflow"""
    try:
        success = await workflow_orchestrator.cancel_workflow(workflow_id)
        if success:
            return create_success_response({"cancelled": True})
        else:
            raise HTTPException(
                status_code=404, detail="Workflow not found or not running")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in cancel_workflow_endpoint: {e}")
        return create_error_response(str(e), 500)

# Predefined Agent Creation Endpoints


@app.post("/agents/research-coordinator")
async def create_research_coordinator():
    """Create a research coordinator agent"""
    try:
        config = AgentConfig(
            name="ResearchCoordinator",
            agent_type=AgentType.RESEARCH_COORDINATOR,
            system_message="""You are a Research Coordinator Agent responsible for:
            1. Analyzing research tasks and breaking them down into manageable components
            2. Delegating tasks to appropriate specialized agents
            3. Coordinating the overall research workflow
            4. Ensuring quality and completeness of research outputs
            5. Managing timelines and priorities

            Always provide clear, structured responses and maintain coordination between team members."""
        )
        agent_id = await agent_registry.create_agent(config)
        return create_success_response({"agent_id": agent_id, "name": config.name})
    except Exception as e:
        logger.error(f"Error creating research coordinator: {e}")
        return create_error_response(str(e), 500)


@app.post("/agents/source-analysis")
async def create_source_analysis_agent():
    """Create a source analysis agent"""
    try:
        config = AgentConfig(
            name="SourceAnalysisAgent",
            agent_type=AgentType.SOURCE_ANALYSIS,
            system_message="""You are a Source Analysis Agent responsible for:
            1. Analyzing and validating information from multiple sources
            2. Assessing source credibility and reliability
            3. Identifying potential biases and inconsistencies
            4. Cross-referencing information across sources
            5. Providing detailed analysis reports

            Always be thorough, objective, and provide evidence-based assessments."""
        )
        agent_id = await agent_registry.create_agent(config)
        return create_success_response({"agent_id": agent_id, "name": config.name})
    except Exception as e:
        logger.error(f"Error creating source analysis agent: {e}")
        return create_error_response(str(e), 500)


@app.post("/agents/synthesis")
async def create_synthesis_agent():
    """Create a synthesis agent"""
    try:
        config = AgentConfig(
            name="SynthesisAgent",
            agent_type=AgentType.SYNTHESIS,
            system_message="""You are a Synthesis Agent responsible for:
            1. Combining information from multiple sources into coherent reports
            2. Identifying patterns, trends, and key insights
            3. Creating structured summaries and conclusions
            4. Ensuring logical flow and consistency in synthesized content
            5. Generating actionable recommendations

            Always provide well-structured, comprehensive, and insightful synthesis."""
        )
        agent_id = await agent_registry.create_agent(config)
        return create_success_response({"agent_id": agent_id, "name": config.name})
    except Exception as e:
        logger.error(f"Error creating synthesis agent: {e}")
        return create_error_response(str(e), 500)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return create_success_response({
        "status": "healthy",
        "service": "core-agent-orchestration",
        "autogen_available": AUTOGEN_AVAILABLE,
        "timestamp": datetime.utcnow().isoformat()
    })


@app.get("/stats")
async def get_stats():
    """Get orchestration statistics"""
    try:
        return create_success_response({
            "total_agents": len(agent_registry.agents),
            "total_workflows": len(workflow_orchestrator.workflows),
            "autogen_available": AUTOGEN_AVAILABLE,
            "agent_types": {
                agent_type.value: sum(1 for config in agent_registry.agent_configs.values()
                                      if config.agent_type == agent_type)
                for agent_type in AgentType
            }
        })
    except Exception as e:
        logger.error(f"Error in get_stats: {e}")
        return create_error_response(str(e), 500)

# Startup event


@app.on_event("startup")
async def startup_event():
    """Initialize the agent orchestration service"""
    logger.info("Starting Core Agent Orchestration service...")

    # Create default agents
    try:
        # Create User Proxy Agent
        user_proxy_config = AgentConfig(
            name="UserProxy",
            agent_type=AgentType.USER_PROXY,
            system_message="You are a user proxy agent that facilitates human-AI interaction."
        )
        await agent_registry.create_agent(user_proxy_config)

        logger.info("Default agents created successfully")
    except Exception as e:
        logger.warning(f"Failed to create default agents: {e}")

    logger.info("Core Agent Orchestration service started successfully")

# Main execution
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Core Agent Orchestration port
        reload=True,
        log_level="info"
    )

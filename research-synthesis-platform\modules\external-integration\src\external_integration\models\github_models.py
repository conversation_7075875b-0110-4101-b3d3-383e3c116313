from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import httpx
import asyncio
import base64
import json
import re
from dataclasses import dataclass
from enum import Enum

class ContentType(str, Enum):
    README = "readme"
    CODE = "code"
    DOCUMENTATION = "documentation"
    ISSUES = "issues"
    DISCUSSIONS = "discussions"
    RELEASES = "releases"
    WIKI = "wiki"

@dataclass
class GitHubRepository:
    owner: str
    name: str
    full_name: str
    description: str
    language: str
    stars: int
    forks: int
    size: int
    created_at: datetime
    updated_at: datetime
    topics: List[str]
    license: Optional[str]
    default_branch: str

@dataclass
class RepositoryContent:
    content_type: ContentType
    path: str
    content: str
    size: int
    encoding: str
    url: str
    extracted_at: datetime

# Research Synthesis Module - Sprint 3 Implementation
# Based on research_synthesis_module.py from folder 3

from shared.common.module_client import ModuleClient
from shared.common.api_framework import create_api_app, create_success_response, create_error_response
import asyncio
import logging
import json
import uuid
import hashlib
import re
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict, field
from abc import ABC, abstractmethod
from enum import Enum
import httpx
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, validator
import uvicorn
from urllib.parse import urlparse
import concurrent.futures
from collections import Counter, defaultdict

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

# Optional dependencies with fallbacks
try:
    import aiofiles
except ImportError:
    aiofiles = None
    logging.warning("aiofiles not available")

try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available, using basic similarity")

try:
    from textstat import flesch_reading_ease, flesch_kincaid_grade
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False
    logging.warning("textstat not available")

try:
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    from nltk.tokenize import sent_tokenize, word_tokenize
    from nltk.corpus import stopwords
    NLTK_AVAILABLE = True

    # Download required NLTK data
    try:
        nltk.download('vader_lexicon', quiet=True)
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
    except:
        logger.warning("Could not download NLTK data")
except ImportError:
    NLTK_AVAILABLE = False
    logging.warning("NLTK not available")

try:
    import spacy
    nlp = spacy.load("en_core_web_sm")
    SPACY_AVAILABLE = True
except (ImportError, OSError):
    SPACY_AVAILABLE = False
    nlp = None
    logging.warning(
        "spaCy not available. Install with: python -m spacy download en_core_web_sm")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import shared components

# Research Status Enum


class ResearchStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    ANALYZING = "analyzing"
    SYNTHESIZING = "synthesizing"
    COMPLETED = "completed"
    FAILED = "failed"

# Source Type Enum


class SourceType(str, Enum):
    GITHUB = "github"
    YOUTUBE = "youtube"
    WEB_SEARCH = "web_search"
    DOCUMENT = "document"
    API = "api"

# Quality Level Enum


class QualityLevel(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"

# Pydantic Models


@dataclass
class SourceInfo:
    """Information about a research source"""
    source_id: str
    source_type: SourceType
    url: str
    title: str
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    credibility_score: float = 0.0
    relevance_score: float = 0.0
    quality_level: QualityLevel = QualityLevel.UNKNOWN
    extracted_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ResearchInsight:
    """A key insight extracted from research"""
    insight_id: str
    content: str
    sources: List[str]  # source_ids
    confidence: float
    category: str
    supporting_evidence: List[str] = field(default_factory=list)
    contradicting_evidence: List[str] = field(default_factory=list)


@dataclass
class ResearchGap:
    """An identified gap in research"""
    gap_id: str
    description: str
    suggested_sources: List[str]
    priority: str  # high, medium, low
    research_questions: List[str] = field(default_factory=list)


class ResearchRequest(BaseModel):
    research_id: Optional[str] = None
    topic: str
    sources: List[Dict[str, Any]] = []
    requirements: Dict[str, Any] = {}
    max_sources: int = 10
    quality_threshold: float = 0.5


class ResearchResponse(BaseModel):
    research_id: str
    topic: str
    status: ResearchStatus
    sources_analyzed: int
    insights: List[Dict[str, Any]] = []
    synthesis: Optional[Dict[str, Any]] = None
    gaps: List[Dict[str, Any]] = []
    quality_metrics: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime

# Source Analysis Engine


class SourceAnalyzer:
    """Analyzes individual sources for credibility and relevance"""

    def __init__(self):
        self.module_client = ModuleClient("research-synthesis")
        if NLTK_AVAILABLE:
            try:
                self.sentiment_analyzer = SentimentIntensityAnalyzer()
            except:
                self.sentiment_analyzer = None
        else:
            self.sentiment_analyzer = None

    async def analyze_source(self, source: SourceInfo) -> SourceInfo:
        """Analyze a source for credibility and relevance"""
        try:
            # Calculate credibility score
            source.credibility_score = await self._calculate_credibility(source)

            # Calculate relevance score (simplified)
            source.relevance_score = await self._calculate_relevance(source)

            # Determine quality level
            source.quality_level = self._determine_quality_level(
                source.credibility_score, source.relevance_score
            )

            return source

        except Exception as e:
            logger.error(f"Error analyzing source {source.source_id}: {e}")
            return source

    async def _calculate_credibility(self, source: SourceInfo) -> float:
        """Calculate credibility score for a source"""
        score = 0.5  # Base score

        # URL-based credibility indicators
        if source.url:
            parsed_url = urlparse(source.url)
            domain = parsed_url.netloc.lower()

            # Academic and government domains get higher scores
            if any(tld in domain for tld in ['.edu', '.gov', '.org']):
                score += 0.2

            # Well-known reliable sources
            reliable_domains = ['github.com',
                                'stackoverflow.com', 'wikipedia.org']
            if any(reliable in domain for reliable in reliable_domains):
                score += 0.15

        # Content-based indicators
        if source.content:
            content_lower = source.content.lower()

            # Check for citations and references
            if re.search(r'\[?\d+\]?|doi:|arxiv:|pmid:', content_lower):
                score += 0.1

            # Check for structured content
            if any(indicator in content_lower for indicator in ['abstract:', 'introduction:', 'methodology:', 'conclusion:']):
                score += 0.1

        return min(score, 1.0)

    async def _calculate_relevance(self, source: SourceInfo) -> float:
        """Calculate relevance score (simplified implementation)"""
        # This would typically use more sophisticated NLP
        # For now, using basic keyword matching
        if not source.content:
            return 0.0

        # Simple relevance based on content length and structure
        content_length = len(source.content)
        if content_length > 1000:
            return 0.8
        elif content_length > 500:
            return 0.6
        else:
            return 0.4

    def _determine_quality_level(self, credibility: float, relevance: float) -> QualityLevel:
        """Determine overall quality level"""
        avg_score = (credibility + relevance) / 2

        if avg_score >= 0.7:
            return QualityLevel.HIGH
        elif avg_score >= 0.5:
            return QualityLevel.MEDIUM
        else:
            return QualityLevel.LOW

# Cross-Source Validator


class CrossSourceValidator:
    """Validates information across multiple sources"""

    def __init__(self):
        self.module_client = ModuleClient("research-synthesis")

    async def validate_across_sources(self, sources: List[SourceInfo]) -> Dict[str, Any]:
        """Validate information consistency across sources"""
        try:
            validation_results = {
                'consistency_score': 0.0,
                'conflicting_information': [],
                'supporting_patterns': [],
                'confidence_level': 'low'
            }

            if len(sources) < 2:
                return validation_results

            # Simple consistency check based on content similarity
            if SKLEARN_AVAILABLE:
                validation_results = await self._advanced_validation(sources)
            else:
                validation_results = await self._basic_validation(sources)

            return validation_results

        except Exception as e:
            logger.error(f"Error in cross-source validation: {e}")
            return validation_results

    async def _advanced_validation(self, sources: List[SourceInfo]) -> Dict[str, Any]:
        """Advanced validation using ML techniques"""
        try:
            # Extract content for analysis
            contents = [source.content for source in sources if source.content]

            if len(contents) < 2:
                return {'consistency_score': 0.0, 'confidence_level': 'low'}

            # Calculate content similarity using TF-IDF
            vectorizer = TfidfVectorizer(
                max_features=1000, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform(contents)
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # Calculate average similarity (excluding diagonal)
            n = len(contents)
            total_similarity = 0
            count = 0

            for i in range(n):
                for j in range(i + 1, n):
                    total_similarity += similarity_matrix[i][j]
                    count += 1

            avg_similarity = total_similarity / count if count > 0 else 0

            return {
                'consistency_score': float(avg_similarity),
                'conflicting_information': [],
                'supporting_patterns': [f"Average content similarity: {avg_similarity:.2f}"],
                'confidence_level': 'high' if avg_similarity > 0.6 else 'medium' if avg_similarity > 0.3 else 'low'
            }

        except Exception as e:
            logger.error(f"Error in advanced validation: {e}")
            return await self._basic_validation(sources)

    async def _basic_validation(self, sources: List[SourceInfo]) -> Dict[str, Any]:
        """Basic validation without ML dependencies"""
        # Simple validation based on source count and types
        source_types = [source.source_type for source in sources]
        unique_types = len(set(source_types))

        # More diverse sources generally indicate better validation
        consistency_score = min(unique_types / len(SourceType), 1.0) * 0.7

        return {
            'consistency_score': consistency_score,
            'conflicting_information': [],
            'supporting_patterns': [f"Sources from {unique_types} different types"],
            'confidence_level': 'medium' if unique_types > 2 else 'low'
        }

# Research Synthesizer


class ResearchSynthesizer:
    """Synthesizes research from multiple sources"""

    def __init__(self):
        self.source_analyzer = SourceAnalyzer()
        self.cross_validator = CrossSourceValidator()
        self.module_client = ModuleClient("research-synthesis")

    async def synthesize_research(self, sources: List[SourceInfo], topic: str) -> Dict[str, Any]:
        """Synthesize research from multiple sources"""
        try:
            # Analyze individual sources
            analyzed_sources = []
            for source in sources:
                analyzed_source = await self.source_analyzer.analyze_source(source)
                analyzed_sources.append(analyzed_source)

            # Cross-validate sources
            validation_results = await self.cross_validator.validate_across_sources(analyzed_sources)

            # Extract insights
            insights = await self._extract_insights(analyzed_sources, topic)

            # Identify gaps
            gaps = await self._identify_gaps(analyzed_sources, topic)

            # Generate synthesis
            synthesis = {
                'topic': topic,
                'summary': await self._generate_summary(analyzed_sources, insights),
                # Top 5 insights
                'key_findings': [insight.content for insight in insights[:5]],
                'source_quality_distribution': self._analyze_source_quality(analyzed_sources),
                'validation_results': validation_results,
                'recommendations': await self._generate_recommendations(insights, gaps)
            }

            return {
                'synthesis': synthesis,
                'insights': [asdict(insight) for insight in insights],
                'gaps': [asdict(gap) for gap in gaps],
                'quality_metrics': self._calculate_quality_metrics(analyzed_sources, validation_results)
            }

        except Exception as e:
            logger.error(f"Error synthesizing research: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _extract_insights(self, sources: List[SourceInfo], topic: str) -> List[ResearchInsight]:
        """Extract key insights from sources"""
        insights = []

        try:
            for i, source in enumerate(sources):
                if not source.content:
                    continue

                # Simple insight extraction (would be more sophisticated in production)
                sentences = source.content.split('.')[:5]  # First 5 sentences

                for j, sentence in enumerate(sentences):
                    if len(sentence.strip()) > 50:  # Meaningful sentences
                        insight = ResearchInsight(
                            insight_id=f"insight_{i}_{j}",
                            content=sentence.strip(),
                            sources=[source.source_id],
                            confidence=source.credibility_score * source.relevance_score,
                            category="general"
                        )
                        insights.append(insight)

            # Sort by confidence and return top insights
            insights.sort(key=lambda x: x.confidence, reverse=True)
            return insights[:10]  # Top 10 insights

        except Exception as e:
            logger.error(f"Error extracting insights: {e}")
            return []

    async def _identify_gaps(self, sources: List[SourceInfo], topic: str) -> List[ResearchGap]:
        """Identify gaps in research coverage"""
        gaps = []

        try:
            # Simple gap identification based on source diversity
            source_types_covered = set(
                source.source_type for source in sources)
            all_source_types = set(SourceType)
            missing_types = all_source_types - source_types_covered

            for missing_type in missing_types:
                gap = ResearchGap(
                    gap_id=f"gap_{missing_type.value}",
                    description=f"No {missing_type.value} sources found for {topic}",
                    suggested_sources=[
                        f"Search for {topic} in {missing_type.value}"],
                    priority="medium",
                    research_questions=[
                        f"What does {missing_type.value} reveal about {topic}?"]
                )
                gaps.append(gap)

            return gaps

        except Exception as e:
            logger.error(f"Error identifying gaps: {e}")
            return []

    async def _generate_summary(self, sources: List[SourceInfo], insights: List[ResearchInsight]) -> str:
        """Generate a summary of the research"""
        try:
            high_quality_sources = [
                s for s in sources if s.quality_level == QualityLevel.HIGH]
            total_sources = len(sources)

            summary = f"Research analysis of {total_sources} sources revealed "
            summary += f"{len(insights)} key insights. "

            if high_quality_sources:
                summary += f"{len(high_quality_sources)} sources were rated as high quality. "

            if insights:
                summary += f"Primary finding: {insights[0].content[:100]}..."

            return summary

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return "Summary generation failed"

    async def _generate_recommendations(self, insights: List[ResearchInsight], gaps: List[ResearchGap]) -> List[str]:
        """Generate recommendations based on insights and gaps"""
        recommendations = []

        try:
            # Recommendations based on insights
            if insights:
                high_confidence_insights = [
                    i for i in insights if i.confidence > 0.7]
                if high_confidence_insights:
                    recommendations.append(
                        f"Focus on {len(high_confidence_insights)} high-confidence findings")

            # Recommendations based on gaps
            if gaps:
                high_priority_gaps = [g for g in gaps if g.priority == "high"]
                if high_priority_gaps:
                    recommendations.append(
                        f"Address {len(high_priority_gaps)} high-priority research gaps")

            # General recommendations
            recommendations.append("Consider additional source validation")
            recommendations.append(
                "Expand research to include more diverse source types")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Review findings and consider additional research"]

    def _analyze_source_quality(self, sources: List[SourceInfo]) -> Dict[str, int]:
        """Analyze the distribution of source quality"""
        quality_dist = {level.value: 0 for level in QualityLevel}

        for source in sources:
            quality_dist[source.quality_level.value] += 1

        return quality_dist

    def _calculate_quality_metrics(self, sources: List[SourceInfo], validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall quality metrics"""
        if not sources:
            return {}

        avg_credibility = sum(
            s.credibility_score for s in sources) / len(sources)
        avg_relevance = sum(s.relevance_score for s in sources) / len(sources)

        return {
            'average_credibility': avg_credibility,
            'average_relevance': avg_relevance,
            'total_sources': len(sources),
            'consistency_score': validation_results.get('consistency_score', 0.0),
            'overall_quality': (avg_credibility + avg_relevance + validation_results.get('consistency_score', 0.0)) / 3
        }

# Research Manager - Main Service Class


class ResearchManager:
    """Main research management service"""

    def __init__(self):
        self.synthesizer = ResearchSynthesizer()
        self.research_sessions: Dict[str, ResearchResponse] = {}
        self.module_client = ModuleClient("research-synthesis")

    async def start_research(self, request: ResearchRequest) -> ResearchResponse:
        """Start a new research session"""
        try:
            research_id = request.research_id or str(uuid.uuid4())

            # Create research session
            research_session = ResearchResponse(
                research_id=research_id,
                topic=request.topic,
                status=ResearchStatus.PENDING,
                sources_analyzed=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.research_sessions[research_id] = research_session

            # Start research in background
            asyncio.create_task(self._execute_research(research_id, request))

            return research_session

        except Exception as e:
            logger.error(f"Error starting research: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _execute_research(self, research_id: str, request: ResearchRequest):
        """Execute the research process"""
        try:
            session = self.research_sessions[research_id]
            session.status = ResearchStatus.IN_PROGRESS
            session.updated_at = datetime.utcnow()

            # Convert request sources to SourceInfo objects
            sources = []
            for i, source_data in enumerate(request.sources):
                source = SourceInfo(
                    source_id=source_data.get('source_id', f"source_{i}"),
                    source_type=SourceType(source_data.get(
                        'source_type', 'web_search')),
                    url=source_data.get('url', ''),
                    title=source_data.get('title', ''),
                    content=source_data.get('content', ''),
                    metadata=source_data.get('metadata', {})
                )
                sources.append(source)

            session.sources_analyzed = len(sources)
            session.status = ResearchStatus.ANALYZING
            session.updated_at = datetime.utcnow()

            # Synthesize research
            synthesis_results = await self.synthesizer.synthesize_research(sources, request.topic)

            session.synthesis = synthesis_results['synthesis']
            session.insights = synthesis_results['insights']
            session.gaps = synthesis_results['gaps']
            session.quality_metrics = synthesis_results['quality_metrics']
            session.status = ResearchStatus.COMPLETED
            session.updated_at = datetime.utcnow()

        except Exception as e:
            logger.error(f"Error executing research {research_id}: {e}")
            session.status = ResearchStatus.FAILED
            session.updated_at = datetime.utcnow()

    async def get_research(self, research_id: str) -> Optional[ResearchResponse]:
        """Get research session by ID"""
        return self.research_sessions.get(research_id)

    async def list_research_sessions(self) -> List[ResearchResponse]:
        """List all research sessions"""
        return list(self.research_sessions.values())


# Initialize components
research_manager = ResearchManager()

# Create FastAPI app
app = create_api_app(
    title="Research Synthesis API",
    description="Research synthesis and analysis service for the research synthesis platform",
    version="1.0.0"
)

# API Routes


@app.post("/research", response_model=ResearchResponse)
async def start_research_endpoint(request: ResearchRequest):
    """Start a new research synthesis session"""
    try:
        result = await research_manager.start_research(request)
        return create_success_response(result.dict())
    except Exception as e:
        logger.error(f"Error in start_research_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/research/{research_id}", response_model=ResearchResponse)
async def get_research_endpoint(research_id: str):
    """Get research session by ID"""
    try:
        result = await research_manager.get_research(research_id)
        if result:
            return create_success_response(result.dict())
        else:
            raise HTTPException(
                status_code=404, detail="Research session not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_research_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/research", response_model=List[ResearchResponse])
async def list_research_endpoint():
    """List all research sessions"""
    try:
        sessions = await research_manager.list_research_sessions()
        return create_success_response([session.dict() for session in sessions])
    except Exception as e:
        logger.error(f"Error in list_research_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.post("/analyze/source")
async def analyze_source_endpoint(source_data: Dict[str, Any]):
    """Analyze a single source"""
    try:
        source = SourceInfo(
            source_id=source_data.get('source_id', str(uuid.uuid4())),
            source_type=SourceType(source_data.get(
                'source_type', 'web_search')),
            url=source_data.get('url', ''),
            title=source_data.get('title', ''),
            content=source_data.get('content', ''),
            metadata=source_data.get('metadata', {})
        )

        analyzer = SourceAnalyzer()
        analyzed_source = await analyzer.analyze_source(source)

        return create_success_response(asdict(analyzed_source))
    except Exception as e:
        logger.error(f"Error in analyze_source_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.post("/validate/cross-source")
async def cross_validate_endpoint(sources_data: List[Dict[str, Any]]):
    """Cross-validate multiple sources"""
    try:
        sources = []
        for source_data in sources_data:
            source = SourceInfo(
                source_id=source_data.get('source_id', str(uuid.uuid4())),
                source_type=SourceType(source_data.get(
                    'source_type', 'web_search')),
                url=source_data.get('url', ''),
                title=source_data.get('title', ''),
                content=source_data.get('content', ''),
                metadata=source_data.get('metadata', {})
            )
            sources.append(source)

        validator = CrossSourceValidator()
        validation_results = await validator.validate_across_sources(sources)

        return create_success_response(validation_results)
    except Exception as e:
        logger.error(f"Error in cross_validate_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return create_success_response({
        "status": "healthy",
        "service": "research-synthesis",
        "dependencies": {
            "sklearn": SKLEARN_AVAILABLE,
            "nltk": NLTK_AVAILABLE,
            "spacy": SPACY_AVAILABLE,
            "textstat": TEXTSTAT_AVAILABLE
        },
        "timestamp": datetime.utcnow().isoformat()
    })


@app.get("/stats")
async def get_stats():
    """Get research synthesis statistics"""
    try:
        total_sessions = len(research_manager.research_sessions)
        completed_sessions = sum(1 for session in research_manager.research_sessions.values()
                                 if session.status == ResearchStatus.COMPLETED)

        return create_success_response({
            "total_research_sessions": total_sessions,
            "completed_sessions": completed_sessions,
            "success_rate": completed_sessions / total_sessions if total_sessions > 0 else 0,
            "dependencies_available": {
                "sklearn": SKLEARN_AVAILABLE,
                "nltk": NLTK_AVAILABLE,
                "spacy": SPACY_AVAILABLE,
                "textstat": TEXTSTAT_AVAILABLE
            }
        })
    except Exception as e:
        logger.error(f"Error in get_stats: {e}")
        return create_error_response(str(e), 500)

# Startup event


@app.on_event("startup")
async def startup_event():
    """Initialize the research synthesis service"""
    logger.info("Starting Research Synthesis service...")

    # Initialize NLP components if available
    if NLTK_AVAILABLE:
        try:
            # Ensure NLTK data is available
            import nltk
            nltk.download('vader_lexicon', quiet=True)
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            logger.info("NLTK components initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize NLTK: {e}")

    logger.info("Research Synthesis service started successfully")

# Main execution
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8004,  # Research Synthesis port
        reload=True,
        log_level="info"
    )

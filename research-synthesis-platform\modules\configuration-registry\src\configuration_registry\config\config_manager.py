from typing import Dict, Any, Optional, List
from enum import Enum
import json
import os
from pathlib import Path


class Environment(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class ConfigurationManager:
    """Centralized configuration management system"""

    def __init__(self):
        self.environment = Environment(os.getenv("ENVIRONMENT", "development"))
        self.config_cache: Dict[str, Any] = {}
        self.watchers: List[callable] = []

    def load_base_config(self) -> Dict[str, Any]:
        """Load base configuration for all modules"""
        base_config = {
            "environment": self.environment,
            "logging": {
                "level": "INFO" if self.environment == Environment.PRODUCTION else "DEBUG",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "handlers": ["console", "file"]
            },
            "security": {
                "jwt_secret": os.getenv("JWT_SECRET", "dev-secret-key"),
                "jwt_expiration": 3600,
                "api_key_header": "X-API-Key"
            },
            "performance": {
                "request_timeout": 30,
                "max_retries": 3,
                "circuit_breaker_threshold": 5
            },
            "monitoring": {
                "metrics_enabled": True,
                "tracing_enabled": True,
                "health_check_interval": 30
            }
        }
        return base_config

    def load_module_config(self, module_name: str) -> Dict[str, Any]:
        """Load module-specific configuration"""
        config_path = Path(f"config/{self.environment}/{module_name}.json")

        if config_path.exists():
            with open(config_path, 'r') as f:
                module_config = json.load(f)
        else:
            module_config = self._get_default_module_config(module_name)

        # Merge with base config
        base_config = self.load_base_config()
        merged_config = {**base_config, **module_config}

        # Cache the configuration
        self.config_cache[module_name] = merged_config

        return merged_config

    def get_config(self, module_name: str, key: Optional[str] = None) -> Any:
        """Get configuration value for a module"""
        if module_name not in self.config_cache:
            self.load_module_config(module_name)

        config = self.config_cache[module_name]

        if key:
            return self._get_nested_value(config, key)
        return config

    def update_config(self, module_name: str, key: str, value: Any) -> bool:
        """Update configuration value at runtime"""
        try:
            if module_name not in self.config_cache:
                self.load_module_config(module_name)

            self._set_nested_value(self.config_cache[module_name], key, value)

            # Notify watchers
            for watcher in self.watchers:
                watcher(module_name, key, value)

            return True
        except Exception as e:
            print(f"Failed to update config: {e}")
            return False

    def watch_config_changes(self, callback: callable):
        """Register a callback for configuration changes"""
        self.watchers.append(callback)

    def _get_default_module_config(self, module_name: str) -> Dict[str, Any]:
        """Get default configuration for a module"""
        defaults = {
            "core-agent-orchestration": {
                "port": 8001,
                "autogen": {
                    "max_agents": 10,
                    "conversation_timeout": 300,
                    "memory_limit": "1GB"
                },
                "models": {
                    "primary": "gpt-4o",
                    "fallback": "claude-sonnet-4",
                    "temperature": 0.7
                }
            },
            "external-integration": {
                "port": 8002,
                "github": {
                    "api_url": "https://api.github.com",
                    "rate_limit": 5000,
                    "timeout": 30
                },
                "youtube": {
                    "api_url": "https://www.googleapis.com/youtube/v3",
                    "rate_limit": 10000,
                    "timeout": 30
                },
                "search": {
                    "providers": ["google", "bing", "duckduckgo"],
                    "rate_limit": 1000,
                    "timeout": 15
                }
            },
            # Add defaults for other modules...
        }

        return defaults.get(module_name, {"port": 8000})

    def _get_nested_value(self, config: Dict[str, Any], key: str) -> Any:
        """Get nested configuration value using dot notation"""
        keys = key.split('.')
        value = config
        for k in keys:
            value = value.get(k)
            if value is None:
                break
        return value

    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any):
        """Set nested configuration value using dot notation"""
        keys = key.split('.')
        current = config
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        current[keys[-1]] = value

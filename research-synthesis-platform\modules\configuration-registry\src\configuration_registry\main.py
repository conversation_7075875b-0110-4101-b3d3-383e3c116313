from .security.auth_manager import AuthenticationManager, Permission
from .registry.service_registry import ServiceRegistry, ServiceRegistration, ServiceStatus
from .config.config_manager import ConfigurationManager
from common.api_framework import create_api_app, create_success_response, create_error_response
from fastapi import FastAPI, HTTPException, Depends, Head<PERSON>
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import asyncio
import os
from datetime import datetime

# Import shared utilities
import sys
from pathlib import Path
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))


# Pydantic models for API
class ServiceRegistrationRequest(BaseModel):
    service_id: str
    name: str
    version: str
    host: str
    port: int
    health_check_url: str
    api_base_url: str
    capabilities: List[str]
    metadata: Optional[Dict[str, Any]] = None


class HeartbeatRequest(BaseModel):
    service_id: str


class ConfigUpdateRequest(BaseModel):
    key: str
    value: Any


# Global instances
config_manager: Optional[ConfigurationManager] = None
service_registry: Optional[ServiceRegistry] = None
auth_manager: Optional[AuthenticationManager] = None

# Create FastAPI app
app = create_api_app(
    title="Configuration Registry Module",
    description="Centralized configuration management and service discovery",
    version="1.0.0",
    module_name="configuration-registry"
)


@app.on_event("startup")
async def startup():
    """Initialize module on startup"""
    global config_manager, service_registry, auth_manager

    try:
        # Initialize managers
        config_manager = ConfigurationManager()
        service_registry = ServiceRegistry()
        auth_manager = AuthenticationManager(
            jwt_secret=os.getenv("JWT_SECRET", "dev-secret-key")
        )

        # Start background health monitoring
        asyncio.create_task(service_registry.start_health_monitoring())

        print("Configuration Registry Module started successfully")

    except Exception as e:
        print(f"Failed to start module: {e}")
        raise


def get_api_key(x_api_key: str = Header(None)) -> str:
    """Extract API key from headers"""
    if not x_api_key:
        raise HTTPException(status_code=401, detail="API key required")
    return x_api_key


def authenticate_module(api_key: str = Depends(get_api_key)) -> str:
    """Authenticate module using API key"""
    credentials = auth_manager.validate_api_key(api_key)
    if not credentials:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.module_id


# Configuration endpoints
@app.get("/config/{module_name}", response_model=Dict[str, Any])
async def get_module_config(
    module_name: str,
    key: Optional[str] = None,
    module_id: str = Depends(authenticate_module)
):
    """Get configuration for a module"""
    try:
        # Check permissions
        if not auth_manager.check_permission(module_id, Permission.READ_CONFIG):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions")

        config = config_manager.get_config(module_name, key)

        return create_success_response(
            data=config,
            message=f"Configuration retrieved for {module_name}"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to get configuration",
            errors=[str(e)]
        ).dict()


@app.post("/config/{module_name}", response_model=Dict[str, Any])
async def update_module_config(
    module_name: str,
    request: ConfigUpdateRequest,
    module_id: str = Depends(authenticate_module)
):
    """Update configuration for a module"""
    try:
        # Check permissions
        if not auth_manager.check_permission(module_id, Permission.WRITE_CONFIG):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions")

        success = config_manager.update_config(
            module_name, request.key, request.value)

        if success:
            return create_success_response(
                data={"updated": True},
                message=f"Configuration updated for {module_name}"
            ).dict()
        else:
            return create_error_response(
                message="Failed to update configuration"
            ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to update configuration",
            errors=[str(e)]
        ).dict()


# Service registry endpoints
@app.post("/registry/register", response_model=Dict[str, Any])
async def register_service(
    request: ServiceRegistrationRequest,
    module_id: str = Depends(authenticate_module)
):
    """Register a new service"""
    try:
        # Check permissions
        if not auth_manager.check_permission(module_id, Permission.REGISTER_SERVICE):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions")

        registration = ServiceRegistration(
            service_id=request.service_id,
            name=request.name,
            version=request.version,
            host=request.host,
            port=request.port,
            health_check_url=request.health_check_url,
            api_base_url=request.api_base_url,
            capabilities=request.capabilities,
            metadata=request.metadata or {},
            registered_at=datetime.utcnow(),
            last_heartbeat=datetime.utcnow(),
            status=ServiceStatus.STARTING
        )

        success = await service_registry.register_service(registration)

        if success:
            return create_success_response(
                data={"service_id": request.service_id},
                message="Service registered successfully"
            ).dict()
        else:
            return create_error_response(
                message="Failed to register service"
            ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to register service",
            errors=[str(e)]
        ).dict()


@app.get("/registry/services", response_model=Dict[str, Any])
async def list_services(module_id: str = Depends(authenticate_module)):
    """List all registered services"""
    try:
        # Check permissions
        if not auth_manager.check_permission(module_id, Permission.DISCOVER_SERVICE):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions")

        services = service_registry.discover_services()

        # Convert to dict format for JSON serialization
        services_data = []
        for service in services:
            service_dict = {
                "service_id": service.service_id,
                "name": service.name,
                "version": service.version,
                "host": service.host,
                "port": service.port,
                "health_check_url": service.health_check_url,
                "api_base_url": service.api_base_url,
                "capabilities": service.capabilities,
                "metadata": service.metadata,
                "status": service.status.value,
                "registered_at": service.registered_at.isoformat(),
                "last_heartbeat": service.last_heartbeat.isoformat()
            }
            services_data.append(service_dict)

        return create_success_response(
            data=services_data,
            message="Services retrieved successfully"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to list services",
            errors=[str(e)]
        ).dict()


@app.get("/registry/services/{service_name}", response_model=Dict[str, Any])
async def discover_service(
    service_name: str,
    module_id: str = Depends(authenticate_module)
):
    """Discover services by name"""
    try:
        # Check permissions
        if not auth_manager.check_permission(module_id, Permission.DISCOVER_SERVICE):
            raise HTTPException(
                status_code=403, detail="Insufficient permissions")

        services = service_registry.discover_services(service_name)

        # Convert to dict format
        services_data = []
        for service in services:
            service_dict = {
                "service_id": service.service_id,
                "name": service.name,
                "version": service.version,
                "host": service.host,
                "port": service.port,
                "health_check_url": service.health_check_url,
                "api_base_url": service.api_base_url,
                "capabilities": service.capabilities,
                "metadata": service.metadata,
                "status": service.status.value
            }
            services_data.append(service_dict)

        return create_success_response(
            data=services_data,
            message=f"Services discovered for {service_name}"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to discover services",
            errors=[str(e)]
        ).dict()


@app.post("/registry/heartbeat", response_model=Dict[str, Any])
async def update_heartbeat(
    request: HeartbeatRequest,
    module_id: str = Depends(authenticate_module)
):
    """Update service heartbeat"""
    try:
        success = await service_registry.update_heartbeat(request.service_id)

        if success:
            return create_success_response(
                data={"updated": True},
                message="Heartbeat updated"
            ).dict()
        else:
            return create_error_response(
                message="Failed to update heartbeat"
            ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to update heartbeat",
            errors=[str(e)]
        ).dict()


@app.get("/registry/statistics", response_model=Dict[str, Any])
async def get_registry_statistics(module_id: str = Depends(authenticate_module)):
    """Get service registry statistics"""
    try:
        stats = service_registry.get_service_statistics()

        return create_success_response(
            data=stats,
            message="Registry statistics retrieved"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to get statistics",
            errors=[str(e)]
        ).dict()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

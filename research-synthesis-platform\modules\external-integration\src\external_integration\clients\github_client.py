from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import httpx
import asyncio
import base64
import json
import re

from ..models.github_models import GitHubRepository, RepositoryContent, ContentType


class GitHubClient:
    """Professional GitHub API client with comprehensive repository analysis"""

    def __init__(self, token: str, timeout: int = 30):
        self.token = token
        self.timeout = timeout
        self.base_url = "https://api.github.com"

        # Configure HTTP client with authentication
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            headers={
                "Authorization": f"token {token}",
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "Research-Synthesis-Platform/1.0"
            }
        )

        # Rate limiting tracking
        self.rate_limit_remaining = 5000
        self.rate_limit_reset = datetime.utcnow()

    async def analyze_repository(self, repo_url: str) -> Dict[str, Any]:
        """Comprehensive repository analysis"""
        try:
            # Parse repository URL
            owner, repo_name = self._parse_repo_url(repo_url)

            # Get repository metadata
            repo_info = await self._get_repository_info(owner, repo_name)

            # Analyze repository content
            content_analysis = await self._analyze_repository_content(owner, repo_name)

            # Get community insights
            community_insights = await self._get_community_insights(owner, repo_name)

            # Compile comprehensive analysis
            analysis = {
                "repository": repo_info,
                "content_analysis": content_analysis,
                "community_insights": community_insights,
                "analysis_metadata": {
                    "analyzed_at": datetime.utcnow().isoformat(),
                    "api_calls_used": self._calculate_api_calls(),
                    "analysis_quality": self._assess_analysis_quality(content_analysis, community_insights)
                }
            }

            return analysis

        except Exception as e:
            raise Exception(
                f"Failed to analyze repository {repo_url}: {str(e)}")

    async def _get_repository_info(self, owner: str, repo_name: str) -> GitHubRepository:
        """Get repository metadata and basic information"""

        response = await self._make_api_call(f"/repos/{owner}/{repo_name}")
        data = response.json()

        return GitHubRepository(
            owner=data["owner"]["login"],
            name=data["name"],
            full_name=data["full_name"],
            description=data.get("description", ""),
            language=data.get("language", ""),
            stars=data["stargazers_count"],
            forks=data["forks_count"],
            size=data["size"],
            created_at=datetime.fromisoformat(
                data["created_at"].replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(
                data["updated_at"].replace("Z", "+00:00")),
            topics=data.get("topics", []),
            license=data.get("license", {}).get(
                "name") if data.get("license") else None,
            default_branch=data["default_branch"]
        )

    async def _analyze_repository_content(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Analyze repository content including README, docs, and code structure"""

        content_analysis = {
            "readme": await self._extract_readme(owner, repo_name),
            "documentation": await self._extract_documentation(owner, repo_name),
            "code_structure": await self._analyze_code_structure(owner, repo_name),
            "project_files": await self._analyze_project_files(owner, repo_name)
        }

        return content_analysis

    async def _extract_readme(self, owner: str, repo_name: str) -> Optional[RepositoryContent]:
        """Extract and analyze README file"""

        readme_files = ["README.md", "README.rst", "README.txt", "README"]

        for readme_file in readme_files:
            try:
                response = await self._make_api_call(f"/repos/{owner}/{repo_name}/contents/{readme_file}")
                data = response.json()

                # Decode content
                if data.get("encoding") == "base64":
                    content = base64.b64decode(data["content"]).decode("utf-8")
                else:
                    content = data["content"]

                return RepositoryContent(
                    content_type=ContentType.README,
                    path=data["path"],
                    content=content,
                    size=data["size"],
                    encoding=data["encoding"],
                    url=data["html_url"],
                    extracted_at=datetime.utcnow()
                )

            except httpx.HTTPStatusError as e:
                if e.response.status_code != 404:
                    print(f"Error fetching {readme_file}: {e}")
                continue

        return None

    async def _extract_documentation(self, owner: str, repo_name: str) -> List[RepositoryContent]:
        """Extract documentation files and content"""

        documentation = []
        doc_paths = ["docs", "documentation", "doc", ".github"]

        for doc_path in doc_paths:
            try:
                # Get directory contents
                response = await self._make_api_call(f"/repos/{owner}/{repo_name}/contents/{doc_path}")

                if response.status_code == 200:
                    contents = response.json()

                    # Process documentation files
                    for item in contents:
                        if item["type"] == "file" and self._is_documentation_file(item["name"]):
                            doc_content = await self._extract_file_content(owner, repo_name, item["path"])
                            if doc_content:
                                documentation.append(doc_content)

            except httpx.HTTPStatusError:
                continue  # Directory doesn't exist

        return documentation

    async def _analyze_code_structure(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Analyze repository code structure and organization"""

        try:
            # Get repository tree
            response = await self._make_api_call(f"/repos/{owner}/{repo_name}/git/trees/HEAD?recursive=1")
            tree_data = response.json()

            # Analyze file structure
            files = tree_data.get("tree", [])

            # Categorize files
            file_analysis = {
                "total_files": len(files),
                "languages": self._analyze_languages(files),
                "directory_structure": self._analyze_directory_structure(files),
                "project_type": self._determine_project_type(files),
                "key_files": self._identify_key_files(files)
            }

            return file_analysis

        except Exception as e:
            print(f"Error analyzing code structure: {e}")
            return {}

    async def _analyze_project_files(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Analyze project configuration and metadata files"""

        project_files = {}
        important_files = [
            "package.json", "requirements.txt", "setup.py", "Cargo.toml",
            "pom.xml", "build.gradle", "composer.json", "go.mod",
            "Dockerfile", "docker-compose.yml", ".gitignore",
            "LICENSE", "CONTRIBUTING.md", "CHANGELOG.md"
        ]

        for file_name in important_files:
            try:
                content = await self._extract_file_content(owner, repo_name, file_name)
                if content:
                    project_files[file_name] = {
                        "content": content.content[:1000],  # First 1000 chars
                        "size": content.size,
                        "analysis": self._analyze_project_file(file_name, content.content)
                    }
            except:
                continue

        return project_files

    async def _get_community_insights(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Get community insights including issues, discussions, and releases"""

        community_insights = {}

        # Get recent issues
        community_insights["issues"] = await self._get_recent_issues(owner, repo_name)

        # Get releases
        community_insights["releases"] = await self._get_releases(owner, repo_name)

        # Get contributors
        community_insights["contributors"] = await self._get_top_contributors(owner, repo_name)

        # Get repository statistics
        community_insights["statistics"] = await self._get_repository_statistics(owner, repo_name)

        return community_insights

    async def _get_recent_issues(self, owner: str, repo_name: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent issues for community insights"""

        try:
            response = await self._make_api_call(
                f"/repos/{owner}/{repo_name}/issues?state=all&sort=updated&per_page={limit}"
            )
            issues_data = response.json()

            issues = []
            for issue in issues_data:
                issues.append({
                    "number": issue["number"],
                    "title": issue["title"],
                    "state": issue["state"],
                    "created_at": issue["created_at"],
                    "updated_at": issue["updated_at"],
                    "labels": [label["name"] for label in issue.get("labels", [])],
                    "comments": issue["comments"],
                    "body_preview": issue.get("body", "")[:200] if issue.get("body") else ""
                })

            return issues

        except Exception as e:
            print(f"Error fetching issues: {e}")
            return []

    async def _get_releases(self, owner: str, repo_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get repository releases"""

        try:
            response = await self._make_api_call(f"/repos/{owner}/{repo_name}/releases?per_page={limit}")
            releases_data = response.json()

            releases = []
            for release in releases_data:
                releases.append({
                    "tag_name": release["tag_name"],
                    "name": release["name"],
                    "published_at": release["published_at"],
                    "prerelease": release["prerelease"],
                    "draft": release["draft"],
                    "body_preview": release.get("body", "")[:300] if release.get("body") else ""
                })

            return releases

        except Exception as e:
            print(f"Error fetching releases: {e}")
            return []

    async def _make_api_call(self, endpoint: str) -> httpx.Response:
        """Make authenticated API call with rate limiting"""

        # Check rate limiting
        if self.rate_limit_remaining <= 10 and datetime.utcnow() < self.rate_limit_reset:
            wait_time = (self.rate_limit_reset -
                         datetime.utcnow()).total_seconds()
            if wait_time > 0:
                # Maximum 1 minute wait
                await asyncio.sleep(min(wait_time, 60))

        url = f"{self.base_url}{endpoint}"
        response = await self.client.get(url)

        # Update rate limiting info
        self.rate_limit_remaining = int(
            response.headers.get("X-RateLimit-Remaining", 0))
        reset_timestamp = int(response.headers.get("X-RateLimit-Reset", 0))
        self.rate_limit_reset = datetime.fromtimestamp(reset_timestamp)

        response.raise_for_status()
        return response

    def _parse_repo_url(self, repo_url: str) -> Tuple[str, str]:
        """Parse GitHub repository URL to extract owner and repo name"""

        # Handle different URL formats
        patterns = [
            r"github\.com/([^/]+)/([^/]+?)(?:\.git)?/?$",
            r"github\.com/([^/]+)/([^/]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, repo_url)
            if match:
                return match.group(1), match.group(2)

        raise ValueError(f"Invalid GitHub repository URL: {repo_url}")

    def _calculate_api_calls(self) -> int:
        """Calculate number of API calls used"""
        return 5000 - self.rate_limit_remaining

    def _assess_analysis_quality(self, content_analysis: Dict[str, Any], community_insights: Dict[str, Any]) -> float:
        """Assess the quality of the repository analysis"""
        quality_score = 0.0

        # Check if README exists
        if content_analysis.get("readme"):
            quality_score += 0.3

        # Check documentation
        if content_analysis.get("documentation"):
            quality_score += 0.2

        # Check project files
        if content_analysis.get("project_files"):
            quality_score += 0.2

        # Check community activity
        if community_insights.get("issues"):
            quality_score += 0.15

        if community_insights.get("releases"):
            quality_score += 0.15

        return min(quality_score, 1.0)

    def _is_documentation_file(self, filename: str) -> bool:
        """Check if a file is a documentation file"""
        doc_extensions = ['.md', '.rst', '.txt', '.adoc', '.asciidoc']
        doc_names = ['readme', 'changelog',
                     'contributing', 'license', 'install', 'usage']

        filename_lower = filename.lower()

        # Check extensions
        for ext in doc_extensions:
            if filename_lower.endswith(ext):
                return True

        # Check common doc file names
        for name in doc_names:
            if name in filename_lower:
                return True

        return False

    async def _extract_file_content(self, owner: str, repo_name: str, file_path: str) -> Optional[RepositoryContent]:
        """Extract content from a specific file"""
        try:
            response = await self._make_api_call(f"/repos/{owner}/{repo_name}/contents/{file_path}")
            data = response.json()

            # Decode content
            if data.get("encoding") == "base64":
                content = base64.b64decode(data["content"]).decode("utf-8")
            else:
                content = data["content"]

            return RepositoryContent(
                content_type=ContentType.DOCUMENTATION,
                path=data["path"],
                content=content,
                size=data["size"],
                encoding=data["encoding"],
                url=data["html_url"],
                extracted_at=datetime.utcnow()
            )

        except Exception as e:
            print(f"Error extracting file {file_path}: {e}")
            return None

    def _analyze_languages(self, files: List[Dict[str, Any]]) -> Dict[str, int]:
        """Analyze programming languages in the repository"""
        language_extensions = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.cs': 'C#',
            '.go': 'Go',
            '.rs': 'Rust',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.r': 'R',
            '.m': 'Objective-C',
            '.sh': 'Shell',
            '.sql': 'SQL',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.less': 'LESS'
        }

        language_counts = {}

        for file in files:
            if file.get("type") == "blob":
                path = file.get("path", "")
                for ext, lang in language_extensions.items():
                    if path.endswith(ext):
                        language_counts[lang] = language_counts.get(
                            lang, 0) + 1
                        break

        return language_counts

    def _analyze_directory_structure(self, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze directory structure"""
        directories = set()
        max_depth = 0

        for file in files:
            if file.get("type") == "tree":
                path = file.get("path", "")
                directories.add(path)
                depth = len(path.split("/"))
                max_depth = max(max_depth, depth)

        return {
            "total_directories": len(directories),
            "max_depth": max_depth,
            "top_level_dirs": [d for d in directories if "/" not in d]
        }

    def _determine_project_type(self, files: List[Dict[str, Any]]) -> str:
        """Determine the type of project based on files"""
        file_paths = [f.get("path", "")
                      for f in files if f.get("type") == "blob"]

        # Check for specific project indicators
        if any("package.json" in path for path in file_paths):
            return "Node.js/JavaScript"
        elif any("requirements.txt" in path or "setup.py" in path for path in file_paths):
            return "Python"
        elif any("Cargo.toml" in path for path in file_paths):
            return "Rust"
        elif any("go.mod" in path for path in file_paths):
            return "Go"
        elif any("pom.xml" in path or "build.gradle" in path for path in file_paths):
            return "Java"
        elif any("Dockerfile" in path for path in file_paths):
            return "Containerized Application"
        else:
            return "Unknown"

    def _identify_key_files(self, files: List[Dict[str, Any]]) -> List[str]:
        """Identify key files in the repository"""
        key_files = []
        important_patterns = [
            "README", "LICENSE", "CONTRIBUTING", "CHANGELOG",
            "package.json", "requirements.txt", "setup.py", "Cargo.toml",
            "Dockerfile", "docker-compose", ".gitignore", "Makefile"
        ]

        for file in files:
            if file.get("type") == "blob":
                path = file.get("path", "")
                filename = path.split("/")[-1]

                for pattern in important_patterns:
                    if pattern.lower() in filename.lower():
                        key_files.append(path)
                        break

        return key_files

    def _analyze_project_file(self, filename: str, content: str) -> Dict[str, Any]:
        """Analyze specific project files for insights"""
        analysis = {"type": "unknown", "insights": []}

        try:
            if filename == "package.json":
                data = json.loads(content)
                analysis["type"] = "npm_package"
                analysis["insights"] = [
                    f"Package: {data.get('name', 'unknown')}",
                    f"Version: {data.get('version', 'unknown')}",
                    f"Dependencies: {len(data.get('dependencies', {}))}"
                ]
            elif filename == "requirements.txt":
                lines = content.split('\n')
                deps = [line.strip() for line in lines if line.strip()
                        and not line.startswith('#')]
                analysis["type"] = "python_requirements"
                analysis["insights"] = [f"Python dependencies: {len(deps)}"]
            elif filename == "Dockerfile":
                lines = content.split('\n')
                from_lines = [
                    line for line in lines if line.strip().startswith('FROM')]
                analysis["type"] = "docker_config"
                analysis["insights"] = [f"Base images: {len(from_lines)}"]
        except:
            pass

        return analysis

    async def _get_top_contributors(self, owner: str, repo_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top contributors to the repository"""
        try:
            response = await self._make_api_call(f"/repos/{owner}/{repo_name}/contributors?per_page={limit}")
            contributors_data = response.json()

            contributors = []
            for contributor in contributors_data:
                contributors.append({
                    "login": contributor["login"],
                    "contributions": contributor["contributions"],
                    "avatar_url": contributor["avatar_url"],
                    "html_url": contributor["html_url"]
                })

            return contributors

        except Exception as e:
            print(f"Error fetching contributors: {e}")
            return []

    async def _get_repository_statistics(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Get repository statistics"""
        try:
            # Get languages
            response = await self._make_api_call(f"/repos/{owner}/{repo_name}/languages")
            languages = response.json()

            # Get commit activity (simplified)
            stats = {
                "languages": languages,
                "total_language_bytes": sum(languages.values()),
                "primary_language": max(languages.items(), key=lambda x: x[1])[0] if languages else "Unknown"
            }

            return stats

        except Exception as e:
            print(f"Error fetching repository statistics: {e}")
            return {}

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

#!/usr/bin/env python3
"""
Startup script for Configuration Registry module
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Add shared directory to Python path
shared_path = Path(__file__).parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

def main():
    """Start the Configuration Registry module"""
    
    # Set default environment variables
    os.environ.setdefault("ENVIRONMENT", "development")
    os.environ.setdefault("HOST", "0.0.0.0")
    os.environ.setdefault("PORT", "8000")
    os.environ.setdefault("JWT_SECRET", "dev-secret-key-change-in-production")
    
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    
    print(f"Starting Configuration Registry on {host}:{port}")
    print(f"Environment: {os.getenv('ENVIRONMENT', 'development')}")
    
    # Start the server
    uvicorn.run(
        "configuration_registry.main:app",
        host=host,
        port=port,
        reload=True if os.getenv("ENVIRONMENT") == "development" else False,
        log_level="debug" if os.getenv("ENVIRONMENT") == "development" else "info"
    )

if __name__ == "__main__":
    main()

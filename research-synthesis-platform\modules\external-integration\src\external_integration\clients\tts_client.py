import httpx
import asyncio
from typing import Dict, Any, List, Optional, Union, BinaryIO
from datetime import datetime
import base64
import json
import wave
import io
from dataclasses import dataclass
from enum import Enum
import tempfile
import os


class TTSProvider(str, Enum):
    OPENAI = "openai"
    ELEVENLABS = "elevenlabs"
    AZURE = "azure"


class VoiceGender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    NEUTRAL = "neutral"


class AudioFormat(str, Enum):
    WAV = "wav"
    MP3 = "mp3"
    OGG = "ogg"


@dataclass
class VoiceProfile:
    voice_id: str
    name: str
    gender: VoiceGender
    language: str
    provider: TTSProvider
    description: str
    preview_url: Optional[str] = None
    is_premium: bool = False


@dataclass
class TTSRequest:
    text: str
    voice_profile: VoiceProfile
    speed: float = 1.0
    pitch: float = 1.0
    volume: float = 1.0
    audio_format: AudioFormat = AudioFormat.WAV
    sample_rate: int = 24000


@dataclass
class TTSResult:
    audio_data: bytes
    metadata: Dict[str, Any]
    duration_seconds: float
    file_size_bytes: int
    quality_score: float
    generated_at: datetime


class TTSClient:
    """Unified Text-to-Speech client supporting multiple providers"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.timeout = config.get("timeout", 60)

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "User-Agent": "Research-Synthesis-Platform/1.0"
            }
        )

        # Initialize providers
        self.providers = {
            TTSProvider.OPENAI: OpenAITTSProvider(config.get("openai", {}), self.client),
            TTSProvider.ELEVENLABS: ElevenLabsTTSProvider(config.get("elevenlabs", {}), self.client),
            TTSProvider.AZURE: AzureTTSProvider(
                config.get("azure", {}), self.client)
        }

        # Voice profiles cache
        self.voice_profiles_cache = {}
        self.cache_expiry = datetime.utcnow()

    async def generate_speech(self, request: TTSRequest) -> TTSResult:
        """Generate speech from text using specified voice"""

        try:
            provider = self.providers.get(request.voice_profile.provider)
            if not provider:
                raise Exception(
                    f"Provider {request.voice_profile.provider} not available")

            # Generate audio
            result = await provider.generate_speech(request)

            # Post-process audio if needed
            processed_result = await self._post_process_audio(result, request)

            return processed_result

        except Exception as e:
            raise Exception(f"TTS generation failed: {str(e)}")

    async def generate_conversation(
        self,
        dialogue_segments: List[Dict[str, Any]],
        voice_assignments: Dict[str, VoiceProfile]
    ) -> TTSResult:
        """Generate multi-speaker conversation audio"""

        conversation_parts = []
        total_duration = 0.0

        for segment in dialogue_segments:
            speaker = segment.get("speaker", "default")
            text = segment.get("text", "")
            pause_after = segment.get("pause_seconds", 0.5)

            if speaker not in voice_assignments:
                raise Exception(f"No voice assigned for speaker: {speaker}")

            # Generate speech for this segment
            request = TTSRequest(
                text=text,
                voice_profile=voice_assignments[speaker],
                speed=segment.get("speed", 1.0),
                pitch=segment.get("pitch", 1.0),
                audio_format=AudioFormat.WAV
            )

            segment_result = await self.generate_speech(request)
            conversation_parts.append({
                "audio_data": segment_result.audio_data,
                "duration": segment_result.duration_seconds,
                "pause_after": pause_after,
                "speaker": speaker
            })

            total_duration += segment_result.duration_seconds + pause_after

        # Combine audio segments
        combined_audio = await self._combine_audio_segments(conversation_parts)

        return TTSResult(
            audio_data=combined_audio,
            metadata={
                "speakers": list(voice_assignments.keys()),
                "segments_count": len(dialogue_segments),
                "conversation_type": "multi_speaker"
            },
            duration_seconds=total_duration,
            file_size_bytes=len(combined_audio),
            quality_score=0.9,  # High quality for conversation
            generated_at=datetime.utcnow()
        )

    async def list_available_voices(self, provider: TTSProvider = None) -> List[VoiceProfile]:
        """List available voices from providers"""

        # Check cache first
        cache_key = f"voices_{provider}" if provider else "voices_all"
        if (cache_key in self.voice_profiles_cache and
                datetime.utcnow() < self.cache_expiry):
            return self.voice_profiles_cache[cache_key]

        voices = []

        if provider:
            if provider in self.providers:
                voices = await self.providers[provider].list_voices()
        else:
            # Get voices from all providers
            for provider_name, provider_client in self.providers.items():
                try:
                    provider_voices = await provider_client.list_voices()
                    voices.extend(provider_voices)
                except Exception as e:
                    print(f"Failed to get voices from {provider_name}: {e}")

        # Cache results
        self.voice_profiles_cache[cache_key] = voices
        self.cache_expiry = datetime.utcnow().replace(hour=datetime.utcnow().hour + 1)

        return voices

    async def find_voice(
        self,
        name: str = None,
        gender: VoiceGender = None,
        language: str = None,
        provider: TTSProvider = None
    ) -> Optional[VoiceProfile]:
        """Find voice by criteria"""

        voices = await self.list_available_voices(provider)

        for voice in voices:
            if name and name.lower() not in voice.name.lower():
                continue
            if gender and voice.gender != gender:
                continue
            if language and voice.language.lower() != language.lower():
                continue

            return voice

        return None

    async def _post_process_audio(self, result: TTSResult, request: TTSRequest) -> TTSResult:
        """Post-process generated audio"""

        # For Sprint 2, minimal post-processing
        # In future sprints, could add:
        # - Audio normalization
        # - Format conversion
        # - Quality enhancement

        return result

    async def _combine_audio_segments(self, segments: List[Dict[str, Any]]) -> bytes:
        """Combine multiple audio segments into single file"""

        # Simplified audio combination for Sprint 2
        # In production, use proper audio processing library like pydub

        combined_data = b""

        for segment in segments:
            combined_data += segment["audio_data"]

            # Add pause (simplified - just silence padding)
            pause_duration = segment.get("pause_after", 0.5)
            if pause_duration > 0:
                # Add silence (very simplified approach)
                # 24kHz sample rate
                silence_samples = int(pause_duration * 24000)
                silence = b'\x00' * (silence_samples * 2)  # 16-bit samples
                combined_data += silence

        return combined_data

    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()


class OpenAITTSProvider:
    """OpenAI Text-to-Speech provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.api_key = config.get("api_key")
        self.client = client
        self.base_url = "https://api.openai.com/v1"

        # OpenAI TTS voices
        self.voice_profiles = [
            VoiceProfile("alloy", "Alloy", VoiceGender.NEUTRAL,
                         "en", TTSProvider.OPENAI, "Neutral, balanced voice"),
            VoiceProfile("echo", "Echo", VoiceGender.MALE, "en",
                         TTSProvider.OPENAI, "Male voice with clarity"),
            VoiceProfile("fable", "Fable", VoiceGender.NEUTRAL, "en",
                         TTSProvider.OPENAI, "Expressive, storytelling voice"),
            VoiceProfile("onyx", "Onyx", VoiceGender.MALE, "en",
                         TTSProvider.OPENAI, "Deep male voice"),
            VoiceProfile("nova", "Nova", VoiceGender.FEMALE, "en",
                         TTSProvider.OPENAI, "Warm female voice"),
            VoiceProfile("shimmer", "Shimmer", VoiceGender.FEMALE,
                         "en", TTSProvider.OPENAI, "Bright female voice")
        ]

    async def generate_speech(self, request: TTSRequest) -> TTSResult:
        """Generate speech using OpenAI TTS"""

        if not self.api_key:
            raise Exception("OpenAI API key required")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Map format
        format_mapping = {
            AudioFormat.WAV: "wav",
            AudioFormat.MP3: "mp3",
            AudioFormat.OGG: "opus"
        }

        payload = {
            "model": "tts-1-hd",  # High quality model
            "input": request.text,
            "voice": request.voice_profile.voice_id,
            "response_format": format_mapping.get(request.audio_format, "wav"),
            "speed": max(0.25, min(4.0, request.speed))  # OpenAI limits
        }

        response = await self.client.post(
            f"{self.base_url}/audio/speech",
            headers=headers,
            json=payload
        )

        response.raise_for_status()
        audio_data = response.content

        # Estimate duration (simplified)
        estimated_duration = len(request.text.split()) * \
            0.6  # ~0.6 seconds per word

        return TTSResult(
            audio_data=audio_data,
            metadata={
                "provider": "openai",
                "model": "tts-1-hd",
                "voice": request.voice_profile.voice_id,
                "format": format_mapping.get(request.audio_format, "wav")
            },
            duration_seconds=estimated_duration,
            file_size_bytes=len(audio_data),
            quality_score=0.95,
            generated_at=datetime.utcnow()
        )

    async def list_voices(self) -> List[VoiceProfile]:
        """List available OpenAI voices"""
        return self.voice_profiles.copy()


class ElevenLabsTTSProvider:
    """ElevenLabs Text-to-Speech provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.api_key = config.get("api_key")
        self.client = client
        self.base_url = "https://api.elevenlabs.io/v1"

        # Will be populated from API
        self.voice_profiles = []

    async def generate_speech(self, request: TTSRequest) -> TTSResult:
        """Generate speech using ElevenLabs TTS"""

        if not self.api_key:
            raise Exception("ElevenLabs API key required")

        headers = {
            "xi-api-key": self.api_key,
            "Content-Type": "application/json"
        }

        payload = {
            "text": request.text,
            "model_id": "eleven_multilingual_v2",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.8,
                "style": 0.0,
                "use_speaker_boost": True
            }
        }

        response = await self.client.post(
            f"{self.base_url}/text-to-speech/{request.voice_profile.voice_id}",
            headers=headers,
            json=payload
        )

        response.raise_for_status()
        audio_data = response.content

        # Estimate duration
        # ElevenLabs tends to be slightly slower
        estimated_duration = len(request.text.split()) * 0.7

        return TTSResult(
            audio_data=audio_data,
            metadata={
                "provider": "elevenlabs",
                "model": "eleven_multilingual_v2",
                "voice": request.voice_profile.voice_id
            },
            duration_seconds=estimated_duration,
            file_size_bytes=len(audio_data),
            quality_score=0.98,  # ElevenLabs typically high quality
            generated_at=datetime.utcnow()
        )

    async def list_voices(self) -> List[VoiceProfile]:
        """List available ElevenLabs voices"""

        if not self.api_key:
            return []

        if self.voice_profiles:
            return self.voice_profiles

        try:
            headers = {"xi-api-key": self.api_key}
            response = await self.client.get(f"{self.base_url}/voices", headers=headers)
            response.raise_for_status()

            data = response.json()
            voices = []

            for voice_data in data.get("voices", []):
                voice = VoiceProfile(
                    voice_id=voice_data["voice_id"],
                    name=voice_data["name"],
                    gender=self._detect_gender(voice_data.get("labels", {})),
                    language="en",  # ElevenLabs supports multiple, defaulting to English
                    provider=TTSProvider.ELEVENLABS,
                    description=voice_data.get("description", ""),
                    preview_url=voice_data.get("preview_url"),
                    is_premium=voice_data.get("category") == "premade"
                )
                voices.append(voice)

            self.voice_profiles = voices
            return voices

        except Exception as e:
            print(f"Failed to fetch ElevenLabs voices: {e}")
            return []

    def _detect_gender(self, labels: Dict[str, Any]) -> VoiceGender:
        """Detect gender from voice labels"""

        gender_str = labels.get("gender", "").lower()
        if "male" in gender_str and "female" not in gender_str:
            return VoiceGender.MALE
        elif "female" in gender_str:
            return VoiceGender.FEMALE
        else:
            return VoiceGender.NEUTRAL


class AzureTTSProvider:
    """Azure Cognitive Services Text-to-Speech provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.subscription_key = config.get("subscription_key")
        self.region = config.get("region", "eastus")
        self.client = client

        # Azure voice profiles (sample)
        self.voice_profiles = [
            VoiceProfile("en-US-JennyNeural", "Jenny", VoiceGender.FEMALE,
                         "en-US", TTSProvider.AZURE, "Natural female voice"),
            VoiceProfile("en-US-AriaNeural", "Aria", VoiceGender.FEMALE,
                         "en-US", TTSProvider.AZURE, "Cheerful female voice"),
            VoiceProfile("en-US-GuyNeural", "Guy", VoiceGender.MALE,
                         "en-US", TTSProvider.AZURE, "Natural male voice"),
            VoiceProfile("en-US-DavisNeural", "Davis", VoiceGender.MALE,
                         "en-US", TTSProvider.AZURE, "Conversational male voice")
        ]

    async def generate_speech(self, request: TTSRequest) -> TTSResult:
        """Generate speech using Azure TTS"""

        if not self.subscription_key:
            raise Exception("Azure subscription key required")

        # Get access token
        token = await self._get_access_token()

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/ssml+xml",
            "X-Microsoft-OutputFormat": "riff-24khz-16bit-mono-pcm"
        }

        # Create SSML
        ssml = self._create_ssml(request)

        url = f"https://{self.region}.tts.speech.microsoft.com/cognitiveservices/v1"

        response = await self.client.post(url, headers=headers, content=ssml)
        response.raise_for_status()

        audio_data = response.content
        estimated_duration = len(request.text.split()) * 0.65

        return TTSResult(
            audio_data=audio_data,
            metadata={
                "provider": "azure",
                "voice": request.voice_profile.voice_id,
                "format": "wav"
            },
            duration_seconds=estimated_duration,
            file_size_bytes=len(audio_data),
            quality_score=0.92,
            generated_at=datetime.utcnow()
        )

    async def list_voices(self) -> List[VoiceProfile]:
        """List available Azure voices"""
        return self.voice_profiles.copy()

    async def _get_access_token(self) -> str:
        """Get Azure access token"""

        headers = {
            "Ocp-Apim-Subscription-Key": self.subscription_key
        }

        url = f"https://{self.region}.api.cognitive.microsoft.com/sts/v1.0/issueToken"

        response = await self.client.post(url, headers=headers)
        response.raise_for_status()

        return response.text

    def _create_ssml(self, request: TTSRequest) -> str:
        """Create SSML for Azure TTS"""

        rate = f"{int((request.speed - 1) * 100):+d}%" if request.speed != 1.0 else "0%"
        pitch = f"{int((request.pitch - 1) * 50):+d}%" if request.pitch != 1.0 else "0%"

        ssml = f"""
        <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
            <voice name="{request.voice_profile.voice_id}">
                <prosody rate="{rate}" pitch="{pitch}">
                    {request.text}
                </prosody>
            </voice>
        </speak>
        """.strip()

        return ssml

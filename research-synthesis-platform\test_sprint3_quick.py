#!/usr/bin/env python3
"""
Quick Sprint 3 Test - Simple verification that Sprint 3 components work
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sprint3_imports():
    """Test that Sprint 3 components can be imported"""
    print("🔍 Testing Sprint 3 Component Imports...")
    
    results = []
    
    # Test Memory State Management
    try:
        sys.path.append(str(project_root / "modules" / "memory-state-management" / "src"))
        from memory_state_management.main import MemoryManager, InMemoryStorage
        print("✅ Memory State Management - Import successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Memory State Management - Import failed: {e}")
        results.append(False)
    
    # Test Core Agent Orchestration
    try:
        sys.path.append(str(project_root / "modules" / "core-agent-orchestration" / "src"))
        from core_agent_orchestration.main import AgentRegistry, WorkflowOrchestrator
        print("✅ Core Agent Orchestration - Import successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Core Agent Orchestration - Import failed: {e}")
        results.append(False)
    
    # Test Research Synthesis
    try:
        sys.path.append(str(project_root / "modules" / "research-synthesis" / "src"))
        from research_synthesis.main import ResearchSynthesizer, SourceAnalyzer
        print("✅ Research Synthesis - Import successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Research Synthesis - Import failed: {e}")
        results.append(False)
    
    # Test Inter-Module Communication
    try:
        from shared.common.inter_module_communication import ModuleRegistry, InterModuleClient
        print("✅ Inter-Module Communication - Import successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Inter-Module Communication - Import failed: {e}")
        results.append(False)
    
    return results

def test_sprint3_initialization():
    """Test that Sprint 3 components can be initialized"""
    print("\n🚀 Testing Sprint 3 Component Initialization...")
    
    results = []
    
    # Test Memory Manager
    try:
        sys.path.append(str(project_root / "modules" / "memory-state-management" / "src"))
        from memory_state_management.main import MemoryManager, InMemoryStorage
        
        storage = InMemoryStorage()
        memory_manager = MemoryManager(storage)
        print("✅ Memory Manager - Initialization successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Memory Manager - Initialization failed: {e}")
        results.append(False)
    
    # Test Agent Registry
    try:
        sys.path.append(str(project_root / "modules" / "core-agent-orchestration" / "src"))
        from core_agent_orchestration.main import AgentRegistry, WorkflowOrchestrator
        
        agent_registry = AgentRegistry()
        workflow_orchestrator = WorkflowOrchestrator(agent_registry)
        print("✅ Agent Orchestrator - Initialization successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Agent Orchestrator - Initialization failed: {e}")
        results.append(False)
    
    # Test Research Synthesizer
    try:
        sys.path.append(str(project_root / "modules" / "research-synthesis" / "src"))
        from research_synthesis.main import ResearchSynthesizer, SourceAnalyzer
        
        research_synthesizer = ResearchSynthesizer()
        source_analyzer = SourceAnalyzer()
        print("✅ Research Synthesizer - Initialization successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Research Synthesizer - Initialization failed: {e}")
        results.append(False)
    
    # Test Module Registry
    try:
        from shared.common.inter_module_communication import ModuleRegistry, InterModuleClient
        
        module_registry = ModuleRegistry()
        inter_module_client = InterModuleClient(module_registry)
        print("✅ Module Registry - Initialization successful")
        results.append(True)
    except Exception as e:
        print(f"❌ Module Registry - Initialization failed: {e}")
        results.append(False)
    
    return results

def main():
    """Main test function"""
    print("🚀 Sprint 3 Quick Test")
    print("=" * 40)
    
    # Test imports
    import_results = test_sprint3_imports()
    
    # Test initialization
    init_results = test_sprint3_initialization()
    
    # Summary
    all_results = import_results + init_results
    passed = sum(all_results)
    total = len(all_results)
    
    print(f"\n📊 QUICK TEST SUMMARY")
    print("=" * 40)
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"📈 Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
    
    if passed == total:
        print("\n🎉 All Sprint 3 components working!")
        print("✅ Ready for service deployment")
    else:
        print(f"\n⚠️  {total - passed} component(s) need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

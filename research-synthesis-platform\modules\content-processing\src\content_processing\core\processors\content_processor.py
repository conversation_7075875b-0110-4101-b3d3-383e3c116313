from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import asyncio
import hashlib
import re
from urllib.parse import urlparse


class ContentType(str, Enum):
    TEXT = "text"
    HTML = "html"
    MARKDOWN = "markdown"
    JSON = "json"
    PDF = "pdf"
    VIDEO_TRANSCRIPT = "video_transcript"
    CODE = "code"


class ProcessingQuality(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    FAILED = "failed"


@dataclass
class ContentMetadata:
    content_type: ContentType
    source_url: str
    source_type: str
    extracted_at: datetime
    content_hash: str
    language: str
    encoding: str
    size_bytes: int


@dataclass
class ProcessedContent:
    original_content: str
    processed_content: str
    metadata: ContentMetadata
    quality_score: float
    processing_steps: List[str]
    extracted_entities: Dict[str, List[str]]
    key_points: List[str]
    summary: str
    citations: List[Dict[str, Any]]


class ContentProcessor(ABC):
    """Abstract base class for content processors"""

    @abstractmethod
    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process content and return processed result"""
        pass

    @abstractmethod
    def can_process(self, content_type: ContentType) -> bool:
        """Check if processor can handle this content type"""
        pass

    @abstractmethod
    def get_processor_name(self) -> str:
        """Get processor name for logging"""
        pass


class TextContentProcessor(ContentProcessor):
    """Processor for plain text content"""

    def __init__(self):
        self.name = "TextContentProcessor"

    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process plain text content"""

        processing_steps = []

        # Clean and normalize text
        cleaned_content = await self._clean_text(content)
        processing_steps.append("text_cleaning")

        # Extract entities
        entities = await self._extract_entities(cleaned_content)
        processing_steps.append("entity_extraction")

        # Extract key points
        key_points = await self._extract_key_points(cleaned_content)
        processing_steps.append("key_point_extraction")

        # Generate summary
        summary = await self._generate_summary(cleaned_content)
        processing_steps.append("summarization")

        # Extract citations
        citations = await self._extract_citations(cleaned_content)
        processing_steps.append("citation_extraction")

        # Calculate quality score
        quality_score = await self._calculate_quality_score(cleaned_content, entities, key_points)

        return ProcessedContent(
            original_content=content,
            processed_content=cleaned_content,
            metadata=metadata,
            quality_score=quality_score,
            processing_steps=processing_steps,
            extracted_entities=entities,
            key_points=key_points,
            summary=summary,
            citations=citations
        )

    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type in [ContentType.TEXT, ContentType.MARKDOWN]

    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name

    async def _clean_text(self, content: str) -> str:
        """Clean and normalize text content"""

        # Remove excessive whitespace
        cleaned = re.sub(r'\s+', ' ', content)

        # Remove special characters but keep punctuation
        cleaned = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\"\']+', '', cleaned)

        # Normalize line endings
        cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')

        # Remove empty lines
        lines = [line.strip() for line in cleaned.split('\n') if line.strip()]
        cleaned = '\n'.join(lines)

        return cleaned.strip()

    async def _extract_entities(self, content: str) -> Dict[str, List[str]]:
        """Extract named entities from content"""

        entities = {
            "technical_terms": [],
            "organizations": [],
            "urls": [],
            "email_addresses": [],
            "file_names": [],
            "api_endpoints": []
        }

        # Extract URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`[\]]+'
        entities["urls"] = re.findall(url_pattern, content)

        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["email_addresses"] = re.findall(email_pattern, content)

        # Extract file names (common extensions)
        file_pattern = r'\b\w+\.(py|js|html|css|json|xml|txt|md|pdf|doc|docx|xlsx|csv)\b'
        entities["file_names"] = re.findall(
            file_pattern, content, re.IGNORECASE)

        # Extract technical terms (simplified)
        technical_terms = [
            "API", "REST", "GraphQL", "JSON", "XML", "HTTP", "HTTPS",
            "database", "SQL", "NoSQL", "MongoDB", "PostgreSQL", "MySQL",
            "JavaScript", "Python", "Java", "C++", "TypeScript", "Go", "Rust",
            "React", "Vue", "Angular", "Node.js", "Express", "Django", "Flask",
            "Docker", "Kubernetes", "AWS", "Azure", "GCP", "CI/CD",
            "machine learning", "AI", "neural network", "algorithm"
        ]

        found_terms = []
        content_lower = content.lower()
        for term in technical_terms:
            if term.lower() in content_lower:
                found_terms.append(term)

        entities["technical_terms"] = found_terms

        # Extract organizations (simplified pattern)
        org_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s+(?:Inc|Corp|Ltd|LLC|Company))?\b'
        entities["organizations"] = re.findall(org_pattern, content)

        return entities

    async def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from content"""

        # Split into sentences
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        key_points = []

        # Look for sentences with key indicators
        key_indicators = [
            "important", "key", "main", "primary", "essential", "critical",
            "note that", "remember", "keep in mind", "it's worth",
            "first", "second", "third", "finally", "in conclusion",
            "however", "therefore", "consequently", "as a result"
        ]

        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(indicator in sentence_lower for indicator in key_indicators):
                key_points.append(sentence.strip())

        # If no key indicators found, take longest sentences as potentially important
        if not key_points:
            sentences_by_length = sorted(sentences, key=len, reverse=True)
            key_points = sentences_by_length[:5]  # Top 5 longest sentences

        return key_points[:10]  # Maximum 10 key points

    async def _generate_summary(self, content: str) -> str:
        """Generate content summary"""

        # Simple extractive summarization
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 20]

        if len(sentences) <= 3:
            return content[:500] + "..." if len(content) > 500 else content

        # Take first sentence, middle sentence, and last sentence
        summary_sentences = []
        if sentences:
            summary_sentences.append(sentences[0])
            if len(sentences) > 2:
                summary_sentences.append(sentences[len(sentences) // 2])
            summary_sentences.append(sentences[-1])

        summary = ". ".join(summary_sentences)

        # Limit summary length
        if len(summary) > 500:
            summary = summary[:497] + "..."

        return summary

    async def _extract_citations(self, content: str) -> List[Dict[str, Any]]:
        """Extract citations and references from content"""

        citations = []

        # Extract URLs as potential citations
        url_pattern = r'https?://[^\s<>"{}|\\^`[\]]+'
        urls = re.findall(url_pattern, content)

        for url in urls:
            citation = {
                "type": "url",
                "url": url,
                "title": self._extract_title_from_url(url),
                "extracted_at": datetime.utcnow().isoformat()
            }
            citations.append(citation)

        # Extract academic-style citations (simplified)
        academic_pattern = r'\([A-Za-z]+\s+et\s+al\.,\s+\d{4}\)'
        academic_citations = re.findall(academic_pattern, content)

        for citation_text in academic_citations:
            citation = {
                "type": "academic",
                "text": citation_text,
                "extracted_at": datetime.utcnow().isoformat()
            }
            citations.append(citation)

        return citations

    def _extract_title_from_url(self, url: str) -> str:
        """Extract potential title from URL"""

        try:
            parsed = urlparse(url)
            path = parsed.path.strip('/')

            if path:
                # Convert dashes/underscores to spaces and title case
                title = path.replace('-', ' ').replace('_',
                                                       ' ').replace('/', ' > ')
                return title.title()
            else:
                return parsed.netloc
        except:
            return url

    async def _calculate_quality_score(
        self,
        content: str,
        entities: Dict[str, List[str]],
        key_points: List[str]
    ) -> float:
        """Calculate content quality score"""

        score = 0.0

        # Length score (0-0.3)
        content_length = len(content)
        if content_length > 1000:
            score += 0.3
        elif content_length > 500:
            score += 0.2
        elif content_length > 100:
            score += 0.1

        # Entity richness score (0-0.3)
        total_entities = sum(len(entity_list)
                             for entity_list in entities.values())
        if total_entities > 10:
            score += 0.3
        elif total_entities > 5:
            score += 0.2
        elif total_entities > 0:
            score += 0.1

        # Structure score (0-0.2)
        if key_points:
            score += 0.2

        # Technical content score (0-0.2)
        if entities.get("technical_terms"):
            score += 0.2

        return min(score, 1.0)


class HTMLContentProcessor(ContentProcessor):
    """Processor for HTML content"""

    def __init__(self):
        self.name = "HTMLContentProcessor"

    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process HTML content"""

        processing_steps = []

        # Extract text from HTML
        text_content = await self._extract_text_from_html(content)
        processing_steps.append("html_text_extraction")

        # Extract structured data
        structured_data = await self._extract_structured_data(content)
        processing_steps.append("structured_data_extraction")

        # Use text processor for remaining processing
        text_processor = TextContentProcessor()
        text_result = await text_processor.process(text_content, metadata)

        # Merge results
        text_result.processing_steps = processing_steps + text_result.processing_steps
        text_result.extracted_entities.update(structured_data)

        return text_result

    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type == ContentType.HTML

    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name

    async def _extract_text_from_html(self, html_content: str) -> str:
        """Extract clean text from HTML"""

        # Remove script and style elements
        html_content = re.sub(
            r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(
            r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', html_content)

        # Decode HTML entities
        html_entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&nbsp;': ' '
        }

        for entity, char in html_entities.items():
            text = text.replace(entity, char)

        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    async def _extract_structured_data(self, html_content: str) -> Dict[str, List[str]]:
        """Extract structured data from HTML"""

        structured_data = {
            "headings": [],
            "links": [],
            "images": [],
            "meta_tags": []
        }

        # Extract headings
        heading_pattern = r'<h[1-6][^>]*>(.*?)</h[1-6]>'
        headings = re.findall(heading_pattern, html_content,
                              re.IGNORECASE | re.DOTALL)
        structured_data["headings"] = [
            re.sub(r'<[^>]+>', '', h).strip() for h in headings]

        # Extract links
        link_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>(.*?)</a>'
        links = re.findall(link_pattern, html_content,
                           re.IGNORECASE | re.DOTALL)
        structured_data["links"] = [url for url, text in links]

        # Extract images
        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        images = re.findall(img_pattern, html_content, re.IGNORECASE)
        structured_data["images"] = images

        # Extract meta tags
        meta_pattern = r'<meta[^>]+name=["\']([^"\']+)["\'][^>]+content=["\']([^"\']+)["\'][^>]*>'
        meta_tags = re.findall(meta_pattern, html_content, re.IGNORECASE)
        structured_data["meta_tags"] = [
            f"{name}: {content}" for name, content in meta_tags]

        return structured_data


class ContentProcessingPipeline:
    """Content processing pipeline with multiple processors"""

    def __init__(self):
        self.processors = [
            TextContentProcessor(),
            HTMLContentProcessor()
        ]

        # Processing statistics
        self.processing_stats = {
            "total_processed": 0,
            "processing_times": [],
            "quality_scores": []
        }

    async def process_content(
        self,
        content: str,
        content_type: ContentType,
        source_url: str,
        source_type: str
    ) -> ProcessedContent:
        """Process content through appropriate processor"""

        start_time = datetime.utcnow()

        # Create metadata
        metadata = ContentMetadata(
            content_type=content_type,
            source_url=source_url,
            source_type=source_type,
            extracted_at=start_time,
            content_hash=hashlib.md5(content.encode()).hexdigest(),
            language="en",  # Simplified for Sprint 2
            encoding="utf-8",
            size_bytes=len(content.encode())
        )

        # Find appropriate processor
        processor = self._find_processor(content_type)

        if not processor:
            raise Exception(
                f"No processor available for content type: {content_type}")

        # Process content
        try:
            result = await processor.process(content, metadata)

            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self.processing_stats["total_processed"] += 1
            self.processing_stats["processing_times"].append(processing_time)
            self.processing_stats["quality_scores"].append(
                result.quality_score)

            return result

        except Exception as e:
            raise Exception(f"Content processing failed: {str(e)}")

    def _find_processor(self, content_type: ContentType) -> Optional[ContentProcessor]:
        """Find processor for content type"""

        for processor in self.processors:
            if processor.can_process(content_type):
                return processor

        return None

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing pipeline statistics"""

        stats = self.processing_stats.copy()

        if stats["processing_times"]:
            stats["average_processing_time"] = sum(
                stats["processing_times"]) / len(stats["processing_times"])
            stats["max_processing_time"] = max(stats["processing_times"])

        if stats["quality_scores"]:
            stats["average_quality_score"] = sum(
                stats["quality_scores"]) / len(stats["quality_scores"])
            stats["min_quality_score"] = min(stats["quality_scores"])
            stats["max_quality_score"] = max(stats["quality_scores"])

        return stats

    def add_processor(self, processor: ContentProcessor):
        """Add new processor to pipeline"""
        self.processors.append(processor)

    def list_supported_content_types(self) -> List[ContentType]:
        """List all supported content types"""

        supported_types = set()
        for processor in self.processors:
            for content_type in ContentType:
                if processor.can_process(content_type):
                    supported_types.add(content_type)

        return list(supported_types)

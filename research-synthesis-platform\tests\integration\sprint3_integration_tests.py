# Sprint 3 Integration Testing Framework
# Based on integration_testing_framework.py from folder 3

import asyncio
import logging
import json
import time
import uuid
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, Union, Type
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
from contextlib import asynccontextmanager
import concurrent.futures
import statistics

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

# External dependencies with fallbacks
try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False
    logging.warning("pytest not available")

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    logging.warning("httpx not available")

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    logging.warning("docker not available")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available")

try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    logging.warning("aiofiles not available")

try:
    from fastapi.testclient import TestClient
    FASTAPI_TEST_AVAILABLE = True
except ImportError:
    FASTAPI_TEST_AVAILABLE = False
    logging.warning("FastAPI TestClient not available")

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    logging.warning("PyYAML not available")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test result enums


class TestStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestType(Enum):
    UNIT = "unit"
    INTEGRATION = "integration"
    CONTRACT = "contract"
    PERFORMANCE = "performance"
    CHAOS = "chaos"
    END_TO_END = "end_to_end"


class SeverityLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Data Classes


@dataclass
class TestResult:
    """Result of a test execution"""
    test_id: str
    test_name: str
    test_type: TestType
    status: TestStatus
    duration: float
    error_message: Optional[str] = None
    details: Dict[str, Any] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.details is None:
            self.details = {}


@dataclass
class TestSuite:
    """Collection of related tests"""
    suite_id: str
    name: str
    description: str
    tests: List[str] = None  # test_ids
    setup_hooks: List[Callable] = None
    teardown_hooks: List[Callable] = None

    def __post_init__(self):
        if self.tests is None:
            self.tests = []
        if self.setup_hooks is None:
            self.setup_hooks = []
        if self.teardown_hooks is None:
            self.teardown_hooks = []


@dataclass
class ModuleTestConfig:
    """Configuration for testing a module"""
    module_name: str
    base_url: str
    health_endpoint: str = "/health"
    auth_required: bool = False
    auth_token: Optional[str] = None
    timeout: int = 30
    retry_attempts: int = 3

# Base Test Class


class BaseTest(ABC):
    """Abstract base class for all tests"""

    def __init__(self, test_id: str, name: str, test_type: TestType):
        self.test_id = test_id
        self.name = name
        self.test_type = test_type
        self.setup_hooks: List[Callable] = []
        self.teardown_hooks: List[Callable] = []

    @abstractmethod
    async def execute(self) -> TestResult:
        """Execute the test"""
        pass

    async def setup(self):
        """Setup before test execution"""
        for hook in self.setup_hooks:
            if asyncio.iscoroutinefunction(hook):
                await hook()
            else:
                hook()

    async def teardown(self):
        """Cleanup after test execution"""
        for hook in self.teardown_hooks:
            if asyncio.iscoroutinefunction(hook):
                await hook()
            else:
                hook()

    def add_setup_hook(self, hook: Callable):
        """Add setup hook"""
        self.setup_hooks.append(hook)

    def add_teardown_hook(self, hook: Callable):
        """Add teardown hook"""
        self.teardown_hooks.append(hook)

# HTTP Test Implementation


class HTTPTest(BaseTest):
    """HTTP-based test for API endpoints"""

    def __init__(self, test_id: str, name: str, config: ModuleTestConfig,
                 endpoint: str, method: str = "GET", payload: Optional[Dict] = None,
                 expected_status: int = 200):
        super().__init__(test_id, name, TestType.INTEGRATION)
        self.config = config
        self.endpoint = endpoint
        self.method = method.upper()
        self.payload = payload
        self.expected_status = expected_status

    async def execute(self) -> TestResult:
        """Execute HTTP test"""
        start_time = time.time()

        try:
            await self.setup()

            url = f"{self.config.base_url}{self.endpoint}"
            headers = {}

            if self.config.auth_required and self.config.auth_token:
                headers["Authorization"] = f"Bearer {self.config.auth_token}"

            if not HTTPX_AVAILABLE:
                return TestResult(
                    test_id=self.test_id,
                    test_name=self.name,
                    test_type=self.test_type,
                    status=TestStatus.SKIPPED,
                    duration=0,
                    error_message="httpx not available"
                )

            async with httpx.AsyncClient() as client:
                if self.method == "GET":
                    response = await client.get(url, headers=headers, timeout=self.config.timeout)
                elif self.method == "POST":
                    response = await client.post(url, json=self.payload, headers=headers, timeout=self.config.timeout)
                elif self.method == "PUT":
                    response = await client.put(url, json=self.payload, headers=headers, timeout=self.config.timeout)
                elif self.method == "DELETE":
                    response = await client.delete(url, headers=headers, timeout=self.config.timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {self.method}")

                duration = time.time() - start_time

                if response.status_code == self.expected_status:
                    status = TestStatus.PASSED
                    error_message = None
                else:
                    status = TestStatus.FAILED
                    error_message = f"Expected status {self.expected_status}, got {response.status_code}"

                return TestResult(
                    test_id=self.test_id,
                    test_name=self.name,
                    test_type=self.test_type,
                    status=status,
                    duration=duration,
                    error_message=error_message,
                    details={
                        "response_status": response.status_code,
                        "response_headers": dict(response.headers),
                        "response_body": response.text[:1000] if len(response.text) > 1000 else response.text
                    }
                )

        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_id=self.test_id,
                test_name=self.name,
                test_type=self.test_type,
                status=TestStatus.ERROR,
                duration=duration,
                error_message=str(e)
            )

        finally:
            await self.teardown()

# Performance Test Implementation


class PerformanceTest(BaseTest):
    """Performance test for measuring response times and throughput"""

    def __init__(self, test_id: str, name: str, config: ModuleTestConfig,
                 endpoint: str, concurrent_requests: int = 10,
                 total_requests: int = 100, max_response_time: float = 1.0):
        super().__init__(test_id, name, TestType.PERFORMANCE)
        self.config = config
        self.endpoint = endpoint
        self.concurrent_requests = concurrent_requests
        self.total_requests = total_requests
        self.max_response_time = max_response_time

    async def execute(self) -> TestResult:
        """Execute performance test"""
        start_time = time.time()

        try:
            await self.setup()

            if not HTTPX_AVAILABLE:
                return TestResult(
                    test_id=self.test_id,
                    test_name=self.name,
                    test_type=self.test_type,
                    status=TestStatus.SKIPPED,
                    duration=0,
                    error_message="httpx not available"
                )

            url = f"{self.config.base_url}{self.endpoint}"
            response_times = []
            errors = 0

            # Execute concurrent requests
            semaphore = asyncio.Semaphore(self.concurrent_requests)

            async def make_request():
                async with semaphore:
                    try:
                        request_start = time.time()
                        async with httpx.AsyncClient() as client:
                            response = await client.get(url, timeout=self.config.timeout)
                            request_duration = time.time() - request_start
                            response_times.append(request_duration)
                            return response.status_code == 200
                    except Exception:
                        nonlocal errors
                        errors += 1
                        return False

            # Create tasks for all requests
            tasks = [make_request() for _ in range(self.total_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            duration = time.time() - start_time

            # Calculate statistics
            if response_times:
                avg_response_time = statistics.mean(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
                p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(
                    response_times) > 20 else max_response_time
            else:
                avg_response_time = 0
                min_response_time = 0
                max_response_time = 0
                p95_response_time = 0

            success_rate = (len(response_times) / self.total_requests) * 100
            throughput = len(response_times) / duration

            # Determine test status
            if errors == 0 and avg_response_time <= self.max_response_time:
                status = TestStatus.PASSED
                error_message = None
            else:
                status = TestStatus.FAILED
                error_message = f"Errors: {errors}, Avg response time: {avg_response_time:.3f}s"

            return TestResult(
                test_id=self.test_id,
                test_name=self.name,
                test_type=self.test_type,
                status=status,
                duration=duration,
                error_message=error_message,
                details={
                    "total_requests": self.total_requests,
                    "successful_requests": len(response_times),
                    "failed_requests": errors,
                    "success_rate": success_rate,
                    "throughput": throughput,
                    "avg_response_time": avg_response_time,
                    "min_response_time": min_response_time,
                    "max_response_time": max_response_time,
                    "p95_response_time": p95_response_time
                }
            )

        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_id=self.test_id,
                test_name=self.name,
                test_type=self.test_type,
                status=TestStatus.ERROR,
                duration=duration,
                error_message=str(e)
            )

        finally:
            await self.teardown()

# Contract Test Implementation


class ContractTest(BaseTest):
    """Contract test for API compatibility"""

    def __init__(self, test_id: str, name: str, config: ModuleTestConfig,
                 endpoint: str, expected_schema: Dict[str, Any]):
        super().__init__(test_id, name, TestType.CONTRACT)
        self.config = config
        self.endpoint = endpoint
        self.expected_schema = expected_schema

    async def execute(self) -> TestResult:
        """Execute contract test"""
        start_time = time.time()

        try:
            await self.setup()

            if not HTTPX_AVAILABLE:
                return TestResult(
                    test_id=self.test_id,
                    test_name=self.name,
                    test_type=self.test_type,
                    status=TestStatus.SKIPPED,
                    duration=0,
                    error_message="httpx not available"
                )

            url = f"{self.config.base_url}{self.endpoint}"

            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=self.config.timeout)

                if response.status_code != 200:
                    duration = time.time() - start_time
                    return TestResult(
                        test_id=self.test_id,
                        test_name=self.name,
                        test_type=self.test_type,
                        status=TestStatus.FAILED,
                        duration=duration,
                        error_message=f"API returned status {response.status_code}"
                    )

                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    duration = time.time() - start_time
                    return TestResult(
                        test_id=self.test_id,
                        test_name=self.name,
                        test_type=self.test_type,
                        status=TestStatus.FAILED,
                        duration=duration,
                        error_message="Response is not valid JSON"
                    )

                # Simple schema validation (basic implementation)
                schema_errors = self._validate_schema(
                    response_data, self.expected_schema)

                duration = time.time() - start_time

                if not schema_errors:
                    return TestResult(
                        test_id=self.test_id,
                        test_name=self.name,
                        test_type=self.test_type,
                        status=TestStatus.PASSED,
                        duration=duration,
                        details={"response_data": response_data}
                    )
                else:
                    return TestResult(
                        test_id=self.test_id,
                        test_name=self.name,
                        test_type=self.test_type,
                        status=TestStatus.FAILED,
                        duration=duration,
                        error_message=f"Schema validation failed: {', '.join(schema_errors)}",
                        details={"response_data": response_data,
                                 "schema_errors": schema_errors}
                    )

        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_id=self.test_id,
                test_name=self.name,
                test_type=self.test_type,
                status=TestStatus.ERROR,
                duration=duration,
                error_message=str(e)
            )

        finally:
            await self.teardown()

    def _validate_schema(self, data: Any, schema: Dict[str, Any]) -> List[str]:
        """Basic schema validation"""
        errors = []

        if not isinstance(data, dict):
            errors.append("Response is not a dictionary")
            return errors

        # Check required fields
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")

        # Check field types
        properties = schema.get("properties", {})
        for field, field_schema in properties.items():
            if field in data:
                expected_type = field_schema.get("type")
                actual_value = data[field]

                if expected_type == "string" and not isinstance(actual_value, str):
                    errors.append(
                        f"Field {field} should be string, got {type(actual_value).__name__}")
                elif expected_type == "integer" and not isinstance(actual_value, int):
                    errors.append(
                        f"Field {field} should be integer, got {type(actual_value).__name__}")
                elif expected_type == "number" and not isinstance(actual_value, (int, float)):
                    errors.append(
                        f"Field {field} should be number, got {type(actual_value).__name__}")
                elif expected_type == "boolean" and not isinstance(actual_value, bool):
                    errors.append(
                        f"Field {field} should be boolean, got {type(actual_value).__name__}")
                elif expected_type == "array" and not isinstance(actual_value, list):
                    errors.append(
                        f"Field {field} should be array, got {type(actual_value).__name__}")
                elif expected_type == "object" and not isinstance(actual_value, dict):
                    errors.append(
                        f"Field {field} should be object, got {type(actual_value).__name__}")

        return errors

# Test Runner


class TestRunner:
    """Main test runner for executing test suites"""

    def __init__(self):
        self.test_results: Dict[str, TestResult] = {}
        self.test_suites: Dict[str, TestSuite] = {}

    async def register_test_suite(self, test_suite: TestSuite):
        """Register a test suite"""
        self.test_suites[test_suite.suite_id] = test_suite
        logger.info(f"Registered test suite: {test_suite.name}")

    async def run_test(self, test: BaseTest) -> TestResult:
        """Run a single test"""
        logger.info(f"Running test: {test.name}")
        result = await test.execute()
        self.test_results[test.test_id] = result

        status_emoji = "✅" if result.status == TestStatus.PASSED else "❌" if result.status == TestStatus.FAILED else "⚠️"
        logger.info(
            f"{status_emoji} {test.name}: {result.status.value} ({result.duration:.3f}s)")

        return result

    async def run_test_suite(self, suite_id: str) -> Dict[str, TestResult]:
        """Run all tests in a test suite"""
        suite = self.test_suites.get(suite_id)
        if not suite:
            raise ValueError(f"Test suite {suite_id} not found")

        logger.info(f"Running test suite: {suite.name}")

        # Run setup hooks
        for hook in suite.setup_hooks:
            if asyncio.iscoroutinefunction(hook):
                await hook()
            else:
                hook()

        suite_results = {}

        try:
            # Run all tests in the suite
            for test_id in suite.tests:
                # This would normally get the test from a registry
                # For now, we'll skip if test not found
                logger.info(f"Test {test_id} would be executed here")

        finally:
            # Run teardown hooks
            for hook in suite.teardown_hooks:
                if asyncio.iscoroutinefunction(hook):
                    await hook()
                else:
                    hook()

        return suite_results

    async def get_test_results(self) -> Dict[str, TestResult]:
        """Get all test results"""
        return self.test_results

    async def generate_report(self) -> Dict[str, Any]:
        """Generate test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values(
        ) if result.status == TestStatus.PASSED)
        failed_tests = sum(1 for result in self.test_results.values(
        ) if result.status == TestStatus.FAILED)
        error_tests = sum(1 for result in self.test_results.values()
                          if result.status == TestStatus.ERROR)
        skipped_tests = sum(1 for result in self.test_results.values(
        ) if result.status == TestStatus.SKIPPED)

        total_duration = sum(
            result.duration for result in self.test_results.values())

        return {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "skipped": skipped_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "total_duration": total_duration
            },
            "results": [asdict(result) for result in self.test_results.values()]
        }

# Sprint 3 Specific Tests


async def test_sprint3_modules():
    """Test all Sprint 3 modules"""
    runner = TestRunner()

    # Module configurations
    modules = {
        "memory-state-management": ModuleTestConfig(
            module_name="memory-state-management",
            base_url="http://localhost:8003",
            health_endpoint="/health"
        ),
        "core-agent-orchestration": ModuleTestConfig(
            module_name="core-agent-orchestration",
            base_url="http://localhost:8001",
            health_endpoint="/health"
        ),
        "research-synthesis": ModuleTestConfig(
            module_name="research-synthesis",
            base_url="http://localhost:8004",
            health_endpoint="/health"
        )
    }

    print("🔍 Testing Sprint 3 Module Health Checks...")
    # Health check tests
    for module_name, config in modules.items():
        test = HTTPTest(
            test_id=f"health_{module_name}",
            name=f"Health Check - {module_name}",
            config=config,
            endpoint="/health",
            method="GET",
            expected_status=200
        )
        await runner.run_test(test)

    print("🧠 Testing Memory State Management...")
    # Memory State Management tests
    memory_config = modules["memory-state-management"]

    # Test memory storage
    memory_test = HTTPTest(
        test_id="memory_store",
        name="Memory Storage Test",
        config=memory_config,
        endpoint="/memories",
        method="POST",
        payload={
            "session_id": "test_session",
            "agent_id": "test_agent",
            "memory_type": "test",
            "content": "Test memory content"
        },
        expected_status=200
    )
    await runner.run_test(memory_test)

    # Test memory query
    memory_query_test = HTTPTest(
        test_id="memory_query",
        name="Memory Query Test",
        config=memory_config,
        endpoint="/memories/query",
        method="POST",
        payload={
            "session_id": "test_session",
            "limit": 10
        },
        expected_status=200
    )
    await runner.run_test(memory_query_test)

    print("🤖 Testing Core Agent Orchestration...")
    # Core Agent Orchestration tests
    agent_config = modules["core-agent-orchestration"]

    # Test agent creation
    agent_test = HTTPTest(
        test_id="agent_create",
        name="Agent Creation Test",
        config=agent_config,
        endpoint="/agents",
        method="POST",
        payload={
            "name": "TestAgent",
            "agent_type": "research_coordinator",
            "system_message": "Test agent for integration testing"
        },
        expected_status=200
    )
    await runner.run_test(agent_test)

    # Test agent listing
    agent_list_test = HTTPTest(
        test_id="agent_list",
        name="Agent List Test",
        config=agent_config,
        endpoint="/agents",
        method="GET",
        expected_status=200
    )
    await runner.run_test(agent_list_test)

    print("🔬 Testing Research Synthesis...")
    # Research Synthesis tests
    research_config = modules["research-synthesis"]

    # Test research start
    research_test = HTTPTest(
        test_id="research_start",
        name="Research Start Test",
        config=research_config,
        endpoint="/research",
        method="POST",
        payload={
            "topic": "Test Research Topic",
            "sources": [
                {
                    "source_id": "test_source_1",
                    "source_type": "web_search",
                    "url": "https://example.com",
                    "title": "Test Source",
                    "content": "This is test content for research synthesis testing."
                }
            ]
        },
        expected_status=200
    )
    await runner.run_test(research_test)

    print("⚡ Running Performance Tests...")
    # Performance tests
    for module_name, config in modules.items():
        perf_test = PerformanceTest(
            test_id=f"perf_{module_name}",
            name=f"Performance Test - {module_name}",
            config=config,
            endpoint="/health",
            concurrent_requests=5,
            total_requests=20,
            max_response_time=2.0
        )
        await runner.run_test(perf_test)

    print("📋 Running Contract Tests...")
    # Contract tests
    health_schema = {
        "type": "object",
        "required": ["status", "service", "timestamp"],
        "properties": {
            "status": {"type": "string"},
            "service": {"type": "string"},
            "timestamp": {"type": "string"}
        }
    }

    for module_name, config in modules.items():
        contract_test = ContractTest(
            test_id=f"contract_{module_name}",
            name=f"Contract Test - {module_name}",
            config=config,
            endpoint="/health",
            expected_schema=health_schema
        )
        await runner.run_test(contract_test)

    # Generate and return report
    report = await runner.generate_report()
    return report

# Main execution function


async def main():
    """Main function to run Sprint 3 integration tests"""
    print("🚀 Starting Sprint 3 Integration Tests...")
    print("=" * 60)

    try:
        report = await test_sprint3_modules()

        print("\n📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        summary = report["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"⚠️  Errors: {summary['errors']}")
        print(f"⏭️  Skipped: {summary['skipped']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Total Duration: {summary['total_duration']:.3f}s")

        print("\n📋 DETAILED RESULTS")
        print("=" * 60)
        for result_data in report["results"]:
            status_emoji = "✅" if result_data["status"] == "passed" else "❌" if result_data["status"] == "failed" else "⚠️"
            print(
                f"{status_emoji} {result_data['test_name']}: {result_data['status']} ({result_data['duration']:.3f}s)")
            if result_data.get("error_message"):
                print(f"   Error: {result_data['error_message']}")

        print("\n🎯 SPRINT 3 INTEGRATION TEST COMPLETE!")

        # Return success/failure based on results
        return summary['failed'] == 0 and summary['errors'] == 0

    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

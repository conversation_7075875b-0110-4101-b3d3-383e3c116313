import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict
import statistics


@dataclass
class PerformanceMetric:
    operation: str
    execution_time: float
    success: bool
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CacheEntry:
    data: Any
    created_at: datetime
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.utcnow)
    ttl: int = 3600  # 1 hour default


class PerformanceOptimizer:
    """Performance optimization and monitoring for external integrations"""

    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.cache: Dict[str, CacheEntry] = {}
        self.connection_pools: Dict[str, Any] = {}
        self.rate_limiters: Dict[str, Any] = {}

        # Performance thresholds
        self.thresholds = {
            "github_analysis": 30.0,  # seconds
            "youtube_analysis": 45.0,
            "web_search": 15.0,
            "tts_generation": 20.0,
            "content_processing": 10.0
        }

        # Optimization settings
        self.cache_enabled = True
        self.connection_pooling_enabled = True
        self.async_processing_enabled = True

    def record_metric(self, operation: str, execution_time: float, success: bool, metadata: Dict[str, Any] = None):
        """Record performance metric"""

        metric = PerformanceMetric(
            operation=operation,
            execution_time=execution_time,
            success=success,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )

        self.metrics.append(metric)

        # Keep only recent metrics (last 1000)
        if len(self.metrics) > 1000:
            self.metrics = self.metrics[-1000:]

        # Check performance threshold
        threshold = self.thresholds.get(operation)
        if threshold and execution_time > threshold:
            print(
                f"Performance warning: {operation} took {execution_time:.2f}s (threshold: {threshold}s)")

    def get_cache(self, key: str) -> Optional[Any]:
        """Get item from cache"""

        if not self.cache_enabled or key not in self.cache:
            return None

        entry = self.cache[key]
        current_time = datetime.utcnow()

        # Check TTL
        if current_time - entry.created_at > timedelta(seconds=entry.ttl):
            del self.cache[key]
            return None

        # Update access statistics
        entry.access_count += 1
        entry.last_accessed = current_time

        return entry.data

    def set_cache(self, key: str, data: Any, ttl: int = 3600):
        """Set item in cache"""

        if not self.cache_enabled:
            return

        # Implement LRU eviction if cache is full
        max_cache_size = 1000
        if len(self.cache) >= max_cache_size:
            # Remove 10% of entries
            self._evict_lru_entries(max_cache_size // 10)

        self.cache[key] = CacheEntry(
            data=data,
            created_at=datetime.utcnow(),
            ttl=ttl
        )

    def _evict_lru_entries(self, count: int):
        """Evict least recently used cache entries"""

        # Sort by last accessed time
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed
        )

        # Remove oldest entries
        for i in range(min(count, len(sorted_entries))):
            key = sorted_entries[i][0]
            del self.cache[key]

    def get_performance_statistics(self, operation: str = None) -> Dict[str, Any]:
        """Get performance statistics"""

        if operation:
            operation_metrics = [
                m for m in self.metrics if m.operation == operation]
        else:
            operation_metrics = self.metrics

        if not operation_metrics:
            return {}

        execution_times = [m.execution_time for m in operation_metrics]
        success_count = sum(1 for m in operation_metrics if m.success)

        stats = {
            "operation": operation or "all",
            "total_operations": len(operation_metrics),
            "success_rate": success_count / len(operation_metrics),
            "average_execution_time": statistics.mean(execution_times),
            "median_execution_time": statistics.median(execution_times),
            "min_execution_time": min(execution_times),
            "max_execution_time": max(execution_times),
            "recent_operations": len([m for m in operation_metrics if
                                      datetime.utcnow() - m.timestamp < timedelta(hours=1)])
        }

        if len(execution_times) > 1:
            stats["std_dev_execution_time"] = statistics.stdev(execution_times)

        return stats

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics"""

        if not self.cache_enabled:
            return {"cache_enabled": False}

        current_time = datetime.utcnow()
        active_entries = 0
        expired_entries = 0
        total_access_count = 0

        for entry in self.cache.values():
            if current_time - entry.created_at <= timedelta(seconds=entry.ttl):
                active_entries += 1
            else:
                expired_entries += 1
            total_access_count += entry.access_count

        return {
            "cache_enabled": True,
            "total_entries": len(self.cache),
            "active_entries": active_entries,
            "expired_entries": expired_entries,
            "cache_utilization": active_entries / 1000,  # Assuming max 1000 entries
            "total_access_count": total_access_count,
            "average_access_per_entry": total_access_count / len(self.cache) if self.cache else 0
        }

    def optimize_async_execution(self, tasks: List[Callable]) -> List[Any]:
        """Optimize async task execution"""

        if not self.async_processing_enabled:
            # Execute sequentially
            results = []
            for task in tasks:
                try:
                    if asyncio.iscoroutinefunction(task):
                        result = asyncio.run(task())
                    else:
                        result = task()
                    results.append(result)
                except Exception as e:
                    results.append(e)
            return results

        # Execute in parallel with concurrency control
        async def execute_with_semaphore():
            semaphore = asyncio.Semaphore(5)  # Limit concurrent operations

            async def execute_task(task):
                async with semaphore:
                    try:
                        if asyncio.iscoroutinefunction(task):
                            return await task()
                        else:
                            return task()
                    except Exception as e:
                        return e

            return await asyncio.gather(*[execute_task(task) for task in tasks])

        return asyncio.run(execute_with_semaphore())

    def should_use_cache(self, operation: str, params: Dict[str, Any]) -> bool:
        """Determine if operation should use cache"""

        # Operations that benefit from caching
        cacheable_operations = [
            "github_repository_info",
            "youtube_video_info",
            "web_search_results",
            "content_processing"
        ]

        return operation in cacheable_operations

    def generate_cache_key(self, operation: str, params: Dict[str, Any]) -> str:
        """Generate cache key for operation and parameters"""

        import hashlib
        import json

        # Create deterministic key from operation and parameters
        key_data = {
            "operation": operation,
            "params": sorted(params.items()) if isinstance(params, dict) else params
        }

        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()

    def cleanup_expired_cache(self):
        """Clean up expired cache entries"""

        current_time = datetime.utcnow()
        expired_keys = []

        for key, entry in self.cache.items():
            if current_time - entry.created_at > timedelta(seconds=entry.ttl):
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        print(f"Cleaned up {len(expired_keys)} expired cache entries")

    def get_optimization_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""

        recommendations = []

        # Analyze performance metrics
        stats = self.get_performance_statistics()

        if stats.get("success_rate", 1.0) < 0.9:
            recommendations.append(
                "High failure rate detected. Review error handling and retry mechanisms.")

        if stats.get("average_execution_time", 0) > 30:
            recommendations.append(
                "High average execution time. Consider enabling caching and connection pooling.")

        # Analyze cache performance
        cache_stats = self.get_cache_statistics()

        if cache_stats.get("cache_enabled") and cache_stats.get("cache_utilization", 0) < 0.3:
            recommendations.append(
                "Low cache utilization. Review caching strategy and TTL settings.")

        # Operation-specific recommendations
        for operation, threshold in self.thresholds.items():
            op_stats = self.get_performance_statistics(operation)
            if op_stats.get("average_execution_time", 0) > threshold:
                recommendations.append(
                    f"Operation '{operation}' exceeds performance threshold. Consider optimization.")

        if not recommendations:
            recommendations.append("Performance is within acceptable ranges.")

        return recommendations

    def enable_performance_monitoring(self):
        """Enable comprehensive performance monitoring"""

        # Start background cleanup task
        async def background_cleanup():
            while True:
                await asyncio.sleep(300)  # Every 5 minutes
                self.cleanup_expired_cache()

        # Note: In production, this would be started as a background task
        print("Performance monitoring enabled")

# Performance decorator for timing operations


def measure_performance(optimizer: PerformanceOptimizer, operation: str):
    """Decorator to measure operation performance"""

    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            result = None
            error = None

            try:
                result = await func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                error = e
                raise
            finally:
                execution_time = time.time() - start_time
                optimizer.record_metric(
                    operation=operation,
                    execution_time=execution_time,
                    success=success,
                    metadata={
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_count": len(kwargs),
                        "error": str(error) if error else None
                    }
                )

        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            result = None
            error = None

            try:
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                error = e
                raise
            finally:
                execution_time = time.time() - start_time
                optimizer.record_metric(
                    operation=operation,
                    execution_time=execution_time,
                    success=success,
                    metadata={
                        "function": func.__name__,
                        "args_count": len(args),
                        "kwargs_count": len(kwargs),
                        "error": str(error) if error else None
                    }
                )

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

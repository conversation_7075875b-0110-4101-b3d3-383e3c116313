#!/usr/bin/env python3
"""
Sprint 3 Integration Test - Verifies Sprint 3 builds on Sprint 2 foundation
Tests that Sprint 3 components properly integrate with existing Sprint 2 functionality
"""

import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sprint2_components():
    """Test that Sprint 2 components are available and working"""
    print("🔍 Testing Sprint 2 Foundation Components...")
    
    try:
        # Add Sprint 2 module paths
        sys.path.append(str(project_root / "modules" / "external-integration" / "src"))
        
        # Import Sprint 2 components
        from external_integration.tools.tool_registry import ToolRegistry
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        from external_integration.clients.github_client import GitHubClient
        from external_integration.clients.youtube_client import YouTubeClient
        from external_integration.clients.search_client import SearchClient
        from external_integration.clients.tts_client import TTSClient
        
        # Test Tool Registry
        tool_registry = ToolRegistry()
        print(f"✅ Tool Registry: {len(tool_registry.tools)} tools available")
        
        # Test Performance Optimizer
        performance_optimizer = PerformanceOptimizer()
        performance_optimizer.record_metric("test_metric", 1.0)
        print(f"✅ Performance Optimizer: {len(performance_optimizer.metrics)} metrics recorded")
        
        # Test External Clients
        github_client = GitHubClient()
        youtube_client = YouTubeClient()
        search_client = SearchClient()
        tts_client = TTSClient()
        
        print("✅ External Integration Clients:")
        print(f"   - GitHub Client: {github_client.__class__.__name__}")
        print(f"   - YouTube Client: {youtube_client.__class__.__name__}")
        print(f"   - Search Client: {search_client.__class__.__name__}")
        print(f"   - TTS Client: {tts_client.__class__.__name__}")
        
        return True, {
            'tool_registry': tool_registry,
            'performance_optimizer': performance_optimizer,
            'github_client': github_client,
            'youtube_client': youtube_client,
            'search_client': search_client,
            'tts_client': tts_client
        }
        
    except Exception as e:
        print(f"❌ Sprint 2 foundation test failed: {e}")
        return False, {}

def test_sprint3_memory_with_sprint2(sprint2_components):
    """Test Sprint 3 Memory State Management integration with Sprint 2"""
    print("\n🧠 Testing Sprint 3 Memory State Management...")
    
    try:
        # Add Sprint 3 memory module path
        sys.path.append(str(project_root / "modules" / "memory-state-management" / "src"))
        
        # Import Sprint 3 memory components
        from memory_state_management.main import (
            MemoryManager, InMemoryStorage, MemoryRequest, MemoryQuery
        )
        
        # Initialize memory components
        storage = InMemoryStorage()
        memory_manager = MemoryManager(storage)
        
        print("✅ Sprint 3 Memory Manager initialized")
        
        # Test basic memory operations
        memory_request = MemoryRequest(
            session_id="test_session",
            agent_id="test_agent",
            memory_type="test",
            content="Test memory content for Sprint 3"
        )
        
        # This would normally be async, but for testing we'll check the structure
        print("✅ Memory Request structure validated")
        
        # Check if Sprint 2 integration is available
        if hasattr(memory_manager, 'performance_optimizer'):
            if memory_manager.performance_optimizer:
                print("✅ Sprint 2 Performance Optimizer integrated")
            else:
                print("⚠️  Sprint 2 Performance Optimizer not integrated")
        
        if hasattr(memory_manager, 'tool_registry'):
            if memory_manager.tool_registry:
                print("✅ Sprint 2 Tool Registry integrated")
            else:
                print("⚠️  Sprint 2 Tool Registry not integrated")
        
        return True
        
    except Exception as e:
        print(f"❌ Sprint 3 Memory integration test failed: {e}")
        return False

def test_sprint3_agents_with_sprint2(sprint2_components):
    """Test Sprint 3 Agent Orchestration integration with Sprint 2"""
    print("\n🤖 Testing Sprint 3 Agent Orchestration...")
    
    try:
        # Add Sprint 3 agent module path
        sys.path.append(str(project_root / "modules" / "core-agent-orchestration" / "src"))
        
        # Import Sprint 3 agent components
        from core_agent_orchestration.main import (
            AgentRegistry, WorkflowOrchestrator, AgentConfig, AgentType
        )
        
        # Initialize agent components
        agent_registry = AgentRegistry()
        workflow_orchestrator = WorkflowOrchestrator(agent_registry)
        
        print("✅ Sprint 3 Agent Orchestrator initialized")
        
        # Test agent configuration
        agent_config = AgentConfig(
            name="TestAgent",
            agent_type=AgentType.RESEARCH_COORDINATOR,
            system_message="Test agent for Sprint 3 integration"
        )
        
        print("✅ Agent Configuration structure validated")
        
        # Check Sprint 2 integration
        if hasattr(workflow_orchestrator, 'tool_registry'):
            if workflow_orchestrator.tool_registry:
                print("✅ Sprint 2 Tool Registry integrated")
            else:
                print("⚠️  Sprint 2 Tool Registry not integrated")
        
        if hasattr(workflow_orchestrator, 'github_client'):
            if workflow_orchestrator.github_client:
                print("✅ Sprint 2 GitHub Client integrated")
            else:
                print("⚠️  Sprint 2 GitHub Client not integrated")
        
        if hasattr(workflow_orchestrator, 'youtube_client'):
            if workflow_orchestrator.youtube_client:
                print("✅ Sprint 2 YouTube Client integrated")
            else:
                print("⚠️  Sprint 2 YouTube Client not integrated")
        
        return True
        
    except Exception as e:
        print(f"❌ Sprint 3 Agent integration test failed: {e}")
        return False

def test_sprint3_research_with_sprint2(sprint2_components):
    """Test Sprint 3 Research Synthesis integration with Sprint 2"""
    print("\n🔬 Testing Sprint 3 Research Synthesis...")
    
    try:
        # Add Sprint 3 research module path
        sys.path.append(str(project_root / "modules" / "research-synthesis" / "src"))
        
        # Import Sprint 3 research components
        from research_synthesis.main import (
            ResearchSynthesizer, SourceAnalyzer, ResearchRequest
        )
        
        # Initialize research components
        research_synthesizer = ResearchSynthesizer()
        source_analyzer = SourceAnalyzer()
        
        print("✅ Sprint 3 Research Synthesizer initialized")
        
        # Test research request structure
        research_request = ResearchRequest(
            topic="Test Research Topic",
            sources=[{
                "source_id": "test_source",
                "source_type": "web_search",
                "url": "https://example.com",
                "title": "Test Source",
                "content": "Test content for research synthesis"
            }]
        )
        
        print("✅ Research Request structure validated")
        
        # Check if components can work together
        if hasattr(source_analyzer, 'module_client'):
            print("✅ Module client for inter-module communication available")
        else:
            print("⚠️  Module client not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Sprint 3 Research integration test failed: {e}")
        return False

def test_inter_module_communication():
    """Test Sprint 3 Inter-Module Communication framework"""
    print("\n🔗 Testing Sprint 3 Inter-Module Communication...")
    
    try:
        # Import Sprint 3 communication framework
        from shared.common.inter_module_communication import (
            ModuleRegistry, InterModuleClient, SecurityManager
        )
        
        # Initialize communication components
        module_registry = ModuleRegistry()
        security_manager = SecurityManager("test_secret_key")
        inter_module_client = InterModuleClient(module_registry)
        
        print("✅ Inter-Module Communication framework initialized")
        print(f"   - Module Registry: {len(module_registry.modules)} modules")
        print(f"   - Security Manager: {security_manager.__class__.__name__}")
        print(f"   - Inter-Module Client: {inter_module_client.__class__.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Inter-Module Communication test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Sprint 3 Integration Test - Building on Sprint 2 Foundation")
    print("=" * 70)
    
    start_time = time.time()
    
    # Test Sprint 2 foundation
    sprint2_success, sprint2_components = test_sprint2_components()
    
    if not sprint2_success:
        print("\n❌ Sprint 2 foundation not available - cannot test Sprint 3 integration")
        return False
    
    # Test Sprint 3 components with Sprint 2 integration
    tests = [
        ("Sprint 3 Memory Integration", lambda: test_sprint3_memory_with_sprint2(sprint2_components)),
        ("Sprint 3 Agent Integration", lambda: test_sprint3_agents_with_sprint2(sprint2_components)),
        ("Sprint 3 Research Integration", lambda: test_sprint3_research_with_sprint2(sprint2_components)),
        ("Inter-Module Communication", test_inter_module_communication)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append(False)
    
    # Summary
    duration = time.time() - start_time
    passed = sum([sprint2_success] + results)
    total = len(results) + 1  # +1 for Sprint 2 foundation
    
    print(f"\n📊 INTEGRATION TEST SUMMARY")
    print("=" * 70)
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"📈 Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
    print(f"⏱️  Duration: {duration:.2f}s")
    
    if passed == total:
        print("\n🎉 SUCCESS: Sprint 3 successfully builds on Sprint 2!")
        print("✅ Sprint 2 foundation is solid")
        print("✅ Sprint 3 components properly integrate with Sprint 2")
        print("✅ Inter-module communication framework ready")
        print("✅ Ready for service deployment and API testing")
    else:
        print(f"\n⚠️  {total - passed} integration issue(s) detected")
        if not sprint2_success:
            print("🔧 Fix Sprint 2 foundation first")
        else:
            print("🔧 Check Sprint 3 integration with Sprint 2 components")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

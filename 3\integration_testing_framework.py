# integration_testing/testing_framework.py
import asyncio
import pytest
import httpx
import logging
import json
import time
import uuid
from typing import Any, Dict, List, Optional, Callable, Union, Type
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
from contextlib import asynccontextmanager
import docker
import psutil
import aiofiles
from fastapi import FastAPI
from fastapi.testclient import TestClient
import uvicorn
from pydantic import BaseModel
import yaml
import concurrent.futures
import statistics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test result enums
class TestStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"

class TestType(Enum):
    UNIT = "unit"
    INTEGRATION = "integration"
    CONTRACT = "contract"
    PERFORMANCE = "performance"
    CHAOS = "chaos"
    END_TO_END = "end_to_end"

class SeverityLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Data Classes
@dataclass
class TestCase:
    test_id: str
    name: str
    description: str
    test_type: TestType
    severity: SeverityLevel
    modules_under_test: List[str]
    dependencies: List[str] = None
    timeout: int = 60
    setup_steps: List[str] = None
    teardown_steps: List[str] = None
    expected_results: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.setup_steps is None:
            self.setup_steps = []
        if self.teardown_steps is None:
            self.teardown_steps = []
        if self.expected_results is None:
            self.expected_results = {}
        if self.metadata is None:
            self.metadata = {}

@dataclass
class TestExecution:
    execution_id: str
    test_case: TestCase
    status: TestStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    logs: List[str] = None
    metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.logs is None:
            self.logs = []
        if self.metrics is None:
            self.metrics = {}

@dataclass
class TestSuite:
    suite_id: str
    name: str
    description: str
    test_cases: List[TestCase]
    parallel_execution: bool = False
    timeout: int = 300
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class ModuleEndpoint:
    module_id: str
    base_url: str
    health_endpoint: str = "/health"
    auth_required: bool = True

# Contract Testing
class ContractTest:
    def __init__(self, provider: str, consumer: str, interaction: Dict[str, Any]):
        self.provider = provider
        self.consumer = consumer
        self.interaction = interaction
        self.test_id = f"contract_{provider}_{consumer}_{uuid.uuid4().hex[:8]}"
    
    async def validate_contract(self, provider_endpoint: ModuleEndpoint) -> bool:
        """Validate contract between provider and consumer"""
        try:
            request_spec = self.interaction.get("request", {})
            response_spec = self.interaction.get("response", {})
            
            async with httpx.AsyncClient() as client:
                # Make request based on contract
                url = f"{provider_endpoint.base_url}{request_spec.get('path', '/')}"
                method = request_spec.get("method", "GET").upper()
                headers = request_spec.get("headers", {})
                body = request_spec.get("body")
                
                response = await client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=body if body else None,
                    timeout=30
                )
                
                # Validate response against contract
                expected_status = response_spec.get("status", 200)
                if response.status_code != expected_status:
                    logger.error(f"Status mismatch: expected {expected_status}, got {response.status_code}")
                    return False
                
                expected_headers = response_spec.get("headers", {})
                for header, expected_value in expected_headers.items():
                    actual_value = response.headers.get(header)
                    if actual_value != expected_value:
                        logger.error(f"Header mismatch for {header}: expected {expected_value}, got {actual_value}")
                        return False
                
                # Validate response body structure if specified
                expected_body_schema = response_spec.get("body_schema")
                if expected_body_schema:
                    response_json = response.json()
                    if not self._validate_schema(response_json, expected_body_schema):
                        return False
                
                return True
                
        except Exception as e:
            logger.error(f"Contract validation failed: {e}")
            return False
    
    def _validate_schema(self, data: Any, schema: Dict[str, Any]) -> bool:
        """Simple schema validation"""
        schema_type = schema.get("type")
        
        if schema_type == "object":
            if not isinstance(data, dict):
                return False
            
            required_fields = schema.get("required", [])
            for field in required_fields:
                if field not in data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            properties = schema.get("properties", {})
            for field, field_schema in properties.items():
                if field in data:
                    if not self._validate_schema(data[field], field_schema):
                        return False
        
        elif schema_type == "array":
            if not isinstance(data, list):
                return False
            
            items_schema = schema.get("items")
            if items_schema:
                for item in data:
                    if not self._validate_schema(item, items_schema):
                        return False
        
        elif schema_type == "string":
            if not isinstance(data, str):
                return False
        
        elif schema_type == "number":
            if not isinstance(data, (int, float)):
                return False
        
        elif schema_type == "boolean":
            if not isinstance(data, bool):
                return False
        
        return True

# Performance Testing
class PerformanceTest:
    def __init__(
        self,
        name: str,
        target_endpoint: ModuleEndpoint,
        concurrent_users: int = 10,
        duration_seconds: int = 60,
        ramp_up_seconds: int = 10
    ):
        self.name = name
        self.target_endpoint = target_endpoint
        self.concurrent_users = concurrent_users
        self.duration_seconds = duration_seconds
        self.ramp_up_seconds = ramp_up_seconds
        self.results = []
    
    async def run_load_test(self, test_scenario: Callable) -> Dict[str, Any]:
        """Run load test with specified scenario"""
        start_time = time.time()
        tasks = []
        
        # Gradually ramp up users
        user_interval = self.ramp_up_seconds / self.concurrent_users
        
        for user_id in range(self.concurrent_users):
            # Stagger user start times
            start_delay = user_id * user_interval
            task = asyncio.create_task(
                self._user_session(user_id, test_scenario, start_delay)
            )
            tasks.append(task)
        
        # Wait for test duration
        await asyncio.sleep(self.duration_seconds)
        
        # Cancel all tasks
        for task in tasks:
            task.cancel()
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Calculate metrics
        return self._calculate_performance_metrics()
    
    async def _user_session(self, user_id: int, test_scenario: Callable, start_delay: float):
        """Individual user session"""
        await asyncio.sleep(start_delay)
        
        try:
            while True:
                start_time = time.time()
                
                try:
                    await test_scenario(user_id, self.target_endpoint)
                    duration = time.time() - start_time
                    self.results.append({
                        "user_id": user_id,
                        "timestamp": start_time,
                        "duration": duration,
                        "success": True,
                        "error": None
                    })
                
                except Exception as e:
                    duration = time.time() - start_time
                    self.results.append({
                        "user_id": user_id,
                        "timestamp": start_time,
                        "duration": duration,
                        "success": False,
                        "error": str(e)
                    })
                
                # Small delay between requests
                await asyncio.sleep(0.1)
                
        except asyncio.CancelledError:
            pass
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate performance metrics from results"""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r["success"]]
        failed_results = [r for r in self.results if not r["success"]]
        
        durations = [r["duration"] for r in successful_results]
        
        total_requests = len(self.results)
        successful_requests = len(successful_results)
        failed_requests = len(failed_results)
        
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        metrics = {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": success_rate,
            "throughput_rps": total_requests / self.duration_seconds,
        }
        
        if durations:
            metrics.update({
                "avg_response_time": statistics.mean(durations),
                "min_response_time": min(durations),
                "max_response_time": max(durations),
                "p50_response_time": statistics.median(durations),
                "p95_response_time": self._percentile(durations, 95),
                "p99_response_time": self._percentile(durations, 99),
            })
        
        return metrics
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile value"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * len(sorted_data)
        
        if index.is_integer():
            return sorted_data[int(index) - 1]
        else:
            lower_index = int(index)
            upper_index = lower_index + 1
            
            if upper_index < len(sorted_data):
                weight = index - lower_index
                return sorted_data[lower_index] * (1 - weight) + sorted_data[upper_index] * weight
            else:
                return sorted_data[lower_index]

# Chaos Testing
class ChaosTest:
    def __init__(self, name: str, target_modules: List[str]):
        self.name = name
        self.target_modules = target_modules
        self.docker_client = docker.from_env()
    
    async def introduce_network_latency(self, container_name: str, delay_ms: int, duration_seconds: int):
        """Introduce network latency to a container"""
        try:
            container = self.docker_client.containers.get(container_name)
            
            # Add network delay using tc (traffic control)
            command = f"tc qdisc add dev eth0 root netem delay {delay_ms}ms"
            container.exec_run(command, privileged=True)
            
            logger.info(f"Added {delay_ms}ms latency to {container_name}")
            
            # Wait for specified duration
            await asyncio.sleep(duration_seconds)
            
            # Remove network delay
            command = "tc qdisc del dev eth0 root"
            container.exec_run(command, privileged=True)
            
            logger.info(f"Removed latency from {container_name}")
            
        except Exception as e:
            logger.error(f"Failed to introduce network latency: {e}")
    
    async def simulate_high_cpu_load(self, container_name: str, duration_seconds: int):
        """Simulate high CPU load on a container"""
        try:
            container = self.docker_client.containers.get(container_name)
            
            # Start CPU stress test
            command = f"stress --cpu $(nproc) --timeout {duration_seconds}s"
            exec_result = container.exec_run(command, detach=True)
            
            logger.info(f"Started CPU stress test on {container_name}")
            
            # Wait for stress test to complete
            await asyncio.sleep(duration_seconds + 5)
            
            logger.info(f"CPU stress test completed on {container_name}")
            
        except Exception as e:
            logger.error(f"Failed to simulate CPU load: {e}")
    
    async def simulate_memory_pressure(self, container_name: str, memory_mb: int, duration_seconds: int):
        """Simulate memory pressure on a container"""
        try:
            container = self.docker_client.containers.get(container_name)
            
            # Start memory stress test
            command = f"stress --vm 1 --vm-bytes {memory_mb}M --timeout {duration_seconds}s"
            exec_result = container.exec_run(command, detach=True)
            
            logger.info(f"Started memory stress test on {container_name}")
            
            # Wait for stress test to complete
            await asyncio.sleep(duration_seconds + 5)
            
            logger.info(f"Memory stress test completed on {container_name}")
            
        except Exception as e:
            logger.error(f"Failed to simulate memory pressure: {e}")
    
    async def restart_container(self, container_name: str):
        """Restart a container to simulate failure"""
        try:
            container = self.docker_client.containers.get(container_name)
            
            logger.info(f"Restarting container {container_name}")
            container.restart()
            
            # Wait for container to restart
            await asyncio.sleep(10)
            
            logger.info(f"Container {container_name} restarted")
            
        except Exception as e:
            logger.error(f"Failed to restart container: {e}")

# Test Runner
class IntegrationTestRunner:
    def __init__(self, module_endpoints: Dict[str, ModuleEndpoint]):
        self.module_endpoints = module_endpoints
        self.test_suites: Dict[str, TestSuite] = {}
        self.test_executions: Dict[str, TestExecution] = {}
        self.contract_tests: List[ContractTest] = []
        self.performance_tests: List[PerformanceTest] = []
        self.chaos_tests: List[ChaosTest] = []
    
    def register_test_suite(self, test_suite: TestSuite):
        """Register a test suite"""
        self.test_suites[test_suite.suite_id] = test_suite
        logger.info(f"Registered test suite: {test_suite.suite_id}")
    
    def add_contract_test(self, contract_test: ContractTest):
        """Add a contract test"""
        self.contract_tests.append(contract_test)
    
    def add_performance_test(self, performance_test: PerformanceTest):
        """Add a performance test"""
        self.performance_tests.append(performance_test)
    
    def add_chaos_test(self, chaos_test: ChaosTest):
        """Add a chaos test"""
        self.chaos_tests.append(chaos_test)
    
    async def run_test_suite(self, suite_id: str) -> Dict[str, Any]:
        """Run a complete test suite"""
        if suite_id not in self.test_suites:
            raise ValueError(f"Test suite {suite_id} not found")
        
        test_suite = self.test_suites[suite_id]
        suite_start_time = datetime.utcnow()
        
        logger.info(f"Starting test suite: {test_suite.name}")
        
        if test_suite.parallel_execution:
            results = await self._run_tests_parallel(test_suite.test_cases)
        else:
            results = await self._run_tests_sequential(test_suite.test_cases)
        
        suite_end_time = datetime.utcnow()
        suite_duration = (suite_end_time - suite_start_time).total_seconds()
        
        # Calculate suite metrics
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.status == TestStatus.PASSED)
        failed_tests = sum(1 for r in results if r.status == TestStatus.FAILED)
        error_tests = sum(1 for r in results if r.status == TestStatus.ERROR)
        
        suite_result = {
            "suite_id": suite_id,
            "suite_name": test_suite.name,
            "started_at": suite_start_time.isoformat(),
            "completed_at": suite_end_time.isoformat(),
            "duration": suite_duration,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
            "test_results": [asdict(r) for r in results]
        }
        
        logger.info(f"Test suite {suite_id} completed: {passed_tests}/{total_tests} passed")
        
        return suite_result
    
    async def _run_tests_sequential(self, test_cases: List[TestCase]) -> List[TestExecution]:
        """Run test cases sequentially"""
        results = []
        
        for test_case in test_cases:
            result = await self._execute_test_case(test_case)
            results.append(result)
        
        return results
    
    async def _run_tests_parallel(self, test_cases: List[TestCase]) -> List[TestExecution]:
        """Run test cases in parallel"""
        tasks = []
        
        for test_case in test_cases:
            task = asyncio.create_task(self._execute_test_case(test_case))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_execution = TestExecution(
                    execution_id=str(uuid.uuid4()),
                    test_case=test_cases[i],
                    status=TestStatus.ERROR,
                    started_at=datetime.utcnow(),
                    completed_at=datetime.utcnow(),
                    error_message=str(result)
                )
                processed_results.append(error_execution)
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _execute_test_case(self, test_case: TestCase) -> TestExecution:
        """Execute a single test case"""
        execution_id = str(uuid.uuid4())
        execution = TestExecution(
            execution_id=execution_id,
            test_case=test_case,
            status=TestStatus.RUNNING,
            started_at=datetime.utcnow()
        )
        
        self.test_executions[execution_id] = execution
        
        try:
            # Setup phase
            await self._run_setup_steps(test_case.setup_steps)
            
            # Execute test based on type
            if test_case.test_type == TestType.INTEGRATION:
                result = await self._run_integration_test(test_case)
            elif test_case.test_type == TestType.CONTRACT:
                result = await self._run_contract_test(test_case)
            elif test_case.test_type == TestType.PERFORMANCE:
                result = await self._run_performance_test(test_case)
            elif test_case.test_type == TestType.CHAOS:
                result = await self._run_chaos_test(test_case)
            elif test_case.test_type == TestType.END_TO_END:
                result = await self._run_end_to_end_test(test_case)
            else:
                raise ValueError(f"Unsupported test type: {test_case.test_type}")
            
            execution.status = TestStatus.PASSED if result.get("success", False) else TestStatus.FAILED
            execution.result = result
            
        except Exception as e:
            execution.status = TestStatus.ERROR
            execution.error_message = str(e)
            logger.error(f"Test case {test_case.test_id} failed with error: {e}")
        
        finally:
            # Teardown phase
            try:
                await self._run_teardown_steps(test_case.teardown_steps)
            except Exception as e:
                logger.error(f"Teardown failed for test {test_case.test_id}: {e}")
            
            execution.completed_at = datetime.utcnow()
            execution.duration = (execution.completed_at - execution.started_at).total_seconds()
        
        return execution
    
    async def _run_setup_steps(self, setup_steps: List[str]):
        """Run setup steps for a test"""
        for step in setup_steps:
            logger.info(f"Setup step: {step}")
            # Implement setup step execution logic
    
    async def _run_teardown_steps(self, teardown_steps: List[str]):
        """Run teardown steps for a test"""
        for step in teardown_steps:
            logger.info(f"Teardown step: {step}")
            # Implement teardown step execution logic
    
    async def _run_integration_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run integration test"""
        results = []
        
        # Test inter-module communication
        for module_id in test_case.modules_under_test:
            if module_id in self.module_endpoints:
                endpoint = self.module_endpoints[module_id]
                
                # Test health endpoint
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(f"{endpoint.base_url}{endpoint.health_endpoint}")
                        results.append({
                            "module": module_id,
                            "test": "health_check",
                            "success": response.status_code == 200,
                            "response_time": response.elapsed.total_seconds(),
                            "status_code": response.status_code
                        })
                
                except Exception as e:
                    results.append({
                        "module": module_id,
                        "test": "health_check",
                        "success": False,
                        "error": str(e)
                    })
        
        # Overall success based on all modules being healthy
        overall_success = all(r.get("success", False) for r in results)
        
        return {
            "success": overall_success,
            "results": results
        }
    
    async def _run_contract_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run contract test"""
        # Find relevant contract tests
        relevant_contracts = [
            ct for ct in self.contract_tests
            if ct.provider in test_case.modules_under_test or ct.consumer in test_case.modules_under_test
        ]
        
        results = []
        for contract_test in relevant_contracts:
            if contract_test.provider in self.module_endpoints:
                provider_endpoint = self.module_endpoints[contract_test.provider]
                success = await contract_test.validate_contract(provider_endpoint)
                
                results.append({
                    "contract": contract_test.test_id,
                    "provider": contract_test.provider,
                    "consumer": contract_test.consumer,
                    "success": success
                })
        
        overall_success = all(r.get("success", False) for r in results)
        
        return {
            "success": overall_success,
            "contract_results": results
        }
    
    async def _run_performance_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run performance test"""
        # Find relevant performance tests
        relevant_perf_tests = [
            pt for pt in self.performance_tests
            if pt.target_endpoint.module_id in test_case.modules_under_test
        ]
        
        results = []
        for perf_test in relevant_perf_tests:
            # Simple load test scenario
            async def simple_scenario(user_id: int, endpoint: ModuleEndpoint):
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{endpoint.base_url}{endpoint.health_endpoint}")
                    response.raise_for_status()
            
            metrics = await perf_test.run_load_test(simple_scenario)
            results.append({
                "test": perf_test.name,
                "metrics": metrics
            })
        
        # Check if performance meets criteria
        success = True
        for result in results:
            metrics = result.get("metrics", {})
            if metrics.get("success_rate", 0) < 95:  # 95% success rate threshold
                success = False
            if metrics.get("avg_response_time", 0) > 2.0:  # 2 second response time threshold
                success = False
        
        return {
            "success": success,
            "performance_results": results
        }
    
    async def _run_chaos_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run chaos test"""
        # Find relevant chaos tests
        relevant_chaos_tests = [
            ct for ct in self.chaos_tests
            if any(module in ct.target_modules for module in test_case.modules_under_test)
        ]
        
        results = []
        for chaos_test in relevant_chaos_tests:
            try:
                # Run chaos experiment
                await chaos_test.simulate_high_cpu_load("test_container", 30)
                
                # Verify system resilience
                recovery_success = await self._verify_system_recovery(test_case.modules_under_test)
                
                results.append({
                    "chaos_test": chaos_test.name,
                    "success": recovery_success
                })
                
            except Exception as e:
                results.append({
                    "chaos_test": chaos_test.name,
                    "success": False,
                    "error": str(e)
                })
        
        overall_success = all(r.get("success", False) for r in results)
        
        return {
            "success": overall_success,
            "chaos_results": results
        }
    
    async def _run_end_to_end_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run end-to-end test"""
        # Simulate complete workflow across modules
        workflow_steps = []
        
        try:
            # Step 1: Create workflow in orchestrator
            if "orchestrator" in self.module_endpoints:
                orchestrator_endpoint = self.module_endpoints["orchestrator"]
                
                async with httpx.AsyncClient() as client:
                    workflow_response = await client.post(
                        f"{orchestrator_endpoint.base_url}/research/create",
                        json={"topic": "Test Research Topic"}
                    )
                    workflow_response.raise_for_status()
                    
                    workflow_data = workflow_response.json()
                    workflow_id = workflow_data.get("workflow_id")
                    
                    workflow_steps.append({
                        "step": "create_workflow",
                        "success": True,
                        "workflow_id": workflow_id
                    })
                    
                    # Step 2: Execute workflow
                    if workflow_id:
                        execution_response = await client.post(
                            f"{orchestrator_endpoint.base_url}/workflows/{workflow_id}/execute",
                            json={"initial_message": "Start research on AutoGen testing"}
                        )
                        execution_response.raise_for_status()
                        
                        execution_data = execution_response.json()
                        execution_id = execution_data.get("execution_id")
                        
                        workflow_steps.append({
                            "step": "execute_workflow",
                            "success": True,
                            "execution_id": execution_id
                        })
                        
                        # Step 3: Monitor execution (simplified)
                        await asyncio.sleep(10)  # Wait for execution
                        
                        status_response = await client.get(
                            f"{orchestrator_endpoint.base_url}/executions/{execution_id}"
                        )
                        status_response.raise_for_status()
                        
                        workflow_steps.append({
                            "step": "monitor_execution",
                            "success": True,
                            "status": status_response.json().get("status")
                        })
            
            overall_success = all(step.get("success", False) for step in workflow_steps)
            
            return {
                "success": overall_success,
                "workflow_steps": workflow_steps
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "workflow_steps": workflow_steps
            }
    
    async def _verify_system_recovery(self, modules: List[str]) -> bool:
        """Verify system recovery after chaos experiment"""
        recovery_time = 60  # seconds
        check_interval = 5  # seconds
        
        for attempt in range(recovery_time // check_interval):
            all_healthy = True
            
            for module_id in modules:
                if module_id in self.module_endpoints:
                    endpoint = self.module_endpoints[module_id]
                    
                    try:
                        async with httpx.AsyncClient() as client:
                            response = await client.get(
                                f"{endpoint.base_url}{endpoint.health_endpoint}",
                                timeout=5
                            )
                            if response.status_code != 200:
                                all_healthy = False
                                break
                    
                    except Exception:
                        all_healthy = False
                        break
            
            if all_healthy:
                return True
            
            await asyncio.sleep(check_interval)
        
        return False
    
    async def run_all_contract_tests(self) -> Dict[str, Any]:
        """Run all contract tests"""
        results = []
        
        for contract_test in self.contract_tests:
            if contract_test.provider in self.module_endpoints:
                provider_endpoint = self.module_endpoints[contract_test.provider]
                success = await contract_test.validate_contract(provider_endpoint)
                
                results.append({
                    "contract_id": contract_test.test_id,
                    "provider": contract_test.provider,
                    "consumer": contract_test.consumer,
                    "success": success
                })
        
        total_contracts = len(results)
        passed_contracts = sum(1 for r in results if r["success"])
        
        return {
            "total_contracts": total_contracts,
            "passed_contracts": passed_contracts,
            "success_rate": (passed_contracts / total_contracts) * 100 if total_contracts > 0 else 0,
            "results": results
        }

# Test Configuration Loader
class TestConfigLoader:
    @staticmethod
    async def load_test_config(config_path: str) -> Dict[str, Any]:
        """Load test configuration from YAML file"""
        async with aiofiles.open(config_path, 'r') as file:
            content = await file.read()
            return yaml.safe_load(content)
    
    @staticmethod
    def create_test_suite_from_config(config: Dict[str, Any]) -> TestSuite:
        """Create test suite from configuration"""
        test_cases = []
        
        for test_config in config.get("test_cases", []):
            test_case = TestCase(
                test_id=test_config["test_id"],
                name=test_config["name"],
                description=test_config["description"],
                test_type=TestType(test_config["test_type"]),
                severity=SeverityLevel(test_config["severity"]),
                modules_under_test=test_config["modules_under_test"],
                dependencies=test_config.get("dependencies", []),
                timeout=test_config.get("timeout", 60),
                setup_steps=test_config.get("setup_steps", []),
                teardown_steps=test_config.get("teardown_steps", []),
                expected_results=test_config.get("expected_results", {}),
                metadata=test_config.get("metadata", {})
            )
            test_cases.append(test_case)
        
        return TestSuite(
            suite_id=config["suite_id"],
            name=config["name"],
            description=config["description"],
            test_cases=test_cases,
            parallel_execution=config.get("parallel_execution", False),
            timeout=config.get("timeout", 300),
            metadata=config.get("metadata", {})
        )

# FastAPI Application for Test Management
def create_testing_app(test_runner: IntegrationTestRunner) -> FastAPI:
    app = FastAPI(title="Integration Testing Framework", version="1.0.0")
    
    class RunTestSuiteRequest(BaseModel):
        suite_id: str
    
    class TestSuiteCreateRequest(BaseModel):
        name: str
        description: str
        test_cases: List[Dict[str, Any]]
        parallel_execution: bool = False
        timeout: int = 300
    
    @app.post("/test-suites/create")
    async def create_test_suite(request: TestSuiteCreateRequest):
        suite_id = str(uuid.uuid4())
        
        test_cases = []
        for tc_data in request.test_cases:
            test_case = TestCase(
                test_id=tc_data["test_id"],
                name=tc_data["name"],
                description=tc_data["description"],
                test_type=TestType(tc_data["test_type"]),
                severity=SeverityLevel(tc_data["severity"]),
                modules_under_test=tc_data["modules_under_test"]
            )
            test_cases.append(test_case)
        
        test_suite = TestSuite(
            suite_id=suite_id,
            name=request.name,
            description=request.description,
            test_cases=test_cases,
            parallel_execution=request.parallel_execution,
            timeout=request.timeout
        )
        
        test_runner.register_test_suite(test_suite)
        
        return {"suite_id": suite_id, "status": "created"}
    
    @app.post("/test-suites/run")
    async def run_test_suite(request: RunTestSuiteRequest):
        try:
            results = await test_runner.run_test_suite(request.suite_id)
            return results
        except ValueError as e:
            raise HTTPException(status_code=404, detail=str(e))
    
    @app.post("/contracts/run-all")
    async def run_all_contracts():
        results = await test_runner.run_all_contract_tests()
        return results
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
    
    return app

# Example usage
if __name__ == "__main__":
    # Define module endpoints
    module_endpoints = {
        "memory": ModuleEndpoint(
            module_id="memory",
            base_url="http://localhost:8001",
            health_endpoint="/health"
        ),
        "orchestrator": ModuleEndpoint(
            module_id="orchestrator",
            base_url="http://localhost:8002",
            health_endpoint="/health"
        ),
        "communication": ModuleEndpoint(
            module_id="communication",
            base_url="http://localhost:8003",
            health_endpoint="/health"
        )
    }
    
    # Create test runner
    test_runner = IntegrationTestRunner(module_endpoints)
    
    # Add some contract tests
    contract_test = ContractTest(
        provider="memory",
        consumer="orchestrator",
        interaction={
            "request": {
                "path": "/memory/store",
                "method": "POST",
                "headers": {"Content-Type": "application/json"},
                "body": {
                    "session_id": "test",
                    "agent_id": "test_agent",
                    "memory_type": "conversation",
                    "content": "test content"
                }
            },
            "response": {
                "status": 200,
                "body_schema": {
                    "type": "object",
                    "required": ["memory_id", "status"],
                    "properties": {
                        "memory_id": {"type": "string"},
                        "status": {"type": "string"}
                    }
                }
            }
        }
    )
    test_runner.add_contract_test(contract_test)
    
    # Create FastAPI app
    app = create_testing_app(test_runner)
    
    uvicorn.run(app, host="0.0.0.0", port=8004)
import httpx
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import re
from dataclasses import dataclass
from enum import Enum


class VideoQuality(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class YouTubeVideo:
    video_id: str
    title: str
    description: str
    channel_title: str
    published_at: datetime
    duration: str
    view_count: int
    like_count: int
    comment_count: int
    tags: List[str]
    category_id: str
    language: str
    thumbnail_url: str


@dataclass
class VideoTranscript:
    video_id: str
    language: str
    transcript_segments: List[Dict[str, Any]]
    total_duration: float
    word_count: int
    extracted_at: datetime
    confidence_score: float


@dataclass
class VideoComment:
    comment_id: str
    author: str
    text: str
    like_count: int
    published_at: datetime
    reply_count: int


class YouTubeClient:
    """Professional YouTube API client with transcript and comment analysis"""

    def __init__(self, api_key: str, timeout: int = 30):
        self.api_key = api_key
        self.timeout = timeout
        self.base_url = "https://www.googleapis.com/youtube/v3"

        # Configure HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            headers={
                "User-Agent": "Research-Synthesis-Platform/1.0"
            }
        )

        # Rate limiting (YouTube API quota: 10,000 units/day)
        self.quota_used = 0
        self.daily_quota_limit = 10000

    async def analyze_video(self, video_url: str) -> Dict[str, Any]:
        """Comprehensive YouTube video analysis"""
        try:
            # Extract video ID from URL
            video_id = self._extract_video_id(video_url)

            # Get video metadata
            video_info = await self._get_video_info(video_id)

            # Extract transcript
            transcript = await self._extract_transcript(video_id)

            # Get comments
            comments = await self._get_video_comments(video_id, limit=50)

            # Analyze content
            content_analysis = await self._analyze_video_content(video_info, transcript, comments)

            # Compile comprehensive analysis
            analysis = {
                "video": video_info,
                "transcript": transcript,
                "comments": comments,
                "content_analysis": content_analysis,
                "analysis_metadata": {
                    "analyzed_at": datetime.utcnow().isoformat(),
                    "quota_used": self.quota_used,
                    "analysis_quality": self._assess_analysis_quality(video_info, transcript, comments)
                }
            }

            return analysis

        except Exception as e:
            raise Exception(f"Failed to analyze video {video_url}: {str(e)}")

    async def _get_video_info(self, video_id: str) -> YouTubeVideo:
        """Get comprehensive video metadata"""

        params = {
            "part": "snippet,statistics,contentDetails,status",
            "id": video_id,
            "key": self.api_key
        }

        response = await self._make_api_call("/videos", params)
        data = response.json()

        if not data.get("items"):
            raise Exception(f"Video {video_id} not found or not accessible")

        video_data = data["items"][0]
        snippet = video_data["snippet"]
        statistics = video_data["statistics"]
        content_details = video_data["contentDetails"]

        return YouTubeVideo(
            video_id=video_id,
            title=snippet["title"],
            description=snippet["description"],
            channel_title=snippet["channelTitle"],
            published_at=datetime.fromisoformat(
                snippet["publishedAt"].replace("Z", "+00:00")),
            duration=content_details["duration"],
            view_count=int(statistics.get("viewCount", 0)),
            like_count=int(statistics.get("likeCount", 0)),
            comment_count=int(statistics.get("commentCount", 0)),
            tags=snippet.get("tags", []),
            category_id=snippet.get("categoryId", ""),
            language=snippet.get("defaultLanguage", "en"),
            thumbnail_url=snippet["thumbnails"]["maxres"]["url"] if "maxres" in snippet[
                "thumbnails"] else snippet["thumbnails"]["high"]["url"]
        )

    async def _extract_transcript(self, video_id: str) -> Optional[VideoTranscript]:
        """Extract video transcript using YouTube's captions"""

        try:
            # Get caption tracks
            captions = await self._get_caption_tracks(video_id)

            if not captions:
                # Try alternative transcript extraction
                return await self._extract_auto_generated_captions(video_id)

            # Use the best available caption track
            best_caption = self._select_best_caption_track(captions)

            if best_caption:
                transcript_content = await self._download_caption_track(best_caption["id"])
                return self._process_transcript_content(video_id, transcript_content)

        except Exception as e:
            print(f"Error extracting transcript for {video_id}: {e}")

        return None

    async def _get_caption_tracks(self, video_id: str) -> List[Dict[str, Any]]:
        """Get available caption tracks for video"""

        params = {
            "part": "snippet",
            "videoId": video_id,
            "key": self.api_key
        }

        try:
            response = await self._make_api_call("/captions", params)
            data = response.json()

            captions = []
            for item in data.get("items", []):
                captions.append({
                    "id": item["id"],
                    "language": item["snippet"]["language"],
                    "name": item["snippet"]["name"],
                    "track_kind": item["snippet"]["trackKind"],
                    "is_auto_generated": item["snippet"].get("isAutoGenerated", False)
                })

            return captions

        except Exception as e:
            print(f"Error getting caption tracks: {e}")
            return []

    async def _extract_auto_generated_captions(self, video_id: str) -> Optional[VideoTranscript]:
        """Extract auto-generated captions as fallback"""

        # This is a simplified implementation
        # In production, you might use youtube-transcript-api or similar libraries
        # For Sprint 2, we'll simulate transcript extraction

        # Simulate transcript extraction
        await asyncio.sleep(1)  # Simulate processing time

        # Generate simulated transcript segments
        simulated_segments = [
            {"start": 0.0, "duration": 5.0,
                "text": "Welcome to this tutorial about AutoGen framework"},
            {"start": 5.0, "duration": 8.0,
                "text": "AutoGen is a powerful multi-agent conversation framework"},
            {"start": 13.0, "duration": 6.0,
                "text": "It enables complex agent interactions and workflows"},
            {"start": 19.0, "duration": 7.0,
                "text": "In this video, we'll explore its key features and capabilities"}
        ]

        total_duration = sum(segment["duration"]
                             for segment in simulated_segments)
        word_count = sum(len(segment["text"].split())
                         for segment in simulated_segments)

        return VideoTranscript(
            video_id=video_id,
            language="en",
            transcript_segments=simulated_segments,
            total_duration=total_duration,
            word_count=word_count,
            extracted_at=datetime.utcnow(),
            confidence_score=0.85  # Simulated confidence
        )

    async def _get_video_comments(self, video_id: str, limit: int = 50) -> List[VideoComment]:
        """Get video comments for analysis"""

        params = {
            "part": "snippet,replies",
            "videoId": video_id,
            "maxResults": min(limit, 100),
            "order": "relevance",
            "key": self.api_key
        }

        try:
            response = await self._make_api_call("/commentThreads", params)
            data = response.json()

            comments = []
            for item in data.get("items", []):
                comment_data = item["snippet"]["topLevelComment"]["snippet"]

                comment = VideoComment(
                    comment_id=item["id"],
                    author=comment_data["authorDisplayName"],
                    text=comment_data["textDisplay"],
                    like_count=int(comment_data.get("likeCount", 0)),
                    published_at=datetime.fromisoformat(
                        comment_data["publishedAt"].replace("Z", "+00:00")),
                    reply_count=int(item["snippet"].get("totalReplyCount", 0))
                )

                comments.append(comment)

            return comments

        except Exception as e:
            print(f"Error getting comments for {video_id}: {e}")
            return []

    async def _analyze_video_content(
        self,
        video: YouTubeVideo,
        transcript: Optional[VideoTranscript],
        comments: List[VideoComment]
    ) -> Dict[str, Any]:
        """Analyze video content for insights"""

        analysis = {
            "engagement_metrics": self._calculate_engagement_metrics(video),
            "content_topics": self._extract_content_topics(video, transcript),
            "audience_sentiment": self._analyze_audience_sentiment(comments),
            "educational_value": self._assess_educational_value(video, transcript),
            "technical_content": self._identify_technical_content(video, transcript)
        }

        return analysis

    def _calculate_engagement_metrics(self, video: YouTubeVideo) -> Dict[str, Any]:
        """Calculate video engagement metrics"""

        # Engagement rate calculation
        total_engagements = video.like_count + video.comment_count
        engagement_rate = (total_engagements / video.view_count *
                           100) if video.view_count > 0 else 0

        # View velocity (views per day since publication)
        days_since_publish = (datetime.utcnow() - video.published_at).days
        view_velocity = video.view_count / max(days_since_publish, 1)

        return {
            "engagement_rate": round(engagement_rate, 2),
            "view_velocity": round(view_velocity, 0),
            "likes_per_view": round(video.like_count / video.view_count * 100, 4) if video.view_count > 0 else 0,
            "comments_per_view": round(video.comment_count / video.view_count * 100, 4) if video.view_count > 0 else 0,
            "total_engagements": total_engagements
        }

    def _extract_content_topics(self, video: YouTubeVideo, transcript: Optional[VideoTranscript]) -> List[str]:
        """Extract main topics from video content"""

        topics = []

        # Extract from title and description
        title_words = video.title.lower().split()
        desc_words = video.description.lower().split()[:100]  # First 100 words

        # Common technical terms and topics
        technical_keywords = [
            "autogen", "ai", "machine learning", "python", "framework",
            "agent", "automation", "api", "tutorial", "development",
            "programming", "software", "technology", "github", "coding"
        ]

        for keyword in technical_keywords:
            if keyword in title_words or keyword in desc_words:
                topics.append(keyword)

        # Add video tags
        topics.extend([tag.lower()
                      for tag in video.tags[:10]])  # First 10 tags

        # Extract from transcript if available
        if transcript:
            transcript_text = " ".join(
                [segment["text"].lower() for segment in transcript.transcript_segments])
            for keyword in technical_keywords:
                if keyword in transcript_text and keyword not in topics:
                    topics.append(keyword)

        return list(set(topics))[:15]  # Return unique topics, max 15

    def _analyze_audience_sentiment(self, comments: List[VideoComment]) -> Dict[str, Any]:
        """Analyze sentiment in video comments"""

        if not comments:
            return {"sentiment": "neutral", "confidence": 0.0}

        # Simple sentiment analysis based on keywords
        positive_keywords = ["great", "awesome", "helpful",
                             "thanks", "excellent", "good", "love", "amazing"]
        negative_keywords = ["bad", "terrible", "waste",
                             "boring", "useless", "wrong", "stupid", "hate"]
        question_keywords = ["how", "what", "where",
                             "when", "why", "can", "could", "would", "?"]

        sentiment_scores = []
        question_count = 0

        for comment in comments:
            text = comment.text.lower()
            score = 0

            # Count positive and negative words
            positive_count = sum(
                1 for word in positive_keywords if word in text)
            negative_count = sum(
                1 for word in negative_keywords if word in text)
            has_question = any(word in text for word in question_keywords)

            if has_question:
                question_count += 1

            # Calculate sentiment score
            score = positive_count - negative_count

            # Weight by like count
            weighted_score = score * (1 + comment.like_count * 0.1)
            sentiment_scores.append(weighted_score)

        # Calculate overall sentiment
        avg_sentiment = sum(sentiment_scores) / \
            len(sentiment_scores) if sentiment_scores else 0

        if avg_sentiment > 0.5:
            sentiment = "positive"
        elif avg_sentiment < -0.5:
            sentiment = "negative"
        else:
            sentiment = "neutral"

        return {
            "sentiment": sentiment,
            "confidence": min(abs(avg_sentiment), 1.0),
            "positive_comments": len([s for s in sentiment_scores if s > 0]),
            "negative_comments": len([s for s in sentiment_scores if s < 0]),
            "question_count": question_count,
            "total_comments_analyzed": len(comments)
        }

    def _assess_educational_value(self, video: YouTubeVideo, transcript: Optional[VideoTranscript]) -> Dict[str, Any]:
        """Assess educational value of the video"""

        educational_indicators = {
            "has_tutorial_keywords": False,
            "has_explanation_content": False,
            "has_demonstration": False,
            "structured_content": False,
            "technical_depth": "low"
        }

        # Check title and description for educational keywords
        text_content = f"{video.title} {video.description}".lower()

        tutorial_keywords = ["tutorial", "how to", "guide",
                             "learn", "explained", "introduction", "beginner"]
        explanation_keywords = ["because", "therefore",
                                "example", "demonstrate", "show", "explain"]

        educational_indicators["has_tutorial_keywords"] = any(
            keyword in text_content for keyword in tutorial_keywords)
        educational_indicators["has_explanation_content"] = any(
            keyword in text_content for keyword in explanation_keywords)

        # Analyze transcript if available
        if transcript:
            transcript_text = " ".join(
                [segment["text"].lower() for segment in transcript.transcript_segments])

            # Check for structured content (step-by-step)
            step_indicators = ["first", "second",
                               "next", "then", "finally", "step"]
            educational_indicators["structured_content"] = sum(
                1 for word in step_indicators if word in transcript_text) >= 3

            # Assess technical depth
            technical_terms = ["function", "class", "method",
                               "variable", "parameter", "algorithm", "implementation"]
            technical_count = sum(
                1 for term in technical_terms if term in transcript_text)

            if technical_count >= 5:
                educational_indicators["technical_depth"] = "high"
            elif technical_count >= 2:
                educational_indicators["technical_depth"] = "medium"

        # Calculate educational score
        score = 0
        if educational_indicators["has_tutorial_keywords"]:
            score += 2
        if educational_indicators["has_explanation_content"]:
            score += 2
        if educational_indicators["structured_content"]:
            score += 2
        if educational_indicators["technical_depth"] == "high":
            score += 3
        elif educational_indicators["technical_depth"] == "medium":
            score += 1

        educational_indicators["educational_score"] = min(
            score / 9, 1.0)  # Normalize to 0-1

        return educational_indicators

    def _identify_technical_content(self, video: YouTubeVideo, transcript: Optional[VideoTranscript]) -> Dict[str, Any]:
        """Identify technical content and concepts"""

        technical_analysis = {
            "programming_languages": [],
            "frameworks_mentioned": [],
            "concepts_covered": [],
            "tools_mentioned": []
        }

        # Programming languages
        programming_languages = ["python", "javascript",
                                 "java", "c++", "go", "rust", "typescript"]
        frameworks = ["autogen", "langchain", "tensorflow",
                      "pytorch", "react", "node", "django", "flask"]
        tools = ["github", "docker", "kubernetes", "git", "vscode", "jupyter"]
        concepts = ["api", "machine learning", "artificial intelligence",
                    "algorithm", "database", "web development"]

        # Analyze content
        content_text = f"{video.title} {video.description}".lower()

        if transcript:
            content_text += " " + \
                " ".join([segment["text"].lower()
                         for segment in transcript.transcript_segments])

        # Identify mentioned technologies
        for lang in programming_languages:
            if lang in content_text:
                technical_analysis["programming_languages"].append(lang)

        for framework in frameworks:
            if framework in content_text:
                technical_analysis["frameworks_mentioned"].append(framework)

        for tool in tools:
            if tool in content_text:
                technical_analysis["tools_mentioned"].append(tool)

        for concept in concepts:
            if concept in content_text:
                technical_analysis["concepts_covered"].append(concept)

        return technical_analysis

    async def _make_api_call(self, endpoint: str, params: Dict[str, Any]) -> httpx.Response:
        """Make YouTube API call with quota tracking"""

        url = f"{self.base_url}{endpoint}"

        # Estimate quota cost (simplified)
        quota_cost = 1
        if endpoint == "/videos":
            quota_cost = 1
        elif endpoint == "/commentThreads":
            quota_cost = 1
        elif endpoint == "/captions":
            quota_cost = 50

        # Check quota limit
        if self.quota_used + quota_cost > self.daily_quota_limit:
            raise Exception("YouTube API daily quota limit reached")

        response = await self.client.get(url, params=params)
        response.raise_for_status()

        # Update quota usage
        self.quota_used += quota_cost

        return response

    def _extract_video_id(self, video_url: str) -> str:
        """Extract video ID from YouTube URL"""

        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})',
            r'youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})'
        ]

        for pattern in patterns:
            match = re.search(pattern, video_url)
            if match:
                return match.group(1)

        raise ValueError(f"Invalid YouTube URL: {video_url}")

    def _select_best_caption_track(self, captions: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Select the best caption track for transcript extraction"""

        if not captions:
            return None

        # Prefer manual captions over auto-generated
        manual_captions = [cap for cap in captions if not cap.get(
            "is_auto_generated", False)]
        if manual_captions:
            # Prefer English, then any language
            english_captions = [
                cap for cap in manual_captions if cap["language"] == "en"]
            return english_captions[0] if english_captions else manual_captions[0]

        # Fall back to auto-generated
        auto_captions = [cap for cap in captions if cap.get(
            "is_auto_generated", False)]
        english_auto = [
            cap for cap in auto_captions if cap["language"] == "en"]
        return english_auto[0] if english_auto else (auto_captions[0] if auto_captions else None)

    async def _download_caption_track(self, caption_id: str) -> str:
        """Download caption track content"""

        params = {
            "id": caption_id,
            "tfmt": "srt",  # SubRip format
            "key": self.api_key
        }

        response = await self._make_api_call(f"/captions/{caption_id}", params)
        return response.text

    def _process_transcript_content(self, video_id: str, srt_content: str) -> VideoTranscript:
        """Process SRT subtitle content into structured transcript"""

        # Parse SRT format
        segments = []
        srt_blocks = srt_content.strip().split('\n\n')

        for block in srt_blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # Parse timestamp
                timestamp_line = lines[1]
                times = timestamp_line.split(' --> ')
                if len(times) == 2:
                    start_time = self._parse_srt_timestamp(times[0])
                    end_time = self._parse_srt_timestamp(times[1])

                    # Extract text
                    text = ' '.join(lines[2:])

                    segments.append({
                        "start": start_time,
                        "duration": end_time - start_time,
                        "text": text
                    })

        total_duration = max([seg["start"] + seg["duration"]
                             for seg in segments]) if segments else 0
        word_count = sum(len(seg["text"].split()) for seg in segments)

        return VideoTranscript(
            video_id=video_id,
            language="en",  # Assuming English for now
            transcript_segments=segments,
            total_duration=total_duration,
            word_count=word_count,
            extracted_at=datetime.utcnow(),
            confidence_score=0.95  # High confidence for manual captions
        )

    def _parse_srt_timestamp(self, timestamp: str) -> float:
        """Parse SRT timestamp to seconds"""

        # Format: HH:MM:SS,mmm
        time_part, ms_part = timestamp.split(',')
        h, m, s = map(int, time_part.split(':'))
        ms = int(ms_part)

        return h * 3600 + m * 60 + s + ms / 1000

    def _assess_analysis_quality(
        self,
        video: YouTubeVideo,
        transcript: Optional[VideoTranscript],
        comments: List[VideoComment]
    ) -> float:
        """Assess quality of video analysis"""

        quality_score = 0.0

        # Basic video info always available
        quality_score += 0.3

        # Transcript availability
        if transcript:
            quality_score += 0.4
            if transcript.word_count > 100:
                quality_score += 0.1

        # Comments availability
        if comments:
            quality_score += 0.2
            if len(comments) >= 10:
                quality_score += 0.1

        return min(quality_score, 1.0)

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

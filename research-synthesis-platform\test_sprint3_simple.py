#!/usr/bin/env python3
"""
Simple Sprint 3 Test - Tests Sprint 3 modules without complex imports
"""

import asyncio
import httpx
import time
from datetime import datetime

async def test_module_health(module_name, port):
    """Test if a module is healthy"""
    url = f"http://localhost:{port}/health"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=5.0)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {module_name}: {data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ {module_name}: HTTP {response.status_code}")
                return False
    except Exception as e:
        print(f"⚠️  {module_name}: {str(e)}")
        return False

async def test_memory_module():
    """Test Memory State Management module"""
    print("\n🧠 Testing Memory State Management Module...")
    
    # Test health
    healthy = await test_module_health("Memory State Management", 8003)
    if not healthy:
        return False
    
    # Test memory storage
    try:
        async with httpx.AsyncClient() as client:
            # Store memory
            store_response = await client.post(
                "http://localhost:8003/memories",
                json={
                    "session_id": "test_session",
                    "agent_id": "test_agent",
                    "memory_type": "test",
                    "content": "Test memory content"
                },
                timeout=10.0
            )
            
            if store_response.status_code == 200:
                print("✅ Memory storage: SUCCESS")
                
                # Test memory query
                query_response = await client.post(
                    "http://localhost:8003/memories/query",
                    json={
                        "session_id": "test_session",
                        "limit": 10
                    },
                    timeout=10.0
                )
                
                if query_response.status_code == 200:
                    print("✅ Memory query: SUCCESS")
                    return True
                else:
                    print(f"❌ Memory query: HTTP {query_response.status_code}")
                    return False
            else:
                print(f"❌ Memory storage: HTTP {store_response.status_code}")
                return False
                
    except Exception as e:
        print(f"⚠️  Memory module test failed: {e}")
        return False

async def test_agent_module():
    """Test Core Agent Orchestration module"""
    print("\n🤖 Testing Core Agent Orchestration Module...")
    
    # Test health
    healthy = await test_module_health("Core Agent Orchestration", 8001)
    if not healthy:
        return False
    
    # Test agent creation
    try:
        async with httpx.AsyncClient() as client:
            # Create agent
            create_response = await client.post(
                "http://localhost:8001/agents",
                json={
                    "name": "TestAgent",
                    "agent_type": "research_coordinator",
                    "system_message": "Test agent for integration testing"
                },
                timeout=10.0
            )
            
            if create_response.status_code == 200:
                print("✅ Agent creation: SUCCESS")
                
                # Test agent listing
                list_response = await client.get(
                    "http://localhost:8001/agents",
                    timeout=10.0
                )
                
                if list_response.status_code == 200:
                    print("✅ Agent listing: SUCCESS")
                    return True
                else:
                    print(f"❌ Agent listing: HTTP {list_response.status_code}")
                    return False
            else:
                print(f"❌ Agent creation: HTTP {create_response.status_code}")
                return False
                
    except Exception as e:
        print(f"⚠️  Agent module test failed: {e}")
        return False

async def test_research_module():
    """Test Research Synthesis module"""
    print("\n🔬 Testing Research Synthesis Module...")
    
    # Test health
    healthy = await test_module_health("Research Synthesis", 8004)
    if not healthy:
        return False
    
    # Test research start
    try:
        async with httpx.AsyncClient() as client:
            # Start research
            research_response = await client.post(
                "http://localhost:8004/research",
                json={
                    "topic": "Test Research Topic",
                    "sources": [
                        {
                            "source_id": "test_source_1",
                            "source_type": "web_search",
                            "url": "https://example.com",
                            "title": "Test Source",
                            "content": "This is test content for research synthesis testing."
                        }
                    ]
                },
                timeout=10.0
            )
            
            if research_response.status_code == 200:
                print("✅ Research start: SUCCESS")
                return True
            else:
                print(f"❌ Research start: HTTP {research_response.status_code}")
                return False
                
    except Exception as e:
        print(f"⚠️  Research module test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Sprint 3 Simple Integration Test")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test all modules
    results = []
    
    print("\n📡 Testing Module Health Checks...")
    memory_health = await test_module_health("Memory State Management", 8003)
    agent_health = await test_module_health("Core Agent Orchestration", 8001)
    research_health = await test_module_health("Research Synthesis", 8004)
    
    if not any([memory_health, agent_health, research_health]):
        print("\n❌ No modules are running!")
        print("Please start the Sprint 3 modules first:")
        print("1. Memory State Management (port 8003)")
        print("2. Core Agent Orchestration (port 8001)")
        print("3. Research Synthesis (port 8004)")
        return False
    
    # Test functional endpoints if modules are running
    if memory_health:
        results.append(await test_memory_module())
    
    if agent_health:
        results.append(await test_agent_module())
    
    if research_health:
        results.append(await test_research_module())
    
    # Summary
    duration = time.time() - start_time
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"📈 Success Rate: {(passed/total*100) if total > 0 else 0:.1f}%")
    print(f"⏱️  Duration: {duration:.2f}s")
    
    if passed == total and total > 0:
        print("\n🎉 All Sprint 3 modules are working correctly!")
        return True
    else:
        print(f"\n⚠️  {total - passed} module(s) need attention")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

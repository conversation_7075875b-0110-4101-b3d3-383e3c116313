import httpx
import asyncio
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import json
from datetime import datetime
import time


class ModuleClient:
    """Client for inter-module communication"""
    
    def __init__(
        self,
        module_id: str,
        config_registry_url: str,
        api_key: str,
        timeout: int = 30
    ):
        self.module_id = module_id
        self.config_registry_url = config_registry_url
        self.api_key = api_key
        self.timeout = timeout
        self.service_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            headers={
                "X-API-Key": api_key,
                "X-Module-ID": module_id,
                "Content-Type": "application/json"
            }
        )
    
    async def discover_service(self, service_name: str, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """Discover service endpoint through service registry"""
        cache_key = f"service:{service_name}"
        
        # Check cache first
        if not force_refresh and cache_key in self.service_cache:
            cached_data = self.service_cache[cache_key]
            if time.time() - cached_data["cached_at"] < self.cache_ttl:
                return cached_data["service"]
        
        try:
            response = await self.client.get(f"{self.config_registry_url}/registry/services/{service_name}")
            response.raise_for_status()
            
            services = response.json()["data"]
            if services:
                # Select first healthy service (could implement load balancing here)
                service = services[0]
                
                # Cache the result
                self.service_cache[cache_key] = {
                    "service": service,
                    "cached_at": time.time()
                }
                
                return service
            
        except Exception as e:
            print(f"Failed to discover service {service_name}: {e}")
        
        return None
    
    async def call_module(
        self,
        target_module: str,
        operation: str,
        parameters: Dict[str, Any] = None,
        context: Dict[str, Any] = None,
        priority: int = 1,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """Make API call to another module"""
        
        # Discover target service
        service = await self.discover_service(target_module)
        if not service:
            raise Exception(f"Service {target_module} not found")
        
        # Prepare request
        request_data = {
            "request_id": f"{self.module_id}-{int(time.time() * 1000)}",
            "module_id": self.module_id,
            "operation": operation,
            "parameters": parameters or {},
            "context": context or {},
            "priority": priority,
            "timeout": timeout or self.timeout
        }
        
        # Make API call
        endpoint_url = f"{service['api_base_url']}/api/{operation}"
        
        try:
            start_time = time.time()
            response = await self.client.post(endpoint_url, json=request_data)
            execution_time = (time.time() - start_time) * 1000
            
            response.raise_for_status()
            result = response.json()
            
            # Add execution metadata
            result["execution_time_ms"] = execution_time
            
            return result
            
        except httpx.HTTPStatusError as e:
            error_detail = f"HTTP {e.response.status_code}: {e.response.text}"
            raise Exception(f"API call to {target_module}/{operation} failed: {error_detail}")
        
        except Exception as e:
            raise Exception(f"Failed to call {target_module}/{operation}: {str(e)}")
    
    async def register_self(
        self,
        name: str,
        version: str,
        host: str,
        port: int,
        capabilities: List[str],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Register this module with the service registry"""
        
        registration_data = {
            "service_id": f"{self.module_id}-{int(time.time())}",
            "name": name,
            "version": version,
            "host": host,
            "port": port,
            "health_check_url": f"http://{host}:{port}/health",
            "api_base_url": f"http://{host}:{port}",
            "capabilities": capabilities,
            "metadata": metadata or {}
        }
        
        try:
            response = await self.client.post(
                f"{self.config_registry_url}/registry/register",
                json=registration_data
            )
            response.raise_for_status()
            return response.json()["success"]
            
        except Exception as e:
            print(f"Failed to register service: {e}")
            return False
    
    async def send_heartbeat(self, service_id: str) -> bool:
        """Send heartbeat to service registry"""
        try:
            response = await self.client.post(
                f"{self.config_registry_url}/registry/heartbeat",
                json={"service_id": service_id}
            )
            response.raise_for_status()
            return response.json()["success"]
            
        except Exception as e:
            print(f"Failed to send heartbeat: {e}")
            return False
    
    async def get_config(self, key: Optional[str] = None) -> Dict[str, Any]:
        """Get configuration from config registry"""
        try:
            url = f"{self.config_registry_url}/config/{self.module_id}"
            if key:
                url += f"?key={key}"
                
            response = await self.client.get(url)
            response.raise_for_status()
            
            return response.json()["data"]
            
        except Exception as e:
            print(f"Failed to get config: {e}")
            return {}
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

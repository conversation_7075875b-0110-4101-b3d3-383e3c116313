# Inter-Module Communication Framework - Sprint 3 Implementation
# Based on inter_module_communication.py from folder 3

import asyncio
import logging
import json
import time
import random
import hashlib
import uuid
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
from contextlib import asynccontextmanager

# External dependencies with fallbacks
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    logging.warning("aiohttp not available")

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    logging.warning("httpx not available")

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    logging.warning("PyJWT not available")

try:
    from cryptography.fernet import Fernet
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    logging.warning("cryptography not available")

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("redis not available")

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Enums
class MessagePriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class ModuleStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"

# Data Classes
@dataclass
class ModuleInfo:
    """Information about a module"""
    module_id: str
    name: str
    version: str
    host: str
    port: int
    health_endpoint: str = "/health"
    status: ModuleStatus = ModuleStatus.OFFLINE
    last_heartbeat: Optional[datetime] = None
    capabilities: List[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class InterModuleMessage:
    """Message for inter-module communication"""
    message_id: str
    source_module: str
    target_module: str
    message_type: str
    payload: Dict[str, Any]
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: datetime = None
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl_seconds: Optional[int] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    expected_exception: type = Exception
    name: str = "default"

# Circuit Breaker Implementation
class CircuitBreaker:
    """Circuit breaker for resilient inter-module communication"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.success_count = 0
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info(f"Circuit breaker {self.config.name} moved to HALF_OPEN")
            else:
                raise HTTPException(
                    status_code=503, 
                    detail=f"Circuit breaker {self.config.name} is OPEN"
                )
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            await self._on_success()
            return result
        except self.config.expected_exception as e:
            await self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if self.last_failure_time is None:
            return True
        
        time_since_failure = time.time() - self.last_failure_time
        return time_since_failure >= self.config.recovery_timeout
    
    async def _on_success(self):
        """Handle successful call"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= 3:  # Require 3 successes to close
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.success_count = 0
                logger.info(f"Circuit breaker {self.config.name} moved to CLOSED")
        else:
            self.failure_count = 0
    
    async def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker {self.config.name} moved to OPEN")

# Retry Mechanism
class RetryConfig:
    """Configuration for retry mechanism"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_base: float = 2.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base

async def retry_with_backoff(func: Callable, config: RetryConfig, *args, **kwargs):
    """Execute function with exponential backoff retry"""
    last_exception = None
    
    for attempt in range(config.max_attempts):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            
            if attempt == config.max_attempts - 1:
                break
            
            delay = min(
                config.base_delay * (config.exponential_base ** attempt),
                config.max_delay
            )
            
            # Add jitter
            jitter = random.uniform(0, 0.1) * delay
            total_delay = delay + jitter
            
            logger.warning(f"Attempt {attempt + 1} failed, retrying in {total_delay:.2f}s: {e}")
            await asyncio.sleep(total_delay)
    
    raise last_exception

# Module Registry
class ModuleRegistry:
    """Registry for tracking available modules"""
    
    def __init__(self):
        self.modules: Dict[str, ModuleInfo] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
    
    async def register_module(self, module_info: ModuleInfo):
        """Register a module"""
        self.modules[module_info.module_id] = module_info
        
        # Create circuit breaker for module
        circuit_config = CircuitBreakerConfig(name=f"circuit_{module_info.module_id}")
        self.circuit_breakers[module_info.module_id] = CircuitBreaker(circuit_config)
        
        logger.info(f"Registered module: {module_info.name} ({module_info.module_id})")
    
    async def unregister_module(self, module_id: str):
        """Unregister a module"""
        if module_id in self.modules:
            module_name = self.modules[module_id].name
            del self.modules[module_id]
            if module_id in self.circuit_breakers:
                del self.circuit_breakers[module_id]
            logger.info(f"Unregistered module: {module_name} ({module_id})")
    
    async def get_module(self, module_id: str) -> Optional[ModuleInfo]:
        """Get module information"""
        return self.modules.get(module_id)
    
    async def list_modules(self) -> List[ModuleInfo]:
        """List all registered modules"""
        return list(self.modules.values())
    
    async def find_modules_by_capability(self, capability: str) -> List[ModuleInfo]:
        """Find modules that have a specific capability"""
        return [
            module for module in self.modules.values()
            if capability in module.capabilities
        ]
    
    async def update_module_status(self, module_id: str, status: ModuleStatus):
        """Update module status"""
        if module_id in self.modules:
            self.modules[module_id].status = status
            self.modules[module_id].last_heartbeat = datetime.utcnow()

# HTTP Client for Inter-Module Communication
class InterModuleClient:
    """HTTP client for inter-module communication"""
    
    def __init__(self, module_registry: ModuleRegistry, auth_token: Optional[str] = None):
        self.module_registry = module_registry
        self.auth_token = auth_token
        self.retry_config = RetryConfig()
    
    async def send_message(self, message: InterModuleMessage) -> Dict[str, Any]:
        """Send message to another module"""
        target_module = await self.module_registry.get_module(message.target_module)
        if not target_module:
            raise HTTPException(
                status_code=404, 
                detail=f"Target module {message.target_module} not found"
            )
        
        if target_module.status == ModuleStatus.OFFLINE:
            raise HTTPException(
                status_code=503, 
                detail=f"Target module {message.target_module} is offline"
            )
        
        # Get circuit breaker for target module
        circuit_breaker = self.module_registry.circuit_breakers.get(message.target_module)
        
        if circuit_breaker:
            return await circuit_breaker.call(self._send_http_request, target_module, message)
        else:
            return await self._send_http_request(target_module, message)
    
    async def _send_http_request(self, target_module: ModuleInfo, message: InterModuleMessage) -> Dict[str, Any]:
        """Send HTTP request to target module"""
        url = f"http://{target_module.host}:{target_module.port}/inter-module/message"
        
        headers = {
            "Content-Type": "application/json",
            "X-Source-Module": message.source_module,
            "X-Message-ID": message.message_id
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        payload = {
            "message_id": message.message_id,
            "source_module": message.source_module,
            "target_module": message.target_module,
            "message_type": message.message_type,
            "payload": message.payload,
            "priority": message.priority.value,
            "timestamp": message.timestamp.isoformat(),
            "correlation_id": message.correlation_id,
            "reply_to": message.reply_to,
            "ttl_seconds": message.ttl_seconds
        }
        
        async def make_request():
            if HTTPX_AVAILABLE:
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, json=payload, headers=headers, timeout=30.0)
                    response.raise_for_status()
                    return response.json()
            elif AIOHTTP_AVAILABLE:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=payload, headers=headers) as response:
                        response.raise_for_status()
                        return await response.json()
            else:
                raise HTTPException(status_code=500, detail="No HTTP client available")
        
        return await retry_with_backoff(make_request, self.retry_config)
    
    async def health_check(self, module_id: str) -> bool:
        """Check health of a module"""
        try:
            module = await self.module_registry.get_module(module_id)
            if not module:
                return False
            
            url = f"http://{module.host}:{module.port}{module.health_endpoint}"
            
            async def check_health():
                if HTTPX_AVAILABLE:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(url, timeout=10.0)
                        return response.status_code == 200
                elif AIOHTTP_AVAILABLE:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            return response.status == 200
                else:
                    return False
            
            is_healthy = await check_health()
            
            # Update module status
            status = ModuleStatus.HEALTHY if is_healthy else ModuleStatus.UNHEALTHY
            await self.module_registry.update_module_status(module_id, status)
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"Health check failed for module {module_id}: {e}")
            await self.module_registry.update_module_status(module_id, ModuleStatus.UNHEALTHY)
            return False

# Authentication and Security
class SecurityManager:
    """Manages authentication and security for inter-module communication"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        if CRYPTO_AVAILABLE:
            self.fernet = Fernet(Fernet.generate_key())
        else:
            self.fernet = None
    
    def generate_token(self, module_id: str, expires_in: int = 3600) -> str:
        """Generate JWT token for module authentication"""
        if not JWT_AVAILABLE:
            return f"mock_token_{module_id}_{int(time.time())}"
        
        payload = {
            "module_id": module_id,
            "exp": datetime.utcnow() + timedelta(seconds=expires_in),
            "iat": datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm="HS256")
    
    def verify_token(self, token: str) -> Optional[str]:
        """Verify JWT token and return module_id"""
        if not JWT_AVAILABLE:
            # Mock verification for testing
            if token.startswith("mock_token_"):
                parts = token.split("_")
                if len(parts) >= 3:
                    return parts[2]
            return None
        
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])
            return payload.get("module_id")
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid token")
            return None
    
    def encrypt_message(self, message: str) -> str:
        """Encrypt message content"""
        if not self.fernet:
            return message  # Return as-is if encryption not available
        
        return self.fernet.encrypt(message.encode()).decode()
    
    def decrypt_message(self, encrypted_message: str) -> str:
        """Decrypt message content"""
        if not self.fernet:
            return encrypted_message  # Return as-is if encryption not available
        
        try:
            return self.fernet.decrypt(encrypted_message.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to decrypt message: {e}")
            return encrypted_message

# Performance Monitor
class PerformanceMonitor:
    """Monitors performance of inter-module communication"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.error_counts: Dict[str, int] = {}
    
    async def record_request_time(self, module_id: str, duration: float):
        """Record request duration"""
        if module_id not in self.metrics:
            self.metrics[module_id] = []
        
        self.metrics[module_id].append(duration)
        
        # Keep only last 100 measurements
        if len(self.metrics[module_id]) > 100:
            self.metrics[module_id] = self.metrics[module_id][-100:]
    
    async def record_error(self, module_id: str):
        """Record error for module"""
        self.error_counts[module_id] = self.error_counts.get(module_id, 0) + 1
    
    async def get_metrics(self, module_id: str) -> Dict[str, Any]:
        """Get performance metrics for module"""
        durations = self.metrics.get(module_id, [])
        error_count = self.error_counts.get(module_id, 0)
        
        if not durations:
            return {
                "average_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "error_count": error_count,
                "total_requests": 0
            }
        
        return {
            "average_response_time": sum(durations) / len(durations),
            "min_response_time": min(durations),
            "max_response_time": max(durations),
            "error_count": error_count,
            "total_requests": len(durations)
        }

# Global instances (to be initialized by modules)
module_registry = ModuleRegistry()
security_manager = SecurityManager("default_secret_key")  # Should be configured properly
performance_monitor = PerformanceMonitor()
inter_module_client = InterModuleClient(module_registry)

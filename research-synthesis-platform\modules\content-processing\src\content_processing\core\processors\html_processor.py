import re
from typing import Dict, Any, List

from .content_processor import ContentProcessor, ProcessedContent, ContentMetadata, ContentType
from .text_processor import TextContentProcessor

class HTMLContentProcessor(ContentProcessor):
    """Processor for HTML content"""
    
    def __init__(self):
        self.name = "HTMLContentProcessor"
    
    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process HTML content"""
        
        processing_steps = []
        
        # Extract text from HTML
        text_content = await self._extract_text_from_html(content)
        processing_steps.append("html_text_extraction")
        
        # Extract structured data
        structured_data = await self._extract_structured_data(content)
        processing_steps.append("structured_data_extraction")
        
        # Use text processor for remaining processing
        text_processor = TextContentProcessor()
        text_result = await text_processor.process(text_content, metadata)
        
        # Merge results
        text_result.processing_steps = processing_steps + text_result.processing_steps
        text_result.extracted_entities.update(structured_data)
        
        return text_result
    
    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type == ContentType.HTML
    
    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name
    
    async def _extract_text_from_html(self, html_content: str) -> str:
        """Extract clean text from HTML"""
        
        # Remove script and style elements
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', html_content)
        
        # Decode HTML entities
        html_entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&nbsp;': ' '
        }
        
        for entity, char in html_entities.items():
            text = text.replace(entity, char)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    async def _extract_structured_data(self, html_content: str) -> Dict[str, List[str]]:
        """Extract structured data from HTML"""
        
        structured_data = {
            "headings": [],
            "links": [],
            "images": [],
            "meta_tags": []
        }
        
        # Extract headings
        heading_pattern = r'<h[1-6][^>]*>(.*?)</h[1-6]>'
        headings = re.findall(heading_pattern, html_content, re.IGNORECASE | re.DOTALL)
        structured_data["headings"] = [re.sub(r'<[^>]+>', '', h).strip() for h in headings]
        
        # Extract links
        link_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>(.*?)</a>'
        links = re.findall(link_pattern, html_content, re.IGNORECASE | re.DOTALL)
        structured_data["links"] = [url for url, text in links]
        
        # Extract images
        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        images = re.findall(img_pattern, html_content, re.IGNORECASE)
        structured_data["images"] = images
        
        # Extract meta tags
        meta_pattern = r'<meta[^>]+name=["\']([^"\']+)["\'][^>]+content=["\']([^"\']+)["\'][^>]*>'
        meta_tags = re.findall(meta_pattern, html_content, re.IGNORECASE)
        structured_data["meta_tags"] = [f"{name}: {content}" for name, content in meta_tags]
        
        return structured_data

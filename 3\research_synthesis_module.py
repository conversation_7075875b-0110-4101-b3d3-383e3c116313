# research_synthesis/research_coordinator.py
import asyncio
import logging
import json
import uuid
import hashlib
import re
from typing import Any, Dict, List, Optional, Union, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict, field
from abc import ABC, abstractmethod
from enum import Enum
import httpx
import aiofiles
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, validator
import uvicorn
from urllib.parse import urlparse
import asyncio
import concurrent.futures
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from textstat import flesch_reading_ease, flesch_kincaid_grade
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from collections import Counter, defaultdict
import spacy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download required NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    logger.warning("Could not download NLTK data")

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
    nlp = None

# Enums
class SourceType(Enum):
    GITHUB_REPO = "github_repo"
    YOUTUBE_VIDEO = "youtube_video"
    WEB_ARTICLE = "web_article"
    DOCUMENT = "document"
    API_DOCUMENTATION = "api_documentation"
    RESEARCH_PAPER = "research_paper"
    BLOG_POST = "blog_post"

class ResearchStatus(Enum):
    PENDING = "pending"
    ANALYZING = "analyzing"
    PROCESSING = "processing"
    SYNTHESIZING = "synthesizing"
    COMPLETED = "completed"
    FAILED = "failed"

class CredibilityLevel(Enum):
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

class BiasType(Enum):
    NONE = "none"
    POLITICAL = "political"
    COMMERCIAL = "commercial"
    CONFIRMATION = "confirmation"
    SELECTION = "selection"
    PUBLICATION = "publication"

class QualityMetric(Enum):
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness"
    RELEVANCE = "relevance"
    RECENCY = "recency"
    AUTHORITY = "authority"
    OBJECTIVITY = "objectivity"

# Data Classes
@dataclass
class SourceMetadata:
    url: str
    title: str
    author: Optional[str] = None
    publication_date: Optional[datetime] = None
    domain: Optional[str] = None
    word_count: int = 0
    language: str = "en"
    tags: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.domain is None and self.url:
            parsed = urlparse(self.url)
            self.domain = parsed.netloc

@dataclass
class ContentAnalysis:
    source_id: str
    content_hash: str
    key_points: List[str]
    entities: List[Dict[str, Any]]
    topics: List[Dict[str, float]]  # topic -> relevance score
    sentiment_score: float
    readability_score: float
    technical_complexity: float
    factual_claims: List[str]
    citations: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class QualityAssessment:
    source_id: str
    credibility_level: CredibilityLevel
    bias_types: List[BiasType]
    quality_scores: Dict[QualityMetric, float]
    verification_status: Dict[str, bool]
    issues_found: List[str]
    confidence_score: float
    assessment_timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class ResearchSource:
    source_id: str
    source_type: SourceType
    metadata: SourceMetadata
    raw_content: str
    processed_content: str
    analysis: Optional[ContentAnalysis] = None
    quality_assessment: Optional[QualityAssessment] = None
    extraction_timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class ResearchTask:
    task_id: str
    topic: str
    research_question: str
    sources: List[ResearchSource]
    status: ResearchStatus
    priority: int = 1
    deadline: Optional[datetime] = None
    requester: str = "system"
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)

@dataclass
class SynthesisResult:
    task_id: str
    synthesis_id: str
    key_findings: List[str]
    supporting_evidence: Dict[str, List[str]]
    conflicting_information: List[Dict[str, Any]]
    knowledge_gaps: List[str]
    confidence_levels: Dict[str, float]
    source_citations: Dict[str, List[str]]
    synthesis_summary: str
    detailed_analysis: str
    recommendations: List[str]
    created_at: datetime = field(default_factory=datetime.utcnow)

# Content Analyzer
class ContentAnalyzer:
    def __init__(self):
        self.sentiment_analyzer = SentimentIntensityAnalyzer() if 'SentimentIntensityAnalyzer' in globals() else None
        self.stop_words = set(stopwords.words('english')) if 'stopwords' in globals() else set()
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
    
    async def analyze_content(self, source: ResearchSource) -> ContentAnalysis:
        """Perform comprehensive content analysis"""
        content = source.processed_content or source.raw_content
        
        # Extract key points
        key_points = await self._extract_key_points(content)
        
        # Named entity recognition
        entities = await self._extract_entities(content)
        
        # Topic extraction
        topics = await self._extract_topics(content)
        
        # Sentiment analysis
        sentiment_score = await self._analyze_sentiment(content)
        
        # Readability analysis
        readability_score = await self._calculate_readability(content)
        
        # Technical complexity
        technical_complexity = await self._assess_technical_complexity(content)
        
        # Extract factual claims
        factual_claims = await self._extract_factual_claims(content)
        
        # Extract citations
        citations = await self._extract_citations(content)
        
        # Generate content hash
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        
        return ContentAnalysis(
            source_id=source.source_id,
            content_hash=content_hash,
            key_points=key_points,
            entities=entities,
            topics=topics,
            sentiment_score=sentiment_score,
            readability_score=readability_score,
            technical_complexity=technical_complexity,
            factual_claims=factual_claims,
            citations=citations,
            metadata={
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "content_length": len(content),
                "word_count": len(content.split())
            }
        )
    
    async def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points using sentence ranking"""
        sentences = sent_tokenize(content) if 'sent_tokenize' in globals() else content.split('.')
        
        if len(sentences) < 3:
            return sentences[:2]
        
        # Simple extractive summarization using TF-IDF
        try:
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(sentences)
            sentence_scores = np.mean(tfidf_matrix.toarray(), axis=1)
            
            # Get top sentences
            top_indices = np.argsort(sentence_scores)[-min(5, len(sentences)):]
            key_points = [sentences[i].strip() for i in sorted(top_indices) if sentences[i].strip()]
            
            return key_points
        
        except Exception as e:
            logger.warning(f"TF-IDF extraction failed: {e}")
            # Fallback: return first few sentences
            return [s.strip() for s in sentences[:3] if s.strip()]
    
    async def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract named entities using spaCy"""
        entities = []
        
        if nlp is None:
            return entities
        
        try:
            doc = nlp(content[:1000000])  # Limit content length for performance
            
            for ent in doc.ents:
                entities.append({
                    "text": ent.text,
                    "label": ent.label_,
                    "description": spacy.explain(ent.label_),
                    "start": ent.start_char,
                    "end": ent.end_char,
                    "confidence": getattr(ent, 'confidence', 1.0)
                })
        
        except Exception as e:
            logger.warning(f"Entity extraction failed: {e}")
        
        return entities
    
    async def _extract_topics(self, content: str) -> List[Dict[str, float]]:
        """Extract topics and their relevance scores"""
        topics = []
        
        try:
            # Simple keyword-based topic extraction
            words = word_tokenize(content.lower()) if 'word_tokenize' in globals() else content.lower().split()
            words = [w for w in words if w.isalnum() and w not in self.stop_words and len(w) > 3]
            
            word_freq = Counter(words)
            
            # Get top keywords as topics
            for word, freq in word_freq.most_common(10):
                relevance_score = freq / len(words)
                topics.append({
                    "topic": word,
                    "relevance_score": relevance_score
                })
        
        except Exception as e:
            logger.warning(f"Topic extraction failed: {e}")
        
        return topics
    
    async def _analyze_sentiment(self, content: str) -> float:
        """Analyze sentiment of content"""
        if self.sentiment_analyzer is None:
            return 0.0
        
        try:
            scores = self.sentiment_analyzer.polarity_scores(content)
            return scores['compound']  # Overall sentiment score (-1 to 1)
        
        except Exception as e:
            logger.warning(f"Sentiment analysis failed: {e}")
            return 0.0
    
    async def _calculate_readability(self, content: str) -> float:
        """Calculate readability score"""
        try:
            # Use Flesch Reading Ease score (0-100, higher = easier)
            score = flesch_reading_ease(content)
            return max(0, min(100, score)) / 100.0  # Normalize to 0-1
        
        except Exception as e:
            logger.warning(f"Readability calculation failed: {e}")
            return 0.5  # Default medium readability
    
    async def _assess_technical_complexity(self, content: str) -> float:
        """Assess technical complexity of content"""
        try:
            # Technical indicators
            technical_terms = [
                'algorithm', 'api', 'framework', 'library', 'function', 'method',
                'class', 'object', 'interface', 'protocol', 'implementation',
                'architecture', 'design pattern', 'optimization', 'performance',
                'scalability', 'deployment', 'configuration', 'parameter'
            ]
            
            words = content.lower().split()
            technical_count = sum(1 for word in words if any(term in word for term in technical_terms))
            
            # Normalize by content length
            complexity_score = min(1.0, technical_count / max(len(words), 1) * 10)
            
            return complexity_score
        
        except Exception as e:
            logger.warning(f"Technical complexity assessment failed: {e}")
            return 0.5
    
    async def _extract_factual_claims(self, content: str) -> List[str]:
        """Extract factual claims from content"""
        claims = []
        
        try:
            sentences = sent_tokenize(content) if 'sent_tokenize' in globals() else content.split('.')
            
            # Look for sentences with factual patterns
            factual_patterns = [
                r'\b\d+%\b',  # Percentages
                r'\b\d+\s+(times|x)\b',  # Multipliers
                r'\b(according to|research shows|studies indicate|data reveals)\b',
                r'\b(published|released|announced)\s+in\s+\d{4}\b',  # Date references
                r'\b(increases|decreases|improves|reduces)\s+by\s+\d+\b'
            ]
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 20:  # Skip very short sentences
                    continue
                
                for pattern in factual_patterns:
                    if re.search(pattern, sentence, re.IGNORECASE):
                        claims.append(sentence)
                        break
        
        except Exception as e:
            logger.warning(f"Factual claim extraction failed: {e}")
        
        return claims[:10]  # Limit to top 10 claims
    
    async def _extract_citations(self, content: str) -> List[str]:
        """Extract citations and references"""
        citations = []
        
        try:
            # Look for URL patterns
            url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
            urls = re.findall(url_pattern, content)
            citations.extend(urls)
            
            # Look for academic citation patterns
            citation_patterns = [
                r'\([A-Z][a-z]+,?\s+\d{4}\)',  # (Author, Year)
                r'\[[^\]]+\]',  # [Reference]
                r'doi:\s*[\d.]+/[^\s]+',  # DOI references
            ]
            
            for pattern in citation_patterns:
                matches = re.findall(pattern, content)
                citations.extend(matches)
        
        except Exception as e:
            logger.warning(f"Citation extraction failed: {e}")
        
        return list(set(citations))  # Remove duplicates

# Quality Assessor
class QualityAssessor:
    def __init__(self):
        self.domain_authority_scores = {
            'github.com': 0.9,
            'arxiv.org': 0.95,
            'ieee.org': 0.95,
            'acm.org': 0.9,
            'medium.com': 0.6,
            'dev.to': 0.6,
            'stackoverflow.com': 0.8,
            'microsoft.com': 0.85,
            'google.com': 0.85,
            'openai.com': 0.8,
            'anthropic.com': 0.8
        }
        
        self.bias_indicators = {
            BiasType.COMMERCIAL: [
                'buy now', 'purchase', 'subscribe', 'premium', 'paid',
                'sponsor', 'advertisement', 'affiliate'
            ],
            BiasType.POLITICAL: [
                'liberal', 'conservative', 'democrat', 'republican',
                'left-wing', 'right-wing', 'partisan'
            ],
            BiasType.CONFIRMATION: [
                'obviously', 'clearly', 'without doubt', 'certainly',
                'everyone knows', 'it is obvious'
            ]
        }
    
    async def assess_quality(self, source: ResearchSource, analysis: ContentAnalysis) -> QualityAssessment:
        """Perform comprehensive quality assessment"""
        
        # Assess credibility
        credibility_level = await self._assess_credibility(source, analysis)
        
        # Detect bias
        bias_types = await self._detect_bias(source, analysis)
        
        # Calculate quality scores
        quality_scores = await self._calculate_quality_scores(source, analysis)
        
        # Verification checks
        verification_status = await self._perform_verification_checks(source, analysis)
        
        # Identify issues
        issues_found = await self._identify_issues(source, analysis)
        
        # Overall confidence score
        confidence_score = await self._calculate_confidence_score(
            credibility_level, bias_types, quality_scores, verification_status
        )
        
        return QualityAssessment(
            source_id=source.source_id,
            credibility_level=credibility_level,
            bias_types=bias_types,
            quality_scores=quality_scores,
            verification_status=verification_status,
            issues_found=issues_found,
            confidence_score=confidence_score
        )
    
    async def _assess_credibility(self, source: ResearchSource, analysis: ContentAnalysis) -> CredibilityLevel:
        """Assess source credibility"""
        score = 0.5  # Start with medium credibility
        
        # Domain authority
        domain = source.metadata.domain
        if domain in self.domain_authority_scores:
            score += (self.domain_authority_scores[domain] - 0.5) * 0.4
        
        # Author credentials (if available)
        if source.metadata.author:
            score += 0.1
        
        # Publication date (recent is better)
        if source.metadata.publication_date:
            days_old = (datetime.utcnow() - source.metadata.publication_date).days
            if days_old < 365:  # Less than a year old
                score += 0.1
            elif days_old > 1825:  # More than 5 years old
                score -= 0.1
        
        # Citations and references
        if len(analysis.citations) > 3:
            score += 0.15
        
        # Content quality indicators
        if analysis.readability_score > 0.6:
            score += 0.1
        
        if len(analysis.factual_claims) > 2:
            score += 0.1
        
        # Convert score to credibility level
        if score >= 0.8:
            return CredibilityLevel.VERY_HIGH
        elif score >= 0.65:
            return CredibilityLevel.HIGH
        elif score >= 0.4:
            return CredibilityLevel.MEDIUM
        elif score >= 0.25:
            return CredibilityLevel.LOW
        else:
            return CredibilityLevel.VERY_LOW
    
    async def _detect_bias(self, source: ResearchSource, analysis: ContentAnalysis) -> List[BiasType]:
        """Detect potential bias in content"""
        detected_bias = []
        content = source.processed_content.lower()
        
        for bias_type, indicators in self.bias_indicators.items():
            bias_count = sum(1 for indicator in indicators if indicator in content)
            
            # If significant bias indicators found
            if bias_count >= 2:
                detected_bias.append(bias_type)
        
        # Check for selection bias (one-sided presentation)
        if abs(analysis.sentiment_score) > 0.5:  # Highly positive or negative
            detected_bias.append(BiasType.SELECTION)
        
        return detected_bias if detected_bias else [BiasType.NONE]
    
    async def _calculate_quality_scores(self, source: ResearchSource, analysis: ContentAnalysis) -> Dict[QualityMetric, float]:
        """Calculate quality scores for different metrics"""
        scores = {}
        
        # Accuracy (based on factual claims and citations)
        accuracy_score = min(1.0, len(analysis.factual_claims) * 0.1 + len(analysis.citations) * 0.05)
        scores[QualityMetric.ACCURACY] = accuracy_score
        
        # Completeness (based on content length and key points)
        content_length = len(source.processed_content)
        completeness_score = min(1.0, content_length / 5000 + len(analysis.key_points) * 0.1)
        scores[QualityMetric.COMPLETENESS] = completeness_score
        
        # Relevance (would need topic matching - simplified for now)
        relevance_score = min(1.0, sum(topic['relevance_score'] for topic in analysis.topics[:3]))
        scores[QualityMetric.RELEVANCE] = relevance_score
        
        # Recency
        if source.metadata.publication_date:
            days_old = (datetime.utcnow() - source.metadata.publication_date).days
            recency_score = max(0.0, 1.0 - days_old / 1825)  # Decay over 5 years
        else:
            recency_score = 0.5  # Unknown date
        scores[QualityMetric.RECENCY] = recency_score
        
        # Authority (based on domain and author)
        authority_score = self.domain_authority_scores.get(source.metadata.domain, 0.5)
        if source.metadata.author:
            authority_score = min(1.0, authority_score + 0.1)
        scores[QualityMetric.AUTHORITY] = authority_score
        
        # Objectivity (inverse of bias)
        bias_penalty = len([b for b in await self._detect_bias(source, analysis) if b != BiasType.NONE]) * 0.2
        objectivity_score = max(0.0, 1.0 - bias_penalty)
        scores[QualityMetric.OBJECTIVITY] = objectivity_score
        
        return scores
    
    async def _perform_verification_checks(self, source: ResearchSource, analysis: ContentAnalysis) -> Dict[str, bool]:
        """Perform various verification checks"""
        checks = {}
        
        # Check if content has sufficient length
        checks['sufficient_length'] = len(source.processed_content) > 100
        
        # Check if content has key points
        checks['has_key_points'] = len(analysis.key_points) > 0
        
        # Check if content has citations
        checks['has_citations'] = len(analysis.citations) > 0
        
        # Check if content is not overly promotional
        promotional_words = ['buy', 'purchase', 'subscribe', 'premium', 'offer']
        promotional_count = sum(1 for word in promotional_words if word in source.processed_content.lower())
        checks['not_promotional'] = promotional_count < 3
        
        # Check readability
        checks['readable'] = analysis.readability_score > 0.3
        
        # Check for factual content
        checks['has_facts'] = len(analysis.factual_claims) > 0
        
        return checks
    
    async def _identify_issues(self, source: ResearchSource, analysis: ContentAnalysis) -> List[str]:
        """Identify potential issues with the source"""
        issues = []
        
        # Content too short
        if len(source.processed_content) < 100:
            issues.append("Content is too short for meaningful analysis")
        
        # No citations
        if len(analysis.citations) == 0:
            issues.append("No citations or references found")
        
        # Very old content
        if source.metadata.publication_date:
            days_old = (datetime.utcnow() - source.metadata.publication_date).days
            if days_old > 1825:  # 5 years
                issues.append("Content is over 5 years old")
        
        # Poor readability
        if analysis.readability_score < 0.2:
            issues.append("Poor readability score")
        
        # Strong bias detected
        bias_types = await self._detect_bias(source, analysis)
        if len([b for b in bias_types if b != BiasType.NONE]) > 1:
            issues.append("Multiple types of bias detected")
        
        # No factual claims
        if len(analysis.factual_claims) == 0:
            issues.append("No factual claims found")
        
        return issues
    
    async def _calculate_confidence_score(
        self,
        credibility: CredibilityLevel,
        bias_types: List[BiasType],
        quality_scores: Dict[QualityMetric, float],
        verification_status: Dict[str, bool]
    ) -> float:
        """Calculate overall confidence score"""
        
        # Base score from credibility
        credibility_scores = {
            CredibilityLevel.VERY_HIGH: 0.9,
            CredibilityLevel.HIGH: 0.75,
            CredibilityLevel.MEDIUM: 0.5,
            CredibilityLevel.LOW: 0.3,
            CredibilityLevel.VERY_LOW: 0.1
        }
        
        confidence = credibility_scores.get(credibility, 0.5)
        
        # Adjust for bias
        bias_penalty = len([b for b in bias_types if b != BiasType.NONE]) * 0.1
        confidence = max(0.0, confidence - bias_penalty)
        
        # Adjust for quality scores
        avg_quality = sum(quality_scores.values()) / len(quality_scores)
        confidence = (confidence + avg_quality) / 2
        
        # Adjust for verification checks
        verification_ratio = sum(verification_status.values()) / len(verification_status)
        confidence = (confidence + verification_ratio) / 2
        
        return min(1.0, max(0.0, confidence))

# Research Coordinator
class ResearchCoordinator:
    def __init__(
        self,
        external_integration_url: str = "http://localhost:8005",
        content_processing_url: str = "http://localhost:8006",
        memory_service_url: str = "http://localhost:8001"
    ):
        self.external_integration_url = external_integration_url
        self.content_processing_url = content_processing_url
        self.memory_service_url = memory_service_url
        
        self.content_analyzer = ContentAnalyzer()
        self.quality_assessor = QualityAssessor()
        
        self.active_tasks: Dict[str, ResearchTask] = {}
        self.completed_tasks: Dict[str, SynthesisResult] = {}
        
        # HTTP clients
        self.http_client = httpx.AsyncClient(timeout=60.0)
    
    async def create_research_task(
        self,
        topic: str,
        research_question: str,
        source_urls: List[str],
        priority: int = 1,
        deadline: Optional[datetime] = None,
        requester: str = "system",
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new research task"""
        
        task_id = str(uuid.uuid4())
        
        task = ResearchTask(
            task_id=task_id,
            topic=topic,
            research_question=research_question,
            sources=[],
            status=ResearchStatus.PENDING,
            priority=priority,
            deadline=deadline,
            requester=requester,
            context=context or {}
        )
        
        self.active_tasks[task_id] = task
        
        # Start processing in background
        asyncio.create_task(self._process_research_task(task_id, source_urls))
        
        logger.info(f"Created research task {task_id}: {topic}")
        return task_id
    
    async def _process_research_task(self, task_id: str, source_urls: List[str]):
        """Process research task through complete pipeline"""
        
        task = self.active_tasks[task_id]
        
        try:
            task.status = ResearchStatus.ANALYZING
            
            # Step 1: Collect and analyze sources
            sources = await self._collect_sources(source_urls)
            task.sources = sources
            
            # Step 2: Analyze content for each source
            task.status = ResearchStatus.PROCESSING
            for source in sources:
                try:
                    # Content analysis
                    analysis = await self.content_analyzer.analyze_content(source)
                    source.analysis = analysis
                    
                    # Quality assessment
                    quality_assessment = await self.quality_assessor.assess_quality(source, analysis)
                    source.quality_assessment = quality_assessment
                    
                    logger.info(f"Analyzed source {source.source_id} with confidence {quality_assessment.confidence_score:.2f}")
                
                except Exception as e:
                    logger.error(f"Failed to analyze source {source.source_id}: {e}")
            
            # Step 3: Synthesize research
            task.status = ResearchStatus.SYNTHESIZING
            synthesis_result = await self._synthesize_research(task)
            
            # Step 4: Store results
            await self._store_synthesis_result(synthesis_result)
            
            task.status = ResearchStatus.COMPLETED
            self.completed_tasks[task_id] = synthesis_result
            
            logger.info(f"Research task {task_id} completed successfully")
            
        except Exception as e:
            task.status = ResearchStatus.FAILED
            logger.error(f"Research task {task_id} failed: {e}")
    
    async def _collect_sources(self, source_urls: List[str]) -> List[ResearchSource]:
        """Collect content from various sources"""
        sources = []
        
        for url in source_urls:
            try:
                source_type = self._determine_source_type(url)
                
                # Get content from external integration module
                response = await self.http_client.post(
                    f"{self.external_integration_url}/extract/content",
                    json={"url": url, "source_type": source_type.value}
                )
                
                if response.status_code == 200:
                    content_data = response.json()
                    
                    metadata = SourceMetadata(
                        url=url,
                        title=content_data.get("title", ""),
                        author=content_data.get("author"),
                        publication_date=datetime.fromisoformat(content_data["publication_date"]) if content_data.get("publication_date") else None,
                        word_count=content_data.get("word_count", 0),
                        tags=content_data.get("tags", [])
                    )
                    
                    source = ResearchSource(
                        source_id=str(uuid.uuid4()),
                        source_type=source_type,
                        metadata=metadata,
                        raw_content=content_data.get("raw_content", ""),
                        processed_content=content_data.get("processed_content", "")
                    )
                    
                    sources.append(source)
                
                else:
                    logger.warning(f"Failed to extract content from {url}: {response.status_code}")
            
            except Exception as e:
                logger.error(f"Error collecting source {url}: {e}")
        
        return sources
    
    def _determine_source_type(self, url: str) -> SourceType:
        """Determine source type from URL"""
        domain = urlparse(url).netloc.lower()
        
        if 'github.com' in domain:
            return SourceType.GITHUB_REPO
        elif 'youtube.com' in domain or 'youtu.be' in domain:
            return SourceType.YOUTUBE_VIDEO
        elif any(ext in url.lower() for ext in ['.pdf', '.doc', '.docx']):
            return SourceType.DOCUMENT
        elif 'docs.' in domain or 'documentation' in url.lower():
            return SourceType.API_DOCUMENTATION
        elif 'arxiv.org' in domain or 'ieee.org' in domain:
            return SourceType.RESEARCH_PAPER
        elif any(blog_indicator in domain for blog_indicator in ['blog', 'medium', 'dev.to']):
            return SourceType.BLOG_POST
        else:
            return SourceType.WEB_ARTICLE
    
    async def _synthesize_research(self, task: ResearchTask) -> SynthesisResult:
        """Synthesize research from multiple sources"""
        
        synthesis_id = str(uuid.uuid4())
        
        # Filter high-quality sources
        high_quality_sources = [
            source for source in task.sources
            if source.quality_assessment and source.quality_assessment.confidence_score > 0.6
        ]
        
        if not high_quality_sources:
            # Use all sources if no high-quality ones found
            high_quality_sources = task.sources
        
        # Extract key findings
        key_findings = await self._extract_key_findings(high_quality_sources)
        
        # Identify supporting evidence
        supporting_evidence = await self._identify_supporting_evidence(high_quality_sources, key_findings)
        
        # Find conflicting information
        conflicting_information = await self._find_conflicting_information(high_quality_sources)
        
        # Identify knowledge gaps
        knowledge_gaps = await self._identify_knowledge_gaps(task, high_quality_sources)
        
        # Calculate confidence levels
        confidence_levels = await self._calculate_confidence_levels(high_quality_sources, key_findings)
        
        # Create source citations
        source_citations = await self._create_source_citations(high_quality_sources, key_findings)
        
        # Generate synthesis summary
        synthesis_summary = await self._generate_synthesis_summary(task, key_findings)
        
        # Generate detailed analysis
        detailed_analysis = await self._generate_detailed_analysis(task, high_quality_sources, key_findings)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(task, key_findings, knowledge_gaps)
        
        return SynthesisResult(
            task_id=task.task_id,
            synthesis_id=synthesis_id,
            key_findings=key_findings,
            supporting_evidence=supporting_evidence,
            conflicting_information=conflicting_information,
            knowledge_gaps=knowledge_gaps,
            confidence_levels=confidence_levels,
            source_citations=source_citations,
            synthesis_summary=synthesis_summary,
            detailed_analysis=detailed_analysis,
            recommendations=recommendations
        )
    
    async def _extract_key_findings(self, sources: List[ResearchSource]) -> List[str]:
        """Extract key findings from sources"""
        all_key_points = []
        
        for source in sources:
            if source.analysis:
                all_key_points.extend(source.analysis.key_points)
        
        # Deduplicate and rank by frequency/importance
        key_point_counts = Counter(all_key_points)
        
        # Get most frequent key points
        key_findings = [point for point, count in key_point_counts.most_common(10)]
        
        return key_findings
    
    async def _identify_supporting_evidence(
        self,
        sources: List[ResearchSource],
        key_findings: List[str]
    ) -> Dict[str, List[str]]:
        """Identify supporting evidence for key findings"""
        supporting_evidence = {}
        
        for finding in key_findings:
            evidence = []
            
            for source in sources:
                if source.analysis:
                    # Check if any factual claims support this finding
                    for claim in source.analysis.factual_claims:
                        # Simple similarity check (could be improved with embeddings)
                        if any(word in claim.lower() for word in finding.lower().split()):
                            evidence.append(f"{claim} (Source: {source.metadata.title})")
            
            supporting_evidence[finding] = evidence
        
        return supporting_evidence
    
    async def _find_conflicting_information(self, sources: List[ResearchSource]) -> List[Dict[str, Any]]:
        """Find conflicting information between sources"""
        conflicts = []
        
        # Simple approach: compare sentiment and key claims
        for i, source1 in enumerate(sources):
            for j, source2 in enumerate(sources[i+1:], i+1):
                if source1.analysis and source2.analysis:
                    # Check for sentiment conflicts
                    sentiment_diff = abs(source1.analysis.sentiment_score - source2.analysis.sentiment_score)
                    
                    if sentiment_diff > 0.5:  # Significant sentiment difference
                        conflicts.append({
                            "type": "sentiment_conflict",
                            "source1": {
                                "title": source1.metadata.title,
                                "sentiment": source1.analysis.sentiment_score
                            },
                            "source2": {
                                "title": source2.metadata.title,
                                "sentiment": source2.analysis.sentiment_score
                            },
                            "description": "Sources have conflicting sentiment about the topic"
                        })
        
        return conflicts
    
    async def _identify_knowledge_gaps(self, task: ResearchTask, sources: List[ResearchSource]) -> List[str]:
        """Identify knowledge gaps in the research"""
        gaps = []
        
        # Common knowledge areas that might be missing
        expected_areas = [
            "implementation details",
            "performance metrics",
            "limitations and challenges",
            "future directions",
            "comparison with alternatives",
            "use cases and applications"
        ]
        
        covered_topics = set()
        for source in sources:
            if source.analysis:
                covered_topics.update(topic['topic'] for topic in source.analysis.topics)
        
        # Check which areas are not covered
        for area in expected_areas:
            area_words = area.lower().split()
            if not any(word in covered_topics for word in area_words):
                gaps.append(f"Limited information about {area}")
        
        # Check if research question is fully addressed
        question_words = set(task.research_question.lower().split())
        covered_words = set(word.lower() for word in covered_topics)
        
        missing_aspects = question_words - covered_words - {'what', 'how', 'why', 'when', 'where', 'is', 'are', 'the', 'a', 'an'}
        
        if missing_aspects:
            gaps.append(f"Research question aspects not fully addressed: {', '.join(missing_aspects)}")
        
        return gaps
    
    async def _calculate_confidence_levels(
        self,
        sources: List[ResearchSource],
        key_findings: List[str]
    ) -> Dict[str, float]:
        """Calculate confidence levels for findings"""
        confidence_levels = {}
        
        for finding in key_findings:
            # Base confidence on source quality and consensus
            supporting_sources = 0
            total_confidence = 0.0
            
            for source in sources:
                if source.quality_assessment and source.analysis:
                    # Check if source supports this finding
                    if any(word in source.processed_content.lower() for word in finding.lower().split()):
                        supporting_sources += 1
                        total_confidence += source.quality_assessment.confidence_score
            
            if supporting_sources > 0:
                avg_confidence = total_confidence / supporting_sources
                # Boost confidence if multiple sources agree
                consensus_boost = min(0.2, supporting_sources * 0.05)
                final_confidence = min(1.0, avg_confidence + consensus_boost)
            else:
                final_confidence = 0.0
            
            confidence_levels[finding] = final_confidence
        
        return confidence_levels
    
    async def _create_source_citations(
        self,
        sources: List[ResearchSource],
        key_findings: List[str]
    ) -> Dict[str, List[str]]:
        """Create source citations for findings"""
        citations = {}
        
        for finding in key_findings:
            finding_citations = []
            
            for source in sources:
                # Check if source supports this finding
                if any(word in source.processed_content.lower() for word in finding.lower().split()):
                    citation = f"{source.metadata.title}"
                    if source.metadata.author:
                        citation += f" by {source.metadata.author}"
                    if source.metadata.publication_date:
                        citation += f" ({source.metadata.publication_date.year})"
                    citation += f" - {source.metadata.url}"
                    
                    finding_citations.append(citation)
            
            citations[finding] = finding_citations
        
        return citations
    
    async def _generate_synthesis_summary(self, task: ResearchTask, key_findings: List[str]) -> str:
        """Generate synthesis summary"""
        summary_parts = [
            f"Research Topic: {task.topic}",
            f"Research Question: {task.research_question}",
            "",
            "Key Findings:",
        ]
        
        for i, finding in enumerate(key_findings[:5], 1):
            summary_parts.append(f"{i}. {finding}")
        
        summary_parts.extend([
            "",
            f"Based on analysis of {len(task.sources)} sources.",
            f"Research completed on {datetime.utcnow().strftime('%Y-%m-%d %H:%M UTC')}."
        ])
        
        return "\n".join(summary_parts)
    
    async def _generate_detailed_analysis(
        self,
        task: ResearchTask,
        sources: List[ResearchSource],
        key_findings: List[str]
    ) -> str:
        """Generate detailed analysis"""
        
        analysis_parts = [
            f"# Detailed Research Analysis: {task.topic}",
            "",
            f"**Research Question:** {task.research_question}",
            "",
            "## Methodology",
            f"This analysis is based on {len(sources)} sources collected and analyzed using automated content analysis.",
            "",
            "## Source Quality Assessment",
        ]
        
        # Add source quality summary
        high_quality_count = sum(1 for s in sources if s.quality_assessment and s.quality_assessment.confidence_score > 0.7)
        medium_quality_count = sum(1 for s in sources if s.quality_assessment and 0.4 <= s.quality_assessment.confidence_score <= 0.7)
        low_quality_count = len(sources) - high_quality_count - medium_quality_count
        
        analysis_parts.extend([
            f"- High quality sources: {high_quality_count}",
            f"- Medium quality sources: {medium_quality_count}",
            f"- Lower quality sources: {low_quality_count}",
            "",
            "## Key Findings Analysis",
        ])
        
        # Add detailed findings
        for i, finding in enumerate(key_findings, 1):
            analysis_parts.append(f"### {i}. {finding}")
            analysis_parts.append("")
            # Add more details here based on supporting evidence
        
        return "\n".join(analysis_parts)
    
    async def _generate_recommendations(
        self,
        task: ResearchTask,
        key_findings: List[str],
        knowledge_gaps: List[str]
    ) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Based on key findings
        if key_findings:
            recommendations.append("Consider implementing the key findings in your project or research")
            recommendations.append("Validate findings with additional sources if making critical decisions")
        
        # Based on knowledge gaps
        if knowledge_gaps:
            recommendations.append("Conduct additional research to address identified knowledge gaps")
            for gap in knowledge_gaps[:3]:  # Top 3 gaps
                recommendations.append(f"Investigate: {gap}")
        
        # Source quality recommendations
        high_confidence_sources = [
            s for s in task.sources
            if s.quality_assessment and s.quality_assessment.confidence_score > 0.8
        ]
        
        if high_confidence_sources:
            recommendations.append("Focus on insights from high-confidence sources for decision making")
        else:
            recommendations.append("Seek additional authoritative sources to increase confidence in findings")
        
        # General recommendations
        recommendations.extend([
            "Consider the publication dates of sources when applying findings",
            "Be aware of potential bias in sources as identified in the analysis",
            "Cross-reference findings with domain experts when possible"
        ])
        
        return recommendations
    
    async def _store_synthesis_result(self, synthesis_result: SynthesisResult):
        """Store synthesis result in memory service"""
        try:
            memory_data = {
                "session_id": synthesis_result.task_id,
                "agent_id": "research_coordinator",
                "memory_type": "synthesis_result",
                "content": json.dumps(asdict(synthesis_result), default=str),
                "metadata": {
                    "synthesis_id": synthesis_result.synthesis_id,
                    "completion_timestamp": synthesis_result.created_at.isoformat(),
                    "findings_count": len(synthesis_result.key_findings)
                }
            }
            
            response = await self.http_client.post(
                f"{self.memory_service_url}/memory/store",
                json=memory_data
            )
            response.raise_for_status()
            
        except Exception as e:
            logger.warning(f"Failed to store synthesis result in memory: {e}")
    
    async def get_research_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of research task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                "task_id": task_id,
                "status": task.status.value,
                "topic": task.topic,
                "sources_count": len(task.sources),
                "created_at": task.created_at.isoformat(),
                "completed": task_id in self.completed_tasks
            }
        
        return None
    
    async def get_synthesis_result(self, task_id: str) -> Optional[SynthesisResult]:
        """Get synthesis result for completed task"""
        return self.completed_tasks.get(task_id)
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.http_client.aclose()

# Pydantic models for API
class CreateResearchTaskRequest(BaseModel):
    topic: str
    research_question: str
    source_urls: List[str]
    priority: int = 1
    deadline: Optional[datetime] = None
    requester: str = "system"
    context: Optional[Dict[str, Any]] = None

class ResearchTaskResponse(BaseModel):
    task_id: str
    status: str
    topic: str
    research_question: str
    sources_count: int
    created_at: datetime

# FastAPI Application
def create_research_app(coordinator: ResearchCoordinator) -> FastAPI:
    app = FastAPI(title="Research Synthesis Module", version="1.0.0")
    
    @app.post("/research/create", response_model=dict)
    async def create_research_task(request: CreateResearchTaskRequest):
        task_id = await coordinator.create_research_task(
            topic=request.topic,
            research_question=request.research_question,
            source_urls=request.source_urls,
            priority=request.priority,
            deadline=request.deadline,
            requester=request.requester,
            context=request.context
        )
        
        return {"task_id": task_id, "status": "created"}
    
    @app.get("/research/{task_id}/status")
    async def get_research_status(task_id: str):
        status = await coordinator.get_research_status(task_id)
        if status:
            return status
        raise HTTPException(status_code=404, detail="Research task not found")
    
    @app.get("/research/{task_id}/result")
    async def get_synthesis_result(task_id: str):
        result = await coordinator.get_synthesis_result(task_id)
        if result:
            return asdict(result)
        raise HTTPException(status_code=404, detail="Synthesis result not found or not ready")
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
    
    return app

# Example usage
if __name__ == "__main__":
    coordinator = ResearchCoordinator()
    app = create_research_app(coordinator)
    
    uvicorn.run(app, host="0.0.0.0", port=8007)
#!/usr/bin/env python3
"""
COMPREHENSIVE SPRINT 2 INTEGRATION TEST
Tests the complete Sprint 2 implementation with all components working together
Validates all 8 major components from the sprint plans
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime
import json

# Add paths for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "modules" /
                "external-integration" / "src"))
sys.path.insert(0, str(project_root / "modules" /
                "content-processing" / "src"))


def test_sprint2_imports():
    """Test that all Sprint 2 components can be imported"""
    print("🔍 Testing Sprint 2 Component Imports...")

    try:
        # External Integration Components
        from external_integration.clients.github_client import GitHubClient
        from external_integration.clients.youtube_client import YouTubeClient
        from external_integration.clients.search_client import UnifiedSearchClient
        from external_integration.clients.tts_client import TTSClient
        from external_integration.tools.tool_registry import ToolRegistry
        from external_integration.core.performance_optimizer import PerformanceOptimizer

        # Content Processing Components
        from content_processing.core.processors.content_processor import (
            ContentProcessor, TextContentProcessor, HTMLContentProcessor
        )
        from content_processing.core.processors.advanced_processors import TechnicalContentProcessor, VideoTranscriptProcessor
        from content_processing.main import ContentProcessingPipeline
        from content_processing.core.memory_integration import ContentMemoryManager

        print("✅ All Sprint 2 components imported successfully!")
        return True

    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_external_integration_clients():
    """Test all external integration clients"""
    print("\n🌐 Testing External Integration Clients...")

    try:
        from external_integration.clients.github_client import GitHubClient
        from external_integration.clients.youtube_client import YouTubeClient
        from external_integration.clients.search_client import UnifiedSearchClient, SearchProvider
        from external_integration.clients.tts_client import TTSClient, VoiceGender, AudioFormat

        # Test GitHub Client
        github_client = GitHubClient(token="test_token")
        assert github_client.token == "test_token"
        assert github_client.base_url == "https://api.github.com"
        print("  ✅ GitHub Client initialized correctly")

        # Test YouTube Client
        youtube_client = YouTubeClient(api_key="test_key")
        assert youtube_client.api_key == "test_key"
        assert youtube_client.base_url == "https://www.googleapis.com/youtube/v3"
        print("  ✅ YouTube Client initialized correctly")

        # Test Search Client
        search_config = {
            "google": {"api_key": "test", "search_engine_id": "test"},
            "bing": {"subscription_key": "test"},
            "duckduckgo": {}
        }
        search_client = UnifiedSearchClient(search_config)
        assert len(search_client.providers) == 3
        print("  ✅ Search Client initialized correctly")

        # Test TTS Client
        tts_config = {
            "openai": {"api_key": "test"},
            "elevenlabs": {"api_key": "test"},
            "azure": {"subscription_key": "test", "region": "eastus"}
        }
        tts_client = TTSClient(tts_config)
        assert len(tts_client.providers) == 3
        print("  ✅ TTS Client initialized correctly")

        # Test enums
        assert SearchProvider.GOOGLE == "google"
        assert VoiceGender.MALE == "male"
        assert AudioFormat.WAV == "wav"
        print("  ✅ All enums working correctly")

        return True

    except Exception as e:
        print(f"  ❌ External Integration Clients test failed: {e}")
        return False


def test_tool_registry_system():
    """Test the Tool Registry System"""
    print("\n🔧 Testing Tool Registry System...")

    try:
        from external_integration.tools.tool_registry import ToolRegistry, ToolType, ToolStatus, ToolParameter

        # Create tool registry
        registry = ToolRegistry()
        assert len(registry.tools) == 0
        print("  ✅ Tool Registry created successfully")

        # Test tool registration
        def test_function(param1: str, param2: int = 10) -> str:
            return f"Result: {param1} - {param2}"

        tool_id = registry.register_tool(
            name="Test Tool",
            description="A test tool",
            tool_type=ToolType.UTILITY,
            function=test_function
        )

        assert len(registry.tools) == 1
        assert tool_id in registry.tools
        print("  ✅ Tool registration working correctly")

        # Test tool retrieval
        tool = registry.get_tool(tool_id)
        assert tool.name == "Test Tool"
        assert tool.tool_type == ToolType.UTILITY
        assert len(tool.parameters) == 2  # param1 and param2
        print("  ✅ Tool retrieval working correctly")

        # Test tool statistics
        stats = registry.get_tool_statistics()
        assert stats["total_tools"] == 1
        assert stats["active_tools"] == 1
        print("  ✅ Tool statistics working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Tool Registry System test failed: {e}")
        return False


def test_content_processing_pipeline():
    """Test the Content Processing Pipeline"""
    print("\n📝 Testing Content Processing Pipeline...")

    try:
        from content_processing.main import ContentProcessingPipeline
        from content_processing.core.processors.content_processor import (
            TextContentProcessor, HTMLContentProcessor, ContentType, ContentMetadata
        )
        from content_processing.core.processors.advanced_processors import TechnicalContentProcessor

        # Create pipeline
        pipeline = ContentProcessingPipeline()
        # TextContentProcessor and HTMLContentProcessor are pre-registered
        assert len(pipeline.processors) == 2
        print("  ✅ Content Processing Pipeline created successfully")

        # Register additional processor
        pipeline.register_processor(TechnicalContentProcessor())

        assert len(pipeline.processors) == 3
        print("  ✅ Additional processor registered successfully")

        # Test processor selection
        text_processor = pipeline.get_processor(ContentType.TEXT)
        assert text_processor is not None
        assert text_processor.get_processor_name() == "TextContentProcessor"
        print("  ✅ Processor selection working correctly")

        # Test pipeline statistics
        stats = pipeline.get_pipeline_statistics()
        assert stats["total_processors"] == 3
        assert stats["total_processed"] == 0
        print("  ✅ Pipeline statistics working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Content Processing Pipeline test failed:")
        print(f"      Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_advanced_processors():
    """Test Advanced Content Processors"""
    print("\n🧠 Testing Advanced Content Processors...")

    try:
        from content_processing.core.processors.advanced_processors import TechnicalContentProcessor, VideoTranscriptProcessor
        from content_processing.core.processors.content_processor import ContentType

        # Test Technical Content Processor
        tech_processor = TechnicalContentProcessor()
        assert tech_processor.can_process(ContentType.TEXT) == True
        assert tech_processor.can_process(ContentType.CODE) == True
        assert "python" in tech_processor.programming_languages
        assert "react" in tech_processor.frameworks
        print("  ✅ Technical Content Processor working correctly")

        # Test Video Transcript Processor
        video_processor = VideoTranscriptProcessor()
        assert video_processor.can_process(
            ContentType.VIDEO_TRANSCRIPT) == True
        assert video_processor.can_process(ContentType.TEXT) == False
        print("  ✅ Video Transcript Processor working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Advanced Processors test failed: {e}")
        return False


def test_memory_integration():
    """Test Memory Integration"""
    print("\n🧠 Testing Memory Integration...")

    try:
        from content_processing.core.memory_integration import ContentMemoryManager

        # Create a mock module client for testing
        class MockModuleClient:
            async def call_module(self, target_module, operation, parameters):
                return {"success": True, "data": None}

        # Create memory manager
        mock_client = MockModuleClient()
        memory_manager = ContentMemoryManager(mock_client)

        assert memory_manager.max_cache_size == 1000
        assert memory_manager.default_ttl == 3600
        assert len(memory_manager.local_cache) == 0
        print("  ✅ Memory Integration initialized correctly")

        # Test cache statistics
        cache_stats = memory_manager.get_cache_statistics()
        assert cache_stats["total_entries"] == 0
        assert cache_stats["cache_size_limit"] == 1000
        print("  ✅ Memory cache statistics working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Memory Integration test failed: {e}")
        return False


def test_performance_optimizer():
    """Test Performance Optimizer"""
    print("\n⚡ Testing Performance Optimizer...")

    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer, measure_performance

        # Create optimizer
        optimizer = PerformanceOptimizer()
        assert len(optimizer.metrics) == 0
        assert len(optimizer.cache) == 0
        assert optimizer.cache_enabled == True
        print("  ✅ Performance Optimizer created successfully")

        # Test metric recording
        optimizer.record_metric("test_operation", 1.5, True, {"test": "data"})
        assert len(optimizer.metrics) == 1
        assert optimizer.metrics[0].operation == "test_operation"
        print("  ✅ Metric recording working correctly")

        # Test cache functionality
        optimizer.set_cache("test_key", {"data": "value"})
        cached_data = optimizer.get_cache("test_key")
        assert cached_data["data"] == "value"
        print("  ✅ Cache functionality working correctly")

        # Test performance statistics
        stats = optimizer.get_performance_statistics()
        assert stats["total_operations"] == 1
        assert stats["success_rate"] == 1.0
        print("  ✅ Performance statistics working correctly")

        # Test optimization recommendations
        recommendations = optimizer.get_optimization_recommendations()
        assert len(recommendations) > 0
        print("  ✅ Optimization recommendations working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Performance Optimizer test failed: {e}")
        return False


def test_integration_workflow():
    """Test end-to-end integration workflow"""
    print("\n🔄 Testing End-to-End Integration Workflow...")

    try:
        # Import all components
        from external_integration.tools.tool_registry import ToolRegistry, ToolType
        from content_processing.main import ContentProcessingPipeline
        from content_processing.core.processors.content_processor import TextContentProcessor
        from content_processing.core.processors.advanced_processors import TechnicalContentProcessor
        from external_integration.core.performance_optimizer import PerformanceOptimizer

        # Create integrated system
        tool_registry = ToolRegistry()
        processing_pipeline = ContentProcessingPipeline()
        performance_optimizer = PerformanceOptimizer()

        # Register additional processor (TextContentProcessor and HTMLContentProcessor are pre-registered)
        processing_pipeline.register_processor(TechnicalContentProcessor())

        # Register processing tools
        def process_content_tool(content: str, content_type: str = "text") -> dict:
            """Tool for content processing"""
            return {
                "processed": True,
                "content_length": len(content),
                "content_type": content_type
            }

        tool_id = tool_registry.register_tool(
            name="Content Processor",
            description="Process content using the pipeline",
            tool_type=ToolType.CONTENT_PROCESSING,
            function=process_content_tool
        )

        # Test integrated workflow
        assert len(tool_registry.tools) == 1
        # TextContentProcessor, HTMLContentProcessor, TechnicalContentProcessor
        assert len(processing_pipeline.processors) == 3
        assert len(performance_optimizer.metrics) == 0

        # Record performance metric
        performance_optimizer.record_metric("content_processing", 0.5, True)
        assert len(performance_optimizer.metrics) == 1

        print("  ✅ End-to-end integration workflow working correctly")

        # Test system statistics
        tool_stats = tool_registry.get_tool_statistics()
        pipeline_stats = processing_pipeline.get_pipeline_statistics()
        perf_stats = performance_optimizer.get_performance_statistics()

        system_summary = {
            "tools_registered": tool_stats["total_tools"],
            "processors_available": pipeline_stats["total_processors"],
            "performance_metrics": len(performance_optimizer.metrics),
            "cache_entries": len(performance_optimizer.cache)
        }

        print(f"  📊 System Summary: {json.dumps(system_summary, indent=2)}")
        print("  ✅ System integration and statistics working correctly")

        return True

    except Exception as e:
        print(f"  ❌ Integration workflow test failed:")
        print(f"      Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run comprehensive Sprint 2 integration test"""
    print("=" * 80)
    print("🚀 SPRINT 2 COMPREHENSIVE INTEGRATION TEST")
    print("Testing complete implementation with all 8 major components")
    print("=" * 80)

    tests = [
        ("Component Imports", test_sprint2_imports),
        ("External Integration Clients", test_external_integration_clients),
        ("Tool Registry System", test_tool_registry_system),
        ("Content Processing Pipeline", test_content_processing_pipeline),
        ("Advanced Processors", test_advanced_processors),
        ("Memory Integration", test_memory_integration),
        ("Performance Optimizer", test_performance_optimizer),
        ("End-to-End Integration", test_integration_workflow)
    ]

    passed = 0
    total = len(tests)
    results = []

    start_time = datetime.now()

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                results.append(f"✅ {test_name}")
                print(f"✅ {test_name} - PASSED")
            else:
                results.append(f"❌ {test_name}")
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            results.append(f"❌ {test_name}")
            print(f"❌ {test_name} - ERROR: {e}")

    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()

    print("\n" + "=" * 80)
    print("📋 SPRINT 2 INTEGRATION TEST RESULTS")
    print("=" * 80)

    for result in results:
        print(f"  {result}")

    print(f"\n📊 SUMMARY:")
    print(f"  Tests Passed: {passed}/{total} ({(passed/total)*100:.1f}%)")
    print(f"  Execution Time: {execution_time:.2f} seconds")

    if passed == total:
        print("\n🎉 ALL SPRINT 2 COMPONENTS WORKING PERFECTLY!")
        print("✅ External Integration Module - Complete")
        print("✅ Content Processing Module - Complete")
        print("✅ Tool Registry System - Complete")
        print("✅ Advanced Processors - Complete")
        print("✅ Memory Integration - Complete")
        print("✅ Performance Optimization - Complete")
        print("✅ End-to-End Integration - Complete")
        print("\n🚀 Sprint 2 is PRODUCTION READY!")
    else:
        print(f"\n⚠️  {total - passed} component(s) need attention")
        print("Check the failed tests above for details")

    print("=" * 80)

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

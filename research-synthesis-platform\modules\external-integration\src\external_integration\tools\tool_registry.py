from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
import inspect
import json
from enum import Enum
import uuid


class ToolType(str, Enum):
    SEARCH = "search"
    GITHUB = "github"
    YOUTUBE = "youtube"
    TTS = "tts"
    CONTENT_PROCESSING = "content_processing"
    UTILITY = "utility"


class ToolStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    MAINTENANCE = "maintenance"


@dataclass
class ToolParameter:
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
    validation_pattern: Optional[str] = None
    allowed_values: Optional[List[Any]] = None


@dataclass
class ToolDefinition:
    tool_id: str
    name: str
    description: str
    tool_type: ToolType
    version: str
    parameters: List[ToolParameter]
    function: Callable
    status: ToolStatus = ToolStatus.ACTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    registered_at: datetime = field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None
    usage_count: int = 0
    error_count: int = 0
    average_execution_time: float = 0.0


@dataclass
class ToolExecutionContext:
    tool_id: str
    execution_id: str
    parameters: Dict[str, Any]
    context: Dict[str, Any]
    started_at: datetime
    requester: str


@dataclass
class ToolExecutionResult:
    execution_id: str
    tool_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time_ms: float = 0.0
    completed_at: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ToolRegistry:
    """Registry and execution manager for external integration tools"""

    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.execution_history: List[ToolExecutionResult] = []
        self.active_executions: Dict[str, ToolExecutionContext] = {}

        # Registry statistics
        self.stats = {
            "total_registrations": 0,
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0
        }

    def register_tool(
        self,
        name: str,
        description: str,
        tool_type: ToolType,
        function: Callable,
        parameters: List[ToolParameter] = None,
        version: str = "1.0.0",
        metadata: Dict[str, Any] = None
    ) -> str:
        """Register a new tool in the registry"""

        tool_id = str(uuid.uuid4())

        # Auto-detect parameters if not provided
        if parameters is None:
            parameters = self._auto_detect_parameters(function)

        tool_definition = ToolDefinition(
            tool_id=tool_id,
            name=name,
            description=description,
            tool_type=tool_type,
            version=version,
            parameters=parameters,
            function=function,
            metadata=metadata or {}
        )

        self.tools[tool_id] = tool_definition
        self.stats["total_registrations"] += 1

        print(f"Registered tool: {name} (ID: {tool_id})")
        return tool_id

    async def execute_tool(
        self,
        tool_id: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None,
        requester: str = "system"
    ) -> ToolExecutionResult:
        """Execute a registered tool"""

        execution_id = str(uuid.uuid4())
        start_time = datetime.utcnow()

        # Get tool definition
        tool = self.tools.get(tool_id)
        if not tool:
            return ToolExecutionResult(
                execution_id=execution_id,
                tool_id=tool_id,
                success=False,
                error=f"Tool {tool_id} not found",
                execution_time_ms=0.0
            )

        # Check tool status
        if tool.status != ToolStatus.ACTIVE:
            return ToolExecutionResult(
                execution_id=execution_id,
                tool_id=tool_id,
                success=False,
                error=f"Tool {tool.name} is not active (status: {tool.status})",
                execution_time_ms=0.0
            )

        # Validate parameters
        validation_result = self._validate_parameters(tool, parameters)
        if not validation_result["valid"]:
            return ToolExecutionResult(
                execution_id=execution_id,
                tool_id=tool_id,
                success=False,
                error=f"Parameter validation failed: {validation_result['errors']}",
                execution_time_ms=0.0
            )

        # Create execution context
        execution_context = ToolExecutionContext(
            tool_id=tool_id,
            execution_id=execution_id,
            parameters=parameters,
            context=context or {},
            started_at=start_time,
            requester=requester
        )

        self.active_executions[execution_id] = execution_context

        try:
            # Execute the tool function
            if asyncio.iscoroutinefunction(tool.function):
                result = await tool.function(**parameters)
            else:
                result = tool.function(**parameters)

            # Calculate execution time
            execution_time = (datetime.utcnow() -
                              start_time).total_seconds() * 1000

            # Update tool statistics
            tool.last_used = datetime.utcnow()
            tool.usage_count += 1
            tool.average_execution_time = (
                (tool.average_execution_time * (tool.usage_count - 1) +
                 execution_time) / tool.usage_count
            )

            # Create success result
            execution_result = ToolExecutionResult(
                execution_id=execution_id,
                tool_id=tool_id,
                success=True,
                result=result,
                execution_time_ms=execution_time,
                metadata={
                    "tool_name": tool.name,
                    "tool_version": tool.version,
                    "requester": requester
                }
            )

            self.stats["total_executions"] += 1
            self.stats["successful_executions"] += 1

        except Exception as e:
            # Calculate execution time for failed execution
            execution_time = (datetime.utcnow() -
                              start_time).total_seconds() * 1000

            # Update error statistics
            tool.error_count += 1

            # Create error result
            execution_result = ToolExecutionResult(
                execution_id=execution_id,
                tool_id=tool_id,
                success=False,
                error=str(e),
                execution_time_ms=execution_time,
                metadata={
                    "tool_name": tool.name,
                    "error_type": type(e).__name__,
                    "requester": requester
                }
            )

            self.stats["total_executions"] += 1
            self.stats["failed_executions"] += 1

            # Update tool status if too many errors
            if tool.error_count > 5 and tool.error_count / tool.usage_count > 0.5:
                tool.status = ToolStatus.ERROR
                print(
                    f"Tool {tool.name} marked as ERROR due to high failure rate")

        finally:
            # Clean up active execution
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]

            # Store execution history
            self.execution_history.append(execution_result)

            # Limit history size
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-1000:]

        return execution_result

    def get_tool(self, tool_id: str) -> Optional[ToolDefinition]:
        """Get tool definition by ID"""
        return self.tools.get(tool_id)

    def find_tools(
        self,
        tool_type: ToolType = None,
        name_pattern: str = None,
        status: ToolStatus = None
    ) -> List[ToolDefinition]:
        """Find tools by criteria"""

        results = []

        for tool in self.tools.values():
            if tool_type and tool.tool_type != tool_type:
                continue
            if name_pattern and name_pattern.lower() not in tool.name.lower():
                continue
            if status and tool.status != status:
                continue

            results.append(tool)

        return results

    def list_tools(self) -> List[ToolDefinition]:
        """List all registered tools"""
        return list(self.tools.values())

    def get_tool_statistics(self, tool_id: str = None) -> Dict[str, Any]:
        """Get tool usage statistics"""

        if tool_id:
            tool = self.tools.get(tool_id)
            if not tool:
                return {}

            return {
                "tool_id": tool_id,
                "name": tool.name,
                "usage_count": tool.usage_count,
                "error_count": tool.error_count,
                "success_rate": (tool.usage_count - tool.error_count) / tool.usage_count if tool.usage_count > 0 else 0.0,
                "average_execution_time_ms": tool.average_execution_time,
                "last_used": tool.last_used.isoformat() if tool.last_used else None,
                "status": tool.status
            }
        else:
            # Global statistics
            return {
                "total_tools": len(self.tools),
                "active_tools": len([t for t in self.tools.values() if t.status == ToolStatus.ACTIVE]),
                "total_executions": self.stats["total_executions"],
                "successful_executions": self.stats["successful_executions"],
                "failed_executions": self.stats["failed_executions"],
                "success_rate": self.stats["successful_executions"] / self.stats["total_executions"] if self.stats["total_executions"] > 0 else 0.0,
                "tools_by_type": self._get_tools_by_type_stats()
            }

    def update_tool_status(self, tool_id: str, status: ToolStatus) -> bool:
        """Update tool status"""

        tool = self.tools.get(tool_id)
        if not tool:
            return False

        old_status = tool.status
        tool.status = status

        print(f"Tool {tool.name} status changed from {old_status} to {status}")
        return True

    def unregister_tool(self, tool_id: str) -> bool:
        """Unregister a tool"""

        if tool_id in self.tools:
            tool = self.tools[tool_id]
            del self.tools[tool_id]
            print(f"Unregistered tool: {tool.name}")
            return True

        return False

    def _auto_detect_parameters(self, function: Callable) -> List[ToolParameter]:
        """Auto-detect function parameters"""

        parameters = []
        sig = inspect.signature(function)

        for param_name, param in sig.parameters.items():
            # Skip 'self' parameter
            if param_name == 'self':
                continue

            param_type = "str"  # Default type
            if param.annotation != param.empty:
                param_type = param.annotation.__name__ if hasattr(
                    param.annotation, '__name__') else str(param.annotation)

            required = param.default == param.empty
            default_value = param.default if param.default != param.empty else None

            tool_param = ToolParameter(
                name=param_name,
                type=param_type,
                description=f"Parameter {param_name}",
                required=required,
                default=default_value
            )

            parameters.append(tool_param)

        return parameters

    def _validate_parameters(self, tool: ToolDefinition, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Validate tool parameters"""

        errors = []

        # Check required parameters
        for param in tool.parameters:
            if param.required and param.name not in parameters:
                errors.append(f"Required parameter '{param.name}' missing")

            if param.name in parameters:
                value = parameters[param.name]

                # Type validation (simplified)
                if param.type == "int" and not isinstance(value, int):
                    try:
                        parameters[param.name] = int(value)
                    except ValueError:
                        errors.append(
                            f"Parameter '{param.name}' must be an integer")

                elif param.type == "float" and not isinstance(value, (int, float)):
                    try:
                        parameters[param.name] = float(value)
                    except ValueError:
                        errors.append(
                            f"Parameter '{param.name}' must be a number")

                elif param.type == "bool" and not isinstance(value, bool):
                    if isinstance(value, str):
                        parameters[param.name] = value.lower() in [
                            'true', '1', 'yes', 'on']
                    else:
                        errors.append(
                            f"Parameter '{param.name}' must be a boolean")

                # Allowed values validation
                if param.allowed_values and value not in param.allowed_values:
                    errors.append(
                        f"Parameter '{param.name}' must be one of: {param.allowed_values}")

        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    def _get_tools_by_type_stats(self) -> Dict[str, int]:
        """Get statistics by tool type"""

        stats = {}
        for tool in self.tools.values():
            tool_type = tool.tool_type.value
            stats[tool_type] = stats.get(tool_type, 0) + 1

        return stats

    def export_tool_definitions(self) -> Dict[str, Any]:
        """Export all tool definitions for AutoGen integration"""

        exported_tools = {}

        for tool_id, tool in self.tools.items():
            if tool.status == ToolStatus.ACTIVE:
                exported_tools[tool_id] = {
                    "name": tool.name,
                    "description": tool.description,
                    "type": tool.tool_type.value,
                    "parameters": [
                        {
                            "name": param.name,
                            "type": param.type,
                            "description": param.description,
                            "required": param.required,
                            "default": param.default
                        }
                        for param in tool.parameters
                    ],
                    "metadata": tool.metadata
                }

        return exported_tools

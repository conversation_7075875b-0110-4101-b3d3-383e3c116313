from .tools.tool_registry import Too<PERSON><PERSON>egistry, ToolType, Tool<PERSON>arameter
from .clients.tts_client import TTS<PERSON><PERSON>, TTSRequest, VoiceProfile
from .clients.search_client import UnifiedSearchClient
from .clients.youtube_client import YouTubeClient
from .clients.github_client import GitH<PERSON><PERSON>lient
from common.module_client import ModuleClient
from common.api_framework import create_api_app, create_success_response, create_error_response
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))


# Pydantic models for API

class GitHubAnalysisRequest(BaseModel):
    repository_url: str
    analysis_depth: str = "comprehensive"


class YouTubeAnalysisRequest(BaseModel):
    video_url: str
    include_comments: bool = True
    comment_limit: int = 50


class SearchRequest(BaseModel):
    query: str
    providers: List[str] = ["google", "bing", "duckduckgo"]
    max_results: int = 20
    result_type: str = "web"


class TTSGenerationRequest(BaseModel):
    text: str
    voice_id: str
    provider: str = "openai"
    speed: float = 1.0
    pitch: float = 1.0
    audio_format: str = "wav"


class ConversationTTSRequest(BaseModel):
    dialogue_segments: List[Dict[str, Any]]
    voice_assignments: Dict[str, str]  # speaker -> voice_id mapping


class ToolExecutionRequest(BaseModel):
    tool_id: str
    parameters: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None


# Global instances
module_client: Optional[ModuleClient] = None
github_client: Optional[GitHubClient] = None
youtube_client: Optional[YouTubeClient] = None
search_client: Optional[UnifiedSearchClient] = None
tts_client: Optional[TTSClient] = None
tool_registry: Optional[ToolRegistry] = None

# Create FastAPI app
app = create_api_app(
    title="External Integration Module",
    description="External API integration for GitHub, YouTube, Search, and TTS services",
    version="1.0.0",
    module_name="external-integration"
)


@app.on_event("startup")
async def startup():
    """Initialize module on startup"""
    global module_client, github_client, youtube_client, search_client, tts_client, tool_registry

    try:
        # Initialize module client
        module_client = ModuleClient(
            module_id="external-integration",
            config_registry_url=os.getenv(
                "CONFIG_REGISTRY_URL", "http://localhost:8000"),
            api_key=os.getenv("MODULE_API_KEY", "default-key")
        )

        # Get configuration
        config = await module_client.get_config()

        # Initialize API clients
        github_client = GitHubClient(
            token=os.getenv("GITHUB_TOKEN", config.get(
                "github", {}).get("token", ""))
        )

        youtube_client = YouTubeClient(
            api_key=os.getenv("YOUTUBE_API_KEY", config.get(
                "youtube", {}).get("api_key", ""))
        )

        search_client = UnifiedSearchClient({
            "google": {
                "api_key": os.getenv("GOOGLE_SEARCH_API_KEY", ""),
                "search_engine_id": os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")
            },
            "bing": {
                "subscription_key": os.getenv("BING_SEARCH_KEY", "")
            },
            "duckduckgo": {}
        })

        tts_client = TTSClient({
            "openai": {
                "api_key": os.getenv("OPENAI_API_KEY", "")
            },
            "elevenlabs": {
                "api_key": os.getenv("ELEVENLABS_API_KEY", "")
            },
            "azure": {
                "subscription_key": os.getenv("AZURE_TTS_KEY", ""),
                "region": os.getenv("AZURE_REGION", "eastus")
            }
        })

        # Initialize tool registry
        tool_registry = ToolRegistry()

        # Register tools
        await register_external_tools()

        # Register with service registry
        await module_client.register_self(
            name="external-integration",
            version="1.0.0",
            host=os.getenv("HOST", "localhost"),
            port=int(os.getenv("PORT", "8002")),
            capabilities=[
                "github_analysis",
                "youtube_analysis",
                "web_search",
                "text_to_speech",
                "tool_execution"
            ],
            metadata={
                "supported_providers": ["github", "youtube", "google", "bing", "duckduckgo", "openai", "elevenlabs", "azure"],
                "tool_count": len(tool_registry.tools) if tool_registry else 0
            }
        )

        print("External Integration Module started successfully")

    except Exception as e:
        print(f"Failed to start module: {e}")
        raise


async def register_external_tools():
    """Register all external integration tools"""

    # GitHub tools
    tool_registry.register_tool(
        name="analyze_github_repository",
        description="Analyze GitHub repository for comprehensive insights",
        tool_type=ToolType.GITHUB,
        function=analyze_github_repository_tool,
        parameters=[
            ToolParameter("repository_url", "str",
                          "GitHub repository URL", required=True),
            ToolParameter("analysis_depth", "str", "Analysis depth level",
                          required=False, default="comprehensive")
        ]
    )

    # YouTube tools
    tool_registry.register_tool(
        name="analyze_youtube_video",
        description="Analyze YouTube video content and extract insights",
        tool_type=ToolType.YOUTUBE,
        function=analyze_youtube_video_tool,
        parameters=[
            ToolParameter("video_url", "str",
                          "YouTube video URL", required=True),
            ToolParameter("include_comments", "bool",
                          "Include comment analysis", required=False, default=True),
            ToolParameter("comment_limit", "int",
                          "Maximum comments to analyze", required=False, default=50)
        ]
    )

    # Search tools
    tool_registry.register_tool(
        name="web_search",
        description="Search the web using multiple search providers",
        tool_type=ToolType.SEARCH,
        function=web_search_tool,
        parameters=[
            ToolParameter("query", "str", "Search query", required=True),
            ToolParameter(
                "max_results", "int", "Maximum results to return", required=False, default=20),
            ToolParameter("providers", "list", "Search providers to use",
                          required=False, default=["google", "bing"])
        ]
    )

    # TTS tools
    tool_registry.register_tool(
        name="generate_speech",
        description="Generate speech audio from text",
        tool_type=ToolType.TTS,
        function=generate_speech_tool,
        parameters=[
            ToolParameter(
                "text", "str", "Text to convert to speech", required=True),
            ToolParameter("voice_id", "str", "Voice ID to use", required=True),
            ToolParameter("provider", "str", "TTS provider",
                          required=False, default="openai"),
            ToolParameter("speed", "float", "Speech speed",
                          required=False, default=1.0)
        ]
    )

# Tool implementations


async def analyze_github_repository_tool(repository_url: str, analysis_depth: str = "comprehensive") -> Dict[str, Any]:
    """Tool function for GitHub repository analysis"""
    return await github_client.analyze_repository(repository_url)


async def analyze_youtube_video_tool(video_url: str, include_comments: bool = True, comment_limit: int = 50) -> Dict[str, Any]:
    """Tool function for YouTube video analysis"""
    return await youtube_client.analyze_video(video_url)


async def web_search_tool(query: str, max_results: int = 20, providers: List[str] = None) -> Dict[str, Any]:
    """Tool function for web search"""
    if providers is None:
        providers = ["google", "bing", "duckduckgo"]

    # Convert string provider names to enum values
    from .clients.search_client import SearchProvider
    provider_enums = []
    for provider in providers:
        try:
            provider_enums.append(SearchProvider(provider))
        except ValueError:
            continue

    results = await search_client.search_and_aggregate(query, provider_enums, max_results)

    return {
        "query": query,
        "results": [
            {
                "title": result.title,
                "url": result.url,
                "snippet": result.snippet,
                "source": result.source,
                "relevance_score": result.relevance_score
            }
            for result in results
        ],
        "total_results": len(results)
    }


async def generate_speech_tool(text: str, voice_id: str, provider: str = "openai", speed: float = 1.0) -> Dict[str, Any]:
    """Tool function for speech generation"""

    # Find voice profile
    voices = await tts_client.list_available_voices()
    voice_profile = None

    for voice in voices:
        if voice.voice_id == voice_id:
            voice_profile = voice
            break

    if not voice_profile:
        raise Exception(f"Voice {voice_id} not found")

    # Create TTS request
    from .clients.tts_client import TTSRequest, AudioFormat
    request = TTSRequest(
        text=text,
        voice_profile=voice_profile,
        speed=speed,
        audio_format=AudioFormat.WAV
    )

    # Generate speech
    result = await tts_client.generate_speech(request)

    # Encode audio data as base64 for transport
    import base64
    audio_base64 = base64.b64encode(result.audio_data).decode('utf-8')

    return {
        "audio_data": audio_base64,
        "duration_seconds": result.duration_seconds,
        "file_size_bytes": result.file_size_bytes,
        "quality_score": result.quality_score,
        "metadata": result.metadata
    }

# API Endpoints


@app.post("/api/github/analyze", response_model=Dict[str, Any])
async def analyze_github_repository(request: GitHubAnalysisRequest):
    """Analyze GitHub repository"""
    try:
        start_time = datetime.utcnow()

        result = await github_client.analyze_repository(request.repository_url)

        execution_time = (datetime.utcnow() -
                          start_time).total_seconds() * 1000

        return create_success_response(
            data=result,
            message="GitHub repository analyzed successfully",
            execution_time_ms=execution_time
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to analyze GitHub repository",
            errors=[str(e)]
        ).dict()


@app.post("/api/youtube/analyze", response_model=Dict[str, Any])
async def analyze_youtube_video(request: YouTubeAnalysisRequest):
    """Analyze YouTube video"""
    try:
        start_time = datetime.utcnow()

        result = await youtube_client.analyze_video(request.video_url)

        execution_time = (datetime.utcnow() -
                          start_time).total_seconds() * 1000

        return create_success_response(
            data=result,
            message="YouTube video analyzed successfully",
            execution_time_ms=execution_time
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to analyze YouTube video",
            errors=[str(e)]
        ).dict()


@app.post("/api/search", response_model=Dict[str, Any])
async def search_web(request: SearchRequest):
    """Search the web"""
    try:
        start_time = datetime.utcnow()

        # Convert string providers to enum
        from .clients.search_client import SearchProvider
        provider_enums = []
        for provider in request.providers:
            try:
                provider_enums.append(SearchProvider(provider))
            except ValueError:
                continue

        results = await search_client.search_and_aggregate(
            request.query,
            provider_enums,
            request.max_results
        )

        execution_time = (datetime.utcnow() -
                          start_time).total_seconds() * 1000

        return create_success_response(
            data={
                "query": request.query,
                "results": [
                    {
                        "title": result.title,
                        "url": result.url,
                        "snippet": result.snippet,
                        "source": result.source,
                        "relevance_score": result.relevance_score
                    }
                    for result in results
                ],
                "total_results": len(results)
            },
            message="Web search completed successfully",
            execution_time_ms=execution_time
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to perform web search",
            errors=[str(e)]
        ).dict()


@app.get("/api/tools", response_model=Dict[str, Any])
async def list_tools():
    """List all registered tools"""
    try:
        tools = tool_registry.list_tools()

        tool_info = []
        for tool in tools:
            tool_info.append({
                "tool_id": tool.tool_id,
                "name": tool.name,
                "description": tool.description,
                "type": tool.tool_type.value,
                "status": tool.status.value,
                "usage_count": tool.usage_count,
                "error_count": tool.error_count,
                "parameters": [
                    {
                        "name": param.name,
                        "type": param.type,
                        "description": param.description,
                        "required": param.required
                    }
                    for param in tool.parameters
                ]
            })

        return create_success_response(
            data=tool_info,
            message="Tools retrieved successfully"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to list tools",
            errors=[str(e)]
        ).dict()


@app.post("/api/tools/execute", response_model=Dict[str, Any])
async def execute_tool(request: ToolExecutionRequest):
    """Execute a registered tool"""
    try:
        start_time = datetime.utcnow()

        result = await tool_registry.execute_tool(
            tool_id=request.tool_id,
            parameters=request.parameters,
            context=request.context
        )

        execution_time = (datetime.utcnow() -
                          start_time).total_seconds() * 1000

        if result.success:
            return create_success_response(
                data={
                    "execution_id": result.execution_id,
                    "result": result.result,
                    "execution_time_ms": result.execution_time_ms
                },
                message="Tool executed successfully",
                execution_time_ms=execution_time
            ).dict()
        else:
            return create_error_response(
                message="Tool execution failed",
                errors=[result.error]
            ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to execute tool",
            errors=[str(e)]
        ).dict()


@app.get("/api/statistics", response_model=Dict[str, Any])
async def get_statistics():
    """Get module statistics"""
    try:
        stats = tool_registry.get_tool_statistics()

        return create_success_response(
            data=stats,
            message="Statistics retrieved successfully"
        ).dict()

    except Exception as e:
        return create_error_response(
            message="Failed to get statistics",
            errors=[str(e)]
        ).dict()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)

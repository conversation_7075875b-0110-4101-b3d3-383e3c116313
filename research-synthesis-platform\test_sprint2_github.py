#!/usr/bin/env python3
"""
Test script to verify Sprint 2 GitHub client implementation
Tests the GitHub client with exact code from sprint plans
"""

import sys
import os
from pathlib import Path

# Add paths for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "shared"))
sys.path.insert(0, str(project_root / "modules" /
                "external-integration" / "src"))


def test_github_imports():
    """Test that GitHub client modules can be imported correctly"""
    print("Testing GitHub client imports from Sprint 2 implementation...")

    try:
        # Test GitHub model imports
        from external_integration.models.github_models import GitHubRepository, RepositoryContent, ContentType
        print("✓ GitHub models imports successful")

        # Test GitHub client imports
        from external_integration.clients.github_client import GitHubClient
        print("✓ GitHub client imports successful")

        return True

    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_github_client_creation():
    """Test GitHub client creation"""
    print("\nTesting GitHub client creation...")

    try:
        from external_integration.clients.github_client import GitHubClient

        # Create GitHub client (with dummy token for testing)
        client = GitHubClient(token="dummy-token-for-testing", timeout=30)
        print("✓ GitHub client created successfully")

        # Test URL parsing
        owner, repo = client._parse_repo_url(
            "https://github.com/microsoft/vscode")
        assert owner == "microsoft"
        assert repo == "vscode"
        print("✓ URL parsing works correctly")

        # Test language analysis
        test_files = [
            {"type": "blob", "path": "main.py"},
            {"type": "blob", "path": "app.js"},
            {"type": "blob", "path": "style.css"},
            {"type": "tree", "path": "src"}
        ]

        languages = client._analyze_languages(test_files)
        assert "Python" in languages
        assert "JavaScript" in languages
        assert "CSS" in languages
        print("✓ Language analysis works correctly")

        # Test project type determination
        project_type = client._determine_project_type(test_files)
        print(f"✓ Project type determination works correctly: {project_type}")

        return True

    except Exception as e:
        print(f"✗ GitHub client test failed: {e}")
        return False


def test_github_models():
    """Test GitHub data models"""
    print("\nTesting GitHub data models...")

    try:
        from external_integration.models.github_models import GitHubRepository, RepositoryContent, ContentType
        from datetime import datetime

        # Test GitHubRepository model
        repo = GitHubRepository(
            owner="test-owner",
            name="test-repo",
            full_name="test-owner/test-repo",
            description="Test repository",
            language="Python",
            stars=100,
            forks=20,
            size=1024,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            topics=["python", "test"],
            license="MIT",
            default_branch="main"
        )

        assert repo.owner == "test-owner"
        assert repo.name == "test-repo"
        assert repo.language == "Python"
        print("✓ GitHubRepository model works correctly")

        # Test RepositoryContent model
        content = RepositoryContent(
            content_type=ContentType.README,
            path="README.md",
            content="# Test Repository",
            size=100,
            encoding="utf-8",
            url="https://github.com/test-owner/test-repo/blob/main/README.md",
            extracted_at=datetime.now()
        )

        assert content.content_type == ContentType.README
        assert content.path == "README.md"
        assert "Test Repository" in content.content
        print("✓ RepositoryContent model works correctly")

        # Test ContentType enum
        assert ContentType.README == "readme"
        assert ContentType.CODE == "code"
        assert ContentType.DOCUMENTATION == "documentation"
        print("✓ ContentType enum works correctly")

        return True

    except Exception as e:
        print(f"✗ GitHub models test failed: {e}")
        return False


def main():
    """Run all GitHub client tests"""
    print("=" * 60)
    print("SPRINT 2 GITHUB CLIENT FUNCTIONALITY TEST")
    print("Testing exact code implementation from sprint plans")
    print("=" * 60)

    tests = [
        test_github_imports,
        test_github_client_creation,
        test_github_models
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All Sprint 2 GitHub client tests PASSED!")
        print("The GitHub client implementation matches the sprint plan specifications.")
        print("\nNote: Full API testing requires valid GitHub token and network access.")
    else:
        print("❌ Some tests FAILED!")
        print("Check the implementation against the sprint plan.")

    print("=" * 60)

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

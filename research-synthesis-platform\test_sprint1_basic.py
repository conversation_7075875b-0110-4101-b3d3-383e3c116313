#!/usr/bin/env python3
"""
Basic test to verify Sprint 1 implementation works
Tests the Configuration Registry module with exact code from sprint plans
"""

import sys
import os
from pathlib import Path

# Add paths for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "shared"))
sys.path.insert(0, str(project_root / "modules" / "configuration-registry" / "src"))

def test_imports():
    """Test that all the modules can be imported correctly"""
    print("Testing imports from Sprint 1 implementation...")
    
    try:
        # Test shared common imports
        from common.api_framework import create_api_app, create_success_response, create_error_response
        from common.api_framework import APIResponse, APIRequest, HealthStatus, HealthResponse
        print("✓ Shared API framework imports successful")
        
        # Test configuration registry imports
        from configuration_registry.config.config_manager import ConfigurationManager, Environment
        print("✓ Configuration manager imports successful")
        
        from configuration_registry.registry.service_registry import ServiceRegistry, ServiceRegistration, ServiceStatus
        print("✓ Service registry imports successful")
        
        from configuration_registry.security.auth_manager import AuthenticationManager, Permission, ModuleCredentials
        print("✓ Authentication manager imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_configuration_manager():
    """Test the Configuration Manager functionality"""
    print("\nTesting Configuration Manager...")
    
    try:
        from configuration_registry.config.config_manager import ConfigurationManager
        
        # Create configuration manager
        config_manager = ConfigurationManager()
        print("✓ Configuration manager created")
        
        # Test base config loading
        base_config = config_manager.load_base_config()
        assert "environment" in base_config
        assert "logging" in base_config
        assert "security" in base_config
        print("✓ Base configuration loaded")
        
        # Test module config loading
        module_config = config_manager.load_module_config("core-agent-orchestration")
        assert "port" in module_config
        assert module_config["port"] == 8001
        print("✓ Module configuration loaded")
        
        # Test config retrieval
        config = config_manager.get_config("core-agent-orchestration", "port")
        assert config == 8001
        print("✓ Configuration retrieval works")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration manager test failed: {e}")
        return False

def test_service_registry():
    """Test the Service Registry functionality"""
    print("\nTesting Service Registry...")
    
    try:
        from configuration_registry.registry.service_registry import ServiceRegistry, ServiceRegistration, ServiceStatus
        from datetime import datetime
        
        # Create service registry
        registry = ServiceRegistry()
        print("✓ Service registry created")
        
        # Create a test service registration
        registration = ServiceRegistration(
            service_id="test-service-1",
            name="test-service",
            version="1.0.0",
            host="localhost",
            port=8001,
            health_check_url="http://localhost:8001/health",
            api_base_url="http://localhost:8001",
            capabilities=["test"],
            metadata={"test": True},
            registered_at=datetime.utcnow(),
            last_heartbeat=datetime.utcnow(),
            status=ServiceStatus.STARTING
        )
        print("✓ Service registration created")
        
        # Test service discovery (should be empty initially)
        services = registry.discover_services()
        assert len(services) == 0
        print("✓ Service discovery works (empty)")
        
        return True
        
    except Exception as e:
        print(f"✗ Service registry test failed: {e}")
        return False

def test_auth_manager():
    """Test the Authentication Manager functionality"""
    print("\nTesting Authentication Manager...")
    
    try:
        from configuration_registry.security.auth_manager import AuthenticationManager, Permission
        
        # Create auth manager
        auth_manager = AuthenticationManager("test-secret")
        print("✓ Authentication manager created")
        
        # Test module credentials creation
        credentials = auth_manager.create_module_credentials("test-module", [Permission.READ_CONFIG])
        assert credentials.module_id == "test-module"
        assert Permission.READ_CONFIG in credentials.permissions
        print("✓ Module credentials created")
        
        # Test API key validation
        validated = auth_manager.validate_api_key(credentials.api_key)
        assert validated is not None
        assert validated.module_id == "test-module"
        print("✓ API key validation works")
        
        # Test permission checking
        has_permission = auth_manager.check_permission("test-module", Permission.READ_CONFIG)
        assert has_permission is True
        print("✓ Permission checking works")
        
        return True
        
    except Exception as e:
        print(f"✗ Authentication manager test failed: {e}")
        return False

def test_api_framework():
    """Test the API Framework functionality"""
    print("\nTesting API Framework...")
    
    try:
        from common.api_framework import create_api_app, create_success_response, create_error_response
        from common.api_framework import HealthStatus
        
        # Test app creation
        app = create_api_app(
            title="Test App",
            description="Test Description", 
            version="1.0.0",
            module_name="test-module"
        )
        assert app.title == "Test App"
        print("✓ FastAPI app created")
        
        # Test response creation
        success_response = create_success_response(
            data={"test": "data"},
            message="Test success"
        )
        assert success_response.success is True
        assert success_response.data == {"test": "data"}
        print("✓ Success response created")
        
        error_response = create_error_response(
            message="Test error",
            errors=["Error 1", "Error 2"]
        )
        assert error_response.success is False
        assert len(error_response.errors) == 2
        print("✓ Error response created")
        
        return True
        
    except Exception as e:
        print(f"✗ API framework test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("SPRINT 1 BASIC FUNCTIONALITY TEST")
    print("Testing exact code implementation from sprint plans")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_configuration_manager,
        test_service_registry,
        test_auth_manager,
        test_api_framework
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Sprint 1 basic tests PASSED!")
        print("The implementation matches the sprint plan specifications.")
    else:
        print("❌ Some tests FAILED!")
        print("Check the implementation against the sprint plan.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

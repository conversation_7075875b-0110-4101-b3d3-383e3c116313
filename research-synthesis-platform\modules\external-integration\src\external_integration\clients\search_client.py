import httpx
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import json
import re
from urllib.parse import quote


class SearchProvider(str, Enum):
    GOOGLE = "google"
    BING = "bing"
    DUCKDUCKGO = "duckduckgo"


class SearchResultType(str, Enum):
    WEB = "web"
    NEWS = "news"
    ACADEMIC = "academic"
    IMAGE = "image"
    VIDEO = "video"


@dataclass
class SearchResult:
    title: str
    url: str
    snippet: str
    source: str
    published_date: Optional[datetime]
    result_type: SearchResultType
    relevance_score: float
    metadata: Dict[str, Any]


@dataclass
class SearchQuery:
    query: str
    provider: SearchProvider
    result_type: SearchResultType
    max_results: int
    language: str
    region: str
    safe_search: bool


class UnifiedSearchClient:
    """Unified search client supporting multiple search providers"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.timeout = config.get("timeout", 30)

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "User-Agent": "Research-Synthesis-Platform/1.0"
            }
        )

        # Provider configurations
        self.providers = {
            SearchProvider.GOOGLE: GoogleSearchProvider(config.get("google", {}), self.client),
            SearchProvider.BING: BingSearchProvider(config.get("bing", {}), self.client),
            SearchProvider.DUCKDUCKGO: DuckDuckGoSearchProvider(
                config.get("duckduckgo", {}), self.client)
        }

        # Rate limiting per provider
        self.rate_limits = {
            SearchProvider.GOOGLE: {"calls": 0, "reset_time": datetime.utcnow()},
            SearchProvider.BING: {"calls": 0, "reset_time": datetime.utcnow()},
            SearchProvider.DUCKDUCKGO: {
                "calls": 0, "reset_time": datetime.utcnow()}
        }

    async def search(
        self,
        query: str,
        providers: List[SearchProvider] = None,
        result_type: SearchResultType = SearchResultType.WEB,
        max_results: int = 10,
        language: str = "en",
        region: str = "us"
    ) -> Dict[str, List[SearchResult]]:
        """Perform unified search across multiple providers"""

        if providers is None:
            providers = [SearchProvider.GOOGLE,
                         SearchProvider.BING, SearchProvider.DUCKDUCKGO]

        search_query = SearchQuery(
            query=query,
            provider=providers[0],  # Will be overridden per provider
            result_type=result_type,
            max_results=max_results,
            language=language,
            region=region,
            safe_search=True
        )

        # Execute searches in parallel
        search_tasks = []
        for provider in providers:
            if provider in self.providers:
                search_query.provider = provider
                task = asyncio.create_task(
                    self._search_with_provider(provider, search_query),
                    name=f"search_{provider.value}"
                )
                search_tasks.append(task)

        # Gather results
        results = {}
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)

        for i, result in enumerate(search_results):
            provider = providers[i]
            if isinstance(result, Exception):
                print(f"Search failed for {provider}: {result}")
                results[provider.value] = []
            else:
                results[provider.value] = result

        return results

    async def _search_with_provider(
        self,
        provider: SearchProvider,
        search_query: SearchQuery
    ) -> List[SearchResult]:
        """Execute search with specific provider"""

        try:
            # Check rate limiting
            await self._check_rate_limit(provider)

            # Execute search
            provider_client = self.providers[provider]
            results = await provider_client.search(search_query)

            # Update rate limiting
            self._update_rate_limit(provider)

            return results

        except Exception as e:
            print(f"Search error with {provider}: {e}")
            return []

    async def search_and_aggregate(
        self,
        query: str,
        providers: List[SearchProvider] = None,
        max_results: int = 20
    ) -> List[SearchResult]:
        """Search across providers and aggregate/deduplicate results"""

        # Get results from all providers
        provider_results = await self.search(query, providers, max_results=max_results)

        # Aggregate and deduplicate
        aggregated_results = []
        seen_urls = set()

        # Combine results from all providers
        for provider, results in provider_results.items():
            for result in results:
                if result.url not in seen_urls:
                    seen_urls.add(result.url)
                    aggregated_results.append(result)

        # Sort by relevance score (descending)
        aggregated_results.sort(key=lambda x: x.relevance_score, reverse=True)

        # Return top results
        return aggregated_results[:max_results]

    async def _check_rate_limit(self, provider: SearchProvider):
        """Check and enforce rate limiting"""

        current_time = datetime.utcnow()
        rate_info = self.rate_limits[provider]

        # Reset counters if hour has passed
        if (current_time - rate_info["reset_time"]).total_seconds() > 3600:
            rate_info["calls"] = 0
            rate_info["reset_time"] = current_time

        # Check limits (simplified - adjust per provider's actual limits)
        limits = {
            SearchProvider.GOOGLE: 100,    # Custom Search API: 100 calls/day free
            SearchProvider.BING: 1000,     # Bing Web Search API: 1000 calls/month free
            SearchProvider.DUCKDUCKGO: 1000  # No official API limits for instant answers
        }

        if rate_info["calls"] >= limits[provider]:
            raise Exception(f"Rate limit exceeded for {provider}")

    def _update_rate_limit(self, provider: SearchProvider):
        """Update rate limiting counters"""
        self.rate_limits[provider]["calls"] += 1

    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()


class GoogleSearchProvider:
    """Google Custom Search API provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.api_key = config.get("api_key")
        self.search_engine_id = config.get("search_engine_id")
        self.client = client
        self.base_url = "https://www.googleapis.com/customsearch/v1"

    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """Perform Google search"""

        if not self.api_key or not self.search_engine_id:
            raise Exception(
                "Google Search API key and Search Engine ID required")

        params = {
            "key": self.api_key,
            "cx": self.search_engine_id,
            "q": search_query.query,
            "num": min(search_query.max_results, 10),  # Google API max 10
            "lr": f"lang_{search_query.language}",
            "gl": search_query.region,
            "safe": "active" if search_query.safe_search else "off"
        }

        response = await self.client.get(self.base_url, params=params)
        response.raise_for_status()

        data = response.json()
        results = []

        for item in data.get("items", []):
            result = SearchResult(
                title=item["title"],
                url=item["link"],
                snippet=item["snippet"],
                source="google",
                published_date=None,  # Google doesn't always provide this
                result_type=search_query.result_type,
                relevance_score=0.8,  # Base score for Google results
                metadata={
                    "display_link": item.get("displayLink", ""),
                    "formatted_url": item.get("formattedUrl", ""),
                    "cache_id": item.get("cacheId", "")
                }
            )
            results.append(result)

        return results


class BingSearchProvider:
    """Bing Web Search API provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.subscription_key = config.get("subscription_key")
        self.client = client
        self.base_url = "https://api.bing.microsoft.com/v7.0/search"

    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """Perform Bing search"""

        if not self.subscription_key:
            raise Exception("Bing Search subscription key required")

        headers = {
            "Ocp-Apim-Subscription-Key": self.subscription_key
        }

        params = {
            "q": search_query.query,
            "count": min(search_query.max_results, 50),  # Bing API max 50
            "mkt": f"{search_query.language}-{search_query.region}",
            "safeSearch": "Strict" if search_query.safe_search else "Off"
        }

        response = await self.client.get(self.base_url, params=params, headers=headers)
        response.raise_for_status()

        data = response.json()
        results = []

        for item in data.get("webPages", {}).get("value", []):
            # Parse date if available
            published_date = None
            if "dateLastCrawled" in item:
                try:
                    published_date = datetime.fromisoformat(
                        item["dateLastCrawled"].replace("Z", "+00:00"))
                except:
                    pass

            result = SearchResult(
                title=item["name"],
                url=item["url"],
                snippet=item["snippet"],
                source="bing",
                published_date=published_date,
                result_type=search_query.result_type,
                relevance_score=0.75,  # Base score for Bing results
                metadata={
                    "display_url": item.get("displayUrl", ""),
                    "deep_links": item.get("deepLinks", [])
                }
            )
            results.append(result)

        return results


class DuckDuckGoSearchProvider:
    """DuckDuckGo Instant Answers API provider"""

    def __init__(self, config: Dict[str, Any], client: httpx.AsyncClient):
        self.client = client
        self.base_url = "https://api.duckduckgo.com"

    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """Perform DuckDuckGo search"""

        # DuckDuckGo Instant Answers API
        params = {
            "q": search_query.query,
            "format": "json",
            "no_html": "1",
            "skip_disambig": "1"
        }

        response = await self.client.get(self.base_url, params=params)
        response.raise_for_status()

        data = response.json()
        results = []

        # Process instant answer
        if data.get("AbstractText"):
            result = SearchResult(
                title=data.get("Heading", search_query.query),
                url=data.get("AbstractURL", ""),
                snippet=data["AbstractText"],
                source="duckduckgo",
                published_date=None,
                result_type=search_query.result_type,
                relevance_score=0.7,
                metadata={
                    "abstract_source": data.get("AbstractSource", ""),
                    "answer_type": data.get("AnswerType", ""),
                    "definition": data.get("Definition", "")
                }
            )
            results.append(result)

        # Process related topics
        for topic in data.get("RelatedTopics", [])[:search_query.max_results - len(results)]:
            if isinstance(topic, dict) and topic.get("Text"):
                result = SearchResult(
                    title=topic.get("Text", "").split(" - ")[0],
                    url=topic.get("FirstURL", ""),
                    snippet=topic.get("Text", ""),
                    source="duckduckgo",
                    published_date=None,
                    result_type=search_query.result_type,
                    relevance_score=0.6,
                    metadata={
                        "icon": topic.get("Icon", {}),
                        "result": topic.get("Result", "")
                    }
                )
                results.append(result)

        return results

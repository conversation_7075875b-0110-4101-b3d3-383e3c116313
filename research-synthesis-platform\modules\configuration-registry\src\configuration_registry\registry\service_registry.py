from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import asyncio
import json
from enum import Enum


class ServiceStatus(str, Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    STARTING = "starting"
    STOPPING = "stopping"


@dataclass
class ServiceRegistration:
    service_id: str
    name: str
    version: str
    host: str
    port: int
    health_check_url: str
    api_base_url: str
    capabilities: List[str]
    metadata: Dict[str, Any]
    registered_at: datetime
    last_heartbeat: datetime
    status: ServiceStatus = ServiceStatus.UNKNOWN


class ServiceRegistry:
    """Service discovery and registration system"""
    
    def __init__(self):
        self.services: Dict[str, ServiceRegistration] = {}
        self.service_groups: Dict[str, List[str]] = {}
        self.heartbeat_interval = 30  # seconds
        self.health_check_timeout = 10  # seconds
        
    async def register_service(self, registration: ServiceRegistration) -> bool:
        """Register a new service"""
        try:
            # Validate service registration
            if not self._validate_registration(registration):
                return False
                
            # Update registration timestamps
            registration.registered_at = datetime.utcnow()
            registration.last_heartbeat = datetime.utcnow()
            registration.status = ServiceStatus.STARTING
            
            # Store service registration
            self.services[registration.service_id] = registration
            
            # Add to service groups
            if registration.name not in self.service_groups:
                self.service_groups[registration.name] = []
            self.service_groups[registration.name].append(registration.service_id)
            
            # Perform initial health check
            await self._perform_health_check(registration.service_id)
            
            print(f"Service registered: {registration.name} ({registration.service_id})")
            return True
            
        except Exception as e:
            print(f"Failed to register service {registration.service_id}: {e}")
            return False
    
    async def deregister_service(self, service_id: str) -> bool:
        """Deregister a service"""
        try:
            if service_id not in self.services:
                return False
                
            service = self.services[service_id]
            service.status = ServiceStatus.STOPPING
            
            # Remove from service groups
            if service.name in self.service_groups:
                if service_id in self.service_groups[service.name]:
                    self.service_groups[service.name].remove(service_id)
                    
                # Clean up empty groups
                if not self.service_groups[service.name]:
                    del self.service_groups[service.name]
            
            # Remove service
            del self.services[service_id]
            
            print(f"Service deregistered: {service.name} ({service_id})")
            return True
            
        except Exception as e:
            print(f"Failed to deregister service {service_id}: {e}")
            return False
    
    def discover_services(self, service_name: Optional[str] = None) -> List[ServiceRegistration]:
        """Discover available services"""
        if service_name:
            service_ids = self.service_groups.get(service_name, [])
            return [self.services[sid] for sid in service_ids 
                   if sid in self.services and self.services[sid].status == ServiceStatus.HEALTHY]
        else:
            return [service for service in self.services.values() 
                   if service.status == ServiceStatus.HEALTHY]
    
    def get_service(self, service_id: str) -> Optional[ServiceRegistration]:
        """Get specific service by ID"""
        return self.services.get(service_id)
    
    async def update_heartbeat(self, service_id: str) -> bool:
        """Update service heartbeat"""
        if service_id not in self.services:
            return False
            
        self.services[service_id].last_heartbeat = datetime.utcnow()
        
        # Perform health check if needed
        if self.services[service_id].status != ServiceStatus.HEALTHY:
            await self._perform_health_check(service_id)
            
        return True
    
    async def start_health_monitoring(self):
        """Start background health monitoring"""
        while True:
            await self._monitor_all_services()
            await asyncio.sleep(self.heartbeat_interval)
    
    async def _monitor_all_services(self):
        """Monitor health of all registered services"""
        current_time = datetime.utcnow()
        
        for service_id, service in list(self.services.items()):
            # Check if service has missed heartbeat
            time_since_heartbeat = current_time - service.last_heartbeat
            
            if time_since_heartbeat > timedelta(seconds=self.heartbeat_interval * 2):
                service.status = ServiceStatus.UNHEALTHY
                print(f"Service {service.name} ({service_id}) marked as unhealthy - missed heartbeat")
            else:
                # Perform active health check
                await self._perform_health_check(service_id)
    
    async def _perform_health_check(self, service_id: str):
        """Perform health check on a specific service"""
        try:
            service = self.services.get(service_id)
            if not service:
                return
                
            # Simulate health check (in real implementation, make HTTP request)
            # For Sprint 1, we'll mark services as healthy if they're recently registered
            current_time = datetime.utcnow()
            time_since_registration = current_time - service.registered_at
            
            if time_since_registration < timedelta(minutes=5):
                service.status = ServiceStatus.HEALTHY
            else:
                # In real implementation, make actual HTTP health check
                # await self._http_health_check(service)
                service.status = ServiceStatus.HEALTHY  # Simplified for Sprint 1
                
        except Exception as e:
            if service_id in self.services:
                self.services[service_id].status = ServiceStatus.UNHEALTHY
            print(f"Health check failed for {service_id}: {e}")
    
    def _validate_registration(self, registration: ServiceRegistration) -> bool:
        """Validate service registration data"""
        required_fields = [
            registration.service_id,
            registration.name,
            registration.host,
            registration.port,
            registration.health_check_url
        ]
        
        return all(field for field in required_fields)
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """Get service registry statistics"""
        total_services = len(self.services)
        healthy_services = len([s for s in self.services.values() if s.status == ServiceStatus.HEALTHY])
        unhealthy_services = len([s for s in self.services.values() if s.status == ServiceStatus.UNHEALTHY])
        
        return {
            "total_services": total_services,
            "healthy_services": healthy_services,
            "unhealthy_services": unhealthy_services,
            "service_groups": len(self.service_groups),
            "uptime_percentage": (healthy_services / total_services * 100) if total_services > 0 else 0
        }

#!/usr/bin/env python3
"""
Test script to verify Performance Optimizer implementation from Sprint 2
Tests the Performance Optimizer with exact code from sprint plans
"""

import sys
import os
from pathlib import Path
import time
import asyncio

# Add paths for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "modules" / "external-integration" / "src"))

def test_performance_optimizer_imports():
    """Test that Performance Optimizer can be imported correctly"""
    print("Testing Performance Optimizer imports from Sprint 2 implementation...")
    
    try:
        # Test Performance Optimizer imports
        from external_integration.core.performance_optimizer import (
            PerformanceOptimizer, PerformanceMetric, CacheEntry, measure_performance
        )
        print("✓ Performance Optimizer imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_performance_optimizer_creation():
    """Test Performance Optimizer creation and basic functionality"""
    print("\nTesting Performance Optimizer creation...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        from datetime import datetime
        
        # Create optimizer
        optimizer = PerformanceOptimizer()
        print("✓ Performance Optimizer created successfully")
        
        # Test initial state
        assert len(optimizer.metrics) == 0
        assert len(optimizer.cache) == 0
        assert optimizer.cache_enabled == True
        assert optimizer.connection_pooling_enabled == True
        assert optimizer.async_processing_enabled == True
        print("✓ Performance Optimizer initial state is correct")
        
        # Test thresholds
        assert "github_analysis" in optimizer.thresholds
        assert "youtube_analysis" in optimizer.thresholds
        assert "web_search" in optimizer.thresholds
        assert "tts_generation" in optimizer.thresholds
        assert "content_processing" in optimizer.thresholds
        print("✓ Performance thresholds are properly configured")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance Optimizer creation test failed: {e}")
        return False

def test_metric_recording():
    """Test metric recording functionality"""
    print("\nTesting metric recording...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        
        # Record some metrics
        optimizer.record_metric("test_operation", 1.5, True, {"test": "data"})
        optimizer.record_metric("test_operation", 2.0, True)
        optimizer.record_metric("test_operation", 0.5, False, {"error": "timeout"})
        
        assert len(optimizer.metrics) == 3
        print("✓ Metric recording works correctly")
        
        # Test metric properties
        first_metric = optimizer.metrics[0]
        assert first_metric.operation == "test_operation"
        assert first_metric.execution_time == 1.5
        assert first_metric.success == True
        assert first_metric.metadata["test"] == "data"
        print("✓ Metric properties are stored correctly")
        
        # Test metric limit (should keep only last 1000)
        for i in range(1100):
            optimizer.record_metric(f"operation_{i}", 0.1, True)
        
        assert len(optimizer.metrics) == 1000
        print("✓ Metric limit enforcement works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Metric recording test failed: {e}")
        return False

def test_cache_functionality():
    """Test cache functionality"""
    print("\nTesting cache functionality...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        
        # Test cache set and get
        test_data = {"key": "value", "number": 42}
        optimizer.set_cache("test_key", test_data, ttl=60)
        
        retrieved_data = optimizer.get_cache("test_key")
        assert retrieved_data == test_data
        print("✓ Cache set and get works correctly")
        
        # Test cache miss
        missing_data = optimizer.get_cache("nonexistent_key")
        assert missing_data is None
        print("✓ Cache miss handling works correctly")
        
        # Test cache key generation
        params = {"param1": "value1", "param2": "value2"}
        key1 = optimizer.generate_cache_key("operation", params)
        key2 = optimizer.generate_cache_key("operation", params)
        key3 = optimizer.generate_cache_key("operation", {"param2": "value2", "param1": "value1"})
        
        assert key1 == key2 == key3  # Should be deterministic
        print("✓ Cache key generation works correctly")
        
        # Test should_use_cache
        assert optimizer.should_use_cache("github_repository_info", {}) == True
        assert optimizer.should_use_cache("unknown_operation", {}) == False
        print("✓ Cache usage determination works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Cache functionality test failed: {e}")
        return False

def test_performance_statistics():
    """Test performance statistics functionality"""
    print("\nTesting performance statistics...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        
        # Record some test metrics
        optimizer.record_metric("test_op", 1.0, True)
        optimizer.record_metric("test_op", 2.0, True)
        optimizer.record_metric("test_op", 1.5, False)
        optimizer.record_metric("other_op", 0.5, True)
        
        # Test overall statistics
        overall_stats = optimizer.get_performance_statistics()
        assert overall_stats["total_operations"] == 4
        assert overall_stats["success_rate"] == 0.75  # 3 out of 4 successful
        print("✓ Overall statistics calculation works correctly")
        
        # Test operation-specific statistics
        test_op_stats = optimizer.get_performance_statistics("test_op")
        assert test_op_stats["total_operations"] == 3
        assert test_op_stats["success_rate"] == 2/3  # 2 out of 3 successful
        assert test_op_stats["average_execution_time"] == 1.5  # (1.0 + 2.0 + 1.5) / 3
        print("✓ Operation-specific statistics calculation works correctly")
        
        # Test cache statistics
        optimizer.set_cache("key1", "data1")
        optimizer.set_cache("key2", "data2")
        
        cache_stats = optimizer.get_cache_statistics()
        assert cache_stats["cache_enabled"] == True
        assert cache_stats["total_entries"] == 2
        print("✓ Cache statistics calculation works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance statistics test failed: {e}")
        return False

def test_optimization_recommendations():
    """Test optimization recommendations functionality"""
    print("\nTesting optimization recommendations...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        
        # Test with good performance
        optimizer.record_metric("test_op", 1.0, True)
        optimizer.record_metric("test_op", 1.5, True)
        
        recommendations = optimizer.get_optimization_recommendations()
        assert "Performance is within acceptable ranges." in recommendations
        print("✓ Good performance recommendations work correctly")
        
        # Test with poor performance
        optimizer.record_metric("github_analysis", 45.0, False)  # Exceeds threshold and fails
        optimizer.record_metric("github_analysis", 50.0, False)
        
        recommendations = optimizer.get_optimization_recommendations()
        assert any("High failure rate" in rec for rec in recommendations)
        assert any("github_analysis" in rec and "threshold" in rec for rec in recommendations)
        print("✓ Poor performance recommendations work correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Optimization recommendations test failed: {e}")
        return False

def test_performance_decorator():
    """Test performance measurement decorator"""
    print("\nTesting performance measurement decorator...")
    
    try:
        from external_integration.core.performance_optimizer import PerformanceOptimizer, measure_performance
        
        optimizer = PerformanceOptimizer()
        
        # Test sync function decoration
        @measure_performance(optimizer, "test_sync_operation")
        def test_sync_function(x, y):
            time.sleep(0.01)  # Simulate work
            return x + y
        
        result = test_sync_function(2, 3)
        assert result == 5
        assert len(optimizer.metrics) == 1
        assert optimizer.metrics[0].operation == "test_sync_operation"
        assert optimizer.metrics[0].success == True
        print("✓ Sync function decoration works correctly")
        
        # Test async function decoration
        @measure_performance(optimizer, "test_async_operation")
        async def test_async_function(x, y):
            await asyncio.sleep(0.01)  # Simulate async work
            return x * y
        
        # Note: In a real async test, we would use: result = await test_async_function(3, 4)
        # For this test, we just verify the decorator setup
        assert asyncio.iscoroutinefunction(test_async_function)
        print("✓ Async function decoration setup works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance decorator test failed: {e}")
        return False

def main():
    """Run all Performance Optimizer tests"""
    print("=" * 70)
    print("PERFORMANCE OPTIMIZER FUNCTIONALITY TEST")
    print("Testing exact code implementation from Sprint 2 plans")
    print("=" * 70)
    
    tests = [
        test_performance_optimizer_imports,
        test_performance_optimizer_creation,
        test_metric_recording,
        test_cache_functionality,
        test_performance_statistics,
        test_optimization_recommendations,
        test_performance_decorator
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 70)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Performance Optimizer tests PASSED!")
        print("The Performance Optimizer implementation includes:")
        print("  ✓ Comprehensive performance metric recording")
        print("  ✓ Intelligent caching with LRU eviction")
        print("  ✓ Performance statistics and monitoring")
        print("  ✓ Optimization recommendations")
        print("  ✓ Performance measurement decorators")
        print("  ✓ Cache key generation and management")
        print("  ✓ Threshold-based performance warnings")
        print("\nNote: Full async testing requires proper async test framework.")
    else:
        print("❌ Some tests FAILED!")
        print("Check the implementation against the sprint plan.")
    
    print("=" * 70)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

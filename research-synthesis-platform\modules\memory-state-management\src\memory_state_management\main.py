# Memory State Management Module - Sprint 3 Implementation
# Based on memory_state_module.py from folder 3

from shared.common.module_client import ModuleClient
from shared.common.api_framework import create_api_app, create_success_response, create_error_response
import uvicorn
from pydantic import BaseModel
from fastapi import FastAP<PERSON>, HTTPException, Depends
import asyncio
import json
import pickle
import hashlib
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
import uuid

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

# External dependencies (install as needed)
try:
    import redis.asyncio as redis
except ImportError:
    redis = None
    logging.warning("Redis not available, using in-memory storage")

try:
    import aiofiles
except ImportError:
    aiofiles = None
    logging.warning(
        "aiofiles not available, using synchronous file operations")

try:
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
    from sqlalchemy.orm import declarative_base, sessionmaker
    from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, LargeBinary, Index
    from sqlalchemy.dialects.postgresql import UUID
except ImportError:
    logging.warning("SQLAlchemy not available, using file-based storage")
    AsyncSession = None
    create_async_engine = None
    declarative_base = None


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import shared components

# Type definitions
T = TypeVar('T')

# Database Models (if SQLAlchemy is available)
if declarative_base:
    Base = declarative_base()

    class MemoryRecord(Base):
        __tablename__ = "memory_records"

        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        session_id = Column(String(255), nullable=False, index=True)
        agent_id = Column(String(255), nullable=False, index=True)
        memory_type = Column(String(50), nullable=False, index=True)
        content = Column(Text, nullable=False)
        metadata = Column(JSON, default={})
        created_at = Column(DateTime, default=datetime.utcnow, index=True)
        updated_at = Column(DateTime, default=datetime.utcnow,
                            onupdate=datetime.utcnow)
        expires_at = Column(DateTime, nullable=True, index=True)

        # Indexes for performance
        __table_args__ = (
            Index('idx_session_agent', 'session_id', 'agent_id'),
            Index('idx_type_created', 'memory_type', 'created_at'),
            Index('idx_expires', 'expires_at'),
        )
else:
    Base = None
    MemoryRecord = None

# Pydantic Models for API


class MemoryRequest(BaseModel):
    session_id: str
    agent_id: str
    memory_type: str
    content: str
    metadata: Optional[Dict[str, Any]] = {}
    expires_in_seconds: Optional[int] = None


class MemoryResponse(BaseModel):
    id: str
    session_id: str
    agent_id: str
    memory_type: str
    content: str
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]


class MemoryQuery(BaseModel):
    session_id: Optional[str] = None
    agent_id: Optional[str] = None
    memory_type: Optional[str] = None
    limit: Optional[int] = 100
    offset: Optional[int] = 0

# Memory Storage Interface


class MemoryStorage(ABC):
    """Abstract base class for memory storage backends"""

    @abstractmethod
    async def store_memory(self, memory_data: Dict[str, Any]) -> str:
        """Store memory and return memory ID"""
        pass

    @abstractmethod
    async def retrieve_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve memory by ID"""
        pass

    @abstractmethod
    async def query_memories(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Query memories based on criteria"""
        pass

    @abstractmethod
    async def update_memory(self, memory_id: str, updates: Dict[str, Any]) -> bool:
        """Update memory and return success status"""
        pass

    @abstractmethod
    async def delete_memory(self, memory_id: str) -> bool:
        """Delete memory and return success status"""
        pass

    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Clean up expired memories and return count"""
        pass

# In-Memory Storage Implementation (fallback)


class InMemoryStorage(MemoryStorage):
    """In-memory storage implementation for development/testing"""

    def __init__(self):
        self.memories: Dict[str, Dict[str, Any]] = {}
        self.lock = asyncio.Lock()

    async def store_memory(self, memory_data: Dict[str, Any]) -> str:
        async with self.lock:
            memory_id = str(uuid.uuid4())
            memory_data['id'] = memory_id
            memory_data['created_at'] = datetime.utcnow()
            memory_data['updated_at'] = datetime.utcnow()

            if memory_data.get('expires_in_seconds'):
                memory_data['expires_at'] = datetime.utcnow() + timedelta(
                    seconds=memory_data['expires_in_seconds']
                )

            self.memories[memory_id] = memory_data
            return memory_id

    async def retrieve_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        async with self.lock:
            memory = self.memories.get(memory_id)
            if memory and self._is_expired(memory):
                del self.memories[memory_id]
                return None
            return memory

    async def query_memories(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        async with self.lock:
            results = []
            for memory in self.memories.values():
                if self._is_expired(memory):
                    continue

                if self._matches_query(memory, query):
                    results.append(memory)

            # Apply pagination
            offset = query.get('offset', 0)
            limit = query.get('limit', 100)
            return results[offset:offset + limit]

    async def update_memory(self, memory_id: str, updates: Dict[str, Any]) -> bool:
        async with self.lock:
            if memory_id not in self.memories:
                return False

            memory = self.memories[memory_id]
            if self._is_expired(memory):
                del self.memories[memory_id]
                return False

            memory.update(updates)
            memory['updated_at'] = datetime.utcnow()
            return True

    async def delete_memory(self, memory_id: str) -> bool:
        async with self.lock:
            return self.memories.pop(memory_id, None) is not None

    async def cleanup_expired(self) -> int:
        async with self.lock:
            expired_ids = [
                memory_id for memory_id, memory in self.memories.items()
                if self._is_expired(memory)
            ]

            for memory_id in expired_ids:
                del self.memories[memory_id]

            return len(expired_ids)

    def _is_expired(self, memory: Dict[str, Any]) -> bool:
        expires_at = memory.get('expires_at')
        if expires_at and isinstance(expires_at, datetime):
            return datetime.utcnow() > expires_at
        return False

    def _matches_query(self, memory: Dict[str, Any], query: Dict[str, Any]) -> bool:
        for key, value in query.items():
            if key in ['limit', 'offset']:
                continue
            if memory.get(key) != value:
                return False
        return True

# Memory Manager - Main Service Class


class MemoryManager:
    """Main memory management service"""

    def __init__(self, storage: MemoryStorage):
        self.storage = storage
        self.module_client = ModuleClient("memory-state-management")

    async def store_memory(self, request: MemoryRequest) -> MemoryResponse:
        """Store a new memory"""
        try:
            memory_data = {
                'session_id': request.session_id,
                'agent_id': request.agent_id,
                'memory_type': request.memory_type,
                'content': request.content,
                'metadata': request.metadata or {},
                'expires_in_seconds': request.expires_in_seconds
            }

            memory_id = await self.storage.store_memory(memory_data)
            stored_memory = await self.storage.retrieve_memory(memory_id)

            if not stored_memory:
                raise HTTPException(
                    status_code=500, detail="Failed to store memory")

            return MemoryResponse(**stored_memory)

        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def retrieve_memory(self, memory_id: str) -> Optional[MemoryResponse]:
        """Retrieve a memory by ID"""
        try:
            memory_data = await self.storage.retrieve_memory(memory_id)
            if memory_data:
                return MemoryResponse(**memory_data)
            return None

        except Exception as e:
            logger.error(f"Error retrieving memory {memory_id}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def query_memories(self, query: MemoryQuery) -> List[MemoryResponse]:
        """Query memories based on criteria"""
        try:
            query_dict = {k: v for k, v in query.dict().items()
                          if v is not None}
            memories = await self.storage.query_memories(query_dict)
            return [MemoryResponse(**memory) for memory in memories]

        except Exception as e:
            logger.error(f"Error querying memories: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def update_memory(self, memory_id: str, updates: Dict[str, Any]) -> bool:
        """Update a memory"""
        try:
            updates['updated_at'] = datetime.utcnow()
            return await self.storage.update_memory(memory_id, updates)

        except Exception as e:
            logger.error(f"Error updating memory {memory_id}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory"""
        try:
            return await self.storage.delete_memory(memory_id)

        except Exception as e:
            logger.error(f"Error deleting memory {memory_id}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def cleanup_expired_memories(self) -> int:
        """Clean up expired memories"""
        try:
            return await self.storage.cleanup_expired()

        except Exception as e:
            logger.error(f"Error cleaning up expired memories: {e}")
            return 0


# Initialize storage and manager
storage = InMemoryStorage()  # Default to in-memory storage
memory_manager = MemoryManager(storage)

# Create FastAPI app
app = create_api_app(
    title="Memory State Management API",
    description="Memory and state management service for the research synthesis platform",
    version="1.0.0"
)

# API Routes


@app.post("/memories", response_model=MemoryResponse)
async def store_memory_endpoint(request: MemoryRequest):
    """Store a new memory"""
    try:
        result = await memory_manager.store_memory(request)
        return create_success_response(result.dict())
    except Exception as e:
        logger.error(f"Error in store_memory_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/memories/{memory_id}", response_model=MemoryResponse)
async def get_memory_endpoint(memory_id: str):
    """Retrieve a memory by ID"""
    try:
        result = await memory_manager.retrieve_memory(memory_id)
        if result:
            return create_success_response(result.dict())
        else:
            raise HTTPException(status_code=404, detail="Memory not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_memory_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.post("/memories/query", response_model=List[MemoryResponse])
async def query_memories_endpoint(query: MemoryQuery):
    """Query memories based on criteria"""
    try:
        results = await memory_manager.query_memories(query)
        return create_success_response([result.dict() for result in results])
    except Exception as e:
        logger.error(f"Error in query_memories_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.put("/memories/{memory_id}")
async def update_memory_endpoint(memory_id: str, updates: Dict[str, Any]):
    """Update a memory"""
    try:
        success = await memory_manager.update_memory(memory_id, updates)
        if success:
            return create_success_response({"updated": True})
        else:
            raise HTTPException(status_code=404, detail="Memory not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_memory_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.delete("/memories/{memory_id}")
async def delete_memory_endpoint(memory_id: str):
    """Delete a memory"""
    try:
        success = await memory_manager.delete_memory(memory_id)
        if success:
            return create_success_response({"deleted": True})
        else:
            raise HTTPException(status_code=404, detail="Memory not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_memory_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.post("/memories/cleanup")
async def cleanup_expired_endpoint():
    """Clean up expired memories"""
    try:
        count = await memory_manager.cleanup_expired_memories()
        return create_success_response({"cleaned_up": count})
    except Exception as e:
        logger.error(f"Error in cleanup_expired_endpoint: {e}")
        return create_error_response(str(e), 500)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return create_success_response({
        "status": "healthy",
        "service": "memory-state-management",
        "timestamp": datetime.utcnow().isoformat()
    })


@app.get("/stats")
async def get_stats():
    """Get memory statistics"""
    try:
        # For in-memory storage, we can get basic stats
        if isinstance(storage, InMemoryStorage):
            total_memories = len(storage.memories)
            return create_success_response({
                "total_memories": total_memories,
                "storage_type": "in_memory"
            })
        else:
            return create_success_response({
                "storage_type": "external",
                "message": "Stats not available for external storage"
            })
    except Exception as e:
        logger.error(f"Error in get_stats: {e}")
        return create_error_response(str(e), 500)

# Context Management Functions


class ContextManager:
    """Manages context sharing between modules and agents"""

    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager

    async def store_context(self, session_id: str, agent_id: str, context: Dict[str, Any],
                            context_type: str = "conversation") -> str:
        """Store context for an agent in a session"""
        request = MemoryRequest(
            session_id=session_id,
            agent_id=agent_id,
            memory_type=f"context_{context_type}",
            content=json.dumps(context),
            metadata={"context_type": context_type, "size": len(str(context))}
        )

        response = await self.memory_manager.store_memory(request)
        return response.id

    async def retrieve_context(self, session_id: str, agent_id: str,
                               context_type: str = "conversation") -> Optional[Dict[str, Any]]:
        """Retrieve the latest context for an agent in a session"""
        query = MemoryQuery(
            session_id=session_id,
            agent_id=agent_id,
            memory_type=f"context_{context_type}",
            limit=1
        )

        memories = await self.memory_manager.query_memories(query)
        if memories:
            return json.loads(memories[0].content)
        return None

    async def share_context_between_agents(self, session_id: str, from_agent: str,
                                           to_agent: str, context_type: str = "shared") -> bool:
        """Share context between agents in the same session"""
        context = await self.retrieve_context(session_id, from_agent, context_type)
        if context:
            await self.store_context(session_id, to_agent, context, f"shared_from_{from_agent}")
            return True
        return False


# Initialize context manager
context_manager = ContextManager(memory_manager)

# Context API endpoints


@app.post("/context/store")
async def store_context_endpoint(session_id: str, agent_id: str, context: Dict[str, Any],
                                 context_type: str = "conversation"):
    """Store context for an agent"""
    try:
        context_id = await context_manager.store_context(session_id, agent_id, context, context_type)
        return create_success_response({"context_id": context_id})
    except Exception as e:
        logger.error(f"Error storing context: {e}")
        return create_error_response(str(e), 500)


@app.get("/context/retrieve")
async def retrieve_context_endpoint(session_id: str, agent_id: str, context_type: str = "conversation"):
    """Retrieve context for an agent"""
    try:
        context = await context_manager.retrieve_context(session_id, agent_id, context_type)
        if context:
            return create_success_response(context)
        else:
            return create_success_response(None)
    except Exception as e:
        logger.error(f"Error retrieving context: {e}")
        return create_error_response(str(e), 500)


@app.post("/context/share")
async def share_context_endpoint(session_id: str, from_agent: str, to_agent: str,
                                 context_type: str = "shared"):
    """Share context between agents"""
    try:
        success = await context_manager.share_context_between_agents(
            session_id, from_agent, to_agent, context_type
        )
        return create_success_response({"shared": success})
    except Exception as e:
        logger.error(f"Error sharing context: {e}")
        return create_error_response(str(e), 500)

# Background task for cleanup


async def periodic_cleanup():
    """Periodic cleanup of expired memories"""
    while True:
        try:
            await asyncio.sleep(3600)  # Run every hour
            count = await memory_manager.cleanup_expired_memories()
            logger.info(f"Cleaned up {count} expired memories")
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")

# Startup event


@app.on_event("startup")
async def startup_event():
    """Initialize the memory management service"""
    logger.info("Starting Memory State Management service...")

    # Start background cleanup task
    asyncio.create_task(periodic_cleanup())

    logger.info("Memory State Management service started successfully")

# Main execution
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8003,  # Memory State Management port
        reload=True,
        log_level="info"
    )

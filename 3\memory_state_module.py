# memory_state_management/memory_manager.py
import asyncio
import json
import pickle
import hashlib
import logging
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
import redis.asyncio as redis
import aiofiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, LargeBinary, Index
from sqlalchemy.dialects.postgresql import UUID
import uuid
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type definitions
T = TypeVar('T')
Base = declarative_base()

# Database Models
class MemoryRecord(Base):
    __tablename__ = "memory_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String(255), nullable=False, index=True)
    agent_id = Column(String(255), nullable=False, index=True)
    memory_type = Column(String(50), nullable=False, index=True)
    content = Column(Text, nullable=False)
    metadata = Column(JSON, default={})
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True, index=True)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_session_agent', 'session_id', 'agent_id'),
        Index('idx_type_created', 'memory_type', 'created_at'),
        Index('idx_expires', 'expires_at'),
    )

class StateRecord(Base):
    __tablename__ = "state_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(String(255), nullable=False, index=True)
    module_id = Column(String(255), nullable=False, index=True)
    state_key = Column(String(255), nullable=False)
    state_data = Column(LargeBinary, nullable=False)  # Pickled state
    version = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_workflow_module', 'workflow_id', 'module_id'),
        Index('idx_state_key', 'state_key'),
    )

# Pydantic models for API
class MemoryRequest(BaseModel):
    session_id: str
    agent_id: str
    memory_type: str
    content: str
    metadata: Optional[Dict[str, Any]] = {}
    ttl_seconds: Optional[int] = None

class MemoryResponse(BaseModel):
    id: str
    session_id: str
    agent_id: str
    memory_type: str
    content: str
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

class StateRequest(BaseModel):
    workflow_id: str
    module_id: str
    state_key: str
    state_data: Any

class StateResponse(BaseModel):
    id: str
    workflow_id: str
    module_id: str
    state_key: str
    version: int
    created_at: datetime
    updated_at: datetime

# Abstract Memory Store Interface
class MemoryStore(ABC, Generic[T]):
    @abstractmethod
    async def store(self, key: str, value: T, ttl: Optional[int] = None) -> str:
        pass
    
    @abstractmethod
    async def retrieve(self, key: str) -> Optional[T]:
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        pass
    
    @abstractmethod
    async def search(self, pattern: str) -> List[str]:
        pass

# Redis Memory Store Implementation
class RedisMemoryStore(MemoryStore[str]):
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
    
    async def connect(self):
        self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
        await self.redis_client.ping()
        logger.info("Connected to Redis")
    
    async def disconnect(self):
        if self.redis_client:
            await self.redis_client.close()
    
    async def store(self, key: str, value: str, ttl: Optional[int] = None) -> str:
        if not self.redis_client:
            await self.connect()
        
        await self.redis_client.set(key, value, ex=ttl)
        return key
    
    async def retrieve(self, key: str) -> Optional[str]:
        if not self.redis_client:
            await self.connect()
        
        return await self.redis_client.get(key)
    
    async def delete(self, key: str) -> bool:
        if not self.redis_client:
            await self.connect()
        
        result = await self.redis_client.delete(key)
        return result > 0
    
    async def exists(self, key: str) -> bool:
        if not self.redis_client:
            await self.connect()
        
        return await self.redis_client.exists(key) > 0
    
    async def search(self, pattern: str) -> List[str]:
        if not self.redis_client:
            await self.connect()
        
        return await self.redis_client.keys(pattern)

# Memory Context Manager
@dataclass
class MemoryContext:
    session_id: str
    agent_id: str
    workflow_id: str
    created_at: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}

# Context Compression Engine
class ContextCompressor:
    def __init__(self, max_context_length: int = 8000):
        self.max_context_length = max_context_length
    
    async def compress_context(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Compress context while preserving important information"""
        if len(str(messages)) <= self.max_context_length:
            return messages
        
        # Priority-based compression
        prioritized_messages = self._prioritize_messages(messages)
        compressed = self._truncate_messages(prioritized_messages)
        
        return compressed
    
    def _prioritize_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Assign priority scores to messages"""
        for msg in messages:
            score = 0
            content = msg.get('content', '').lower()
            
            # Higher priority for system messages
            if msg.get('role') == 'system':
                score += 100
            
            # Higher priority for recent messages
            timestamp = msg.get('timestamp', datetime.utcnow())
            age_hours = (datetime.utcnow() - timestamp).total_seconds() / 3600
            score += max(0, 50 - age_hours)
            
            # Higher priority for messages with key terms
            key_terms = ['error', 'result', 'important', 'final', 'summary']
            for term in key_terms:
                if term in content:
                    score += 20
            
            msg['_priority_score'] = score
        
        return sorted(messages, key=lambda x: x.get('_priority_score', 0), reverse=True)
    
    def _truncate_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Truncate messages to fit within context length"""
        total_length = 0
        result = []
        
        for msg in messages:
            msg_length = len(str(msg))
            if total_length + msg_length <= self.max_context_length:
                result.append(msg)
                total_length += msg_length
            else:
                break
        
        return result

# Advanced Memory Manager
class AdvancedMemoryManager:
    def __init__(
        self,
        db_url: str = "postgresql+asyncpg://user:pass@localhost/memory_db",
        redis_url: str = "redis://localhost:6379"
    ):
        self.db_url = db_url
        self.redis_url = redis_url
        self.engine = None
        self.async_session = None
        self.redis_store = RedisMemoryStore(redis_url)
        self.context_compressor = ContextCompressor()
        
    async def initialize(self):
        """Initialize database and connections"""
        self.engine = create_async_engine(self.db_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # Create tables
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        await self.redis_store.connect()
        logger.info("Memory Manager initialized")
    
    async def cleanup(self):
        """Cleanup connections"""
        await self.redis_store.disconnect()
        if self.engine:
            await self.engine.dispose()
    
    @asynccontextmanager
    async def get_session(self):
        async with self.async_session() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise e
    
    # Short-term Memory Management
    async def store_short_term_memory(
        self,
        context: MemoryContext,
        memory_type: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        ttl_seconds: int = 3600
    ) -> str:
        """Store short-term memory in Redis with TTL"""
        memory_key = f"short:{context.session_id}:{context.agent_id}:{memory_type}:{uuid.uuid4()}"
        
        memory_data = {
            "content": content,
            "metadata": metadata or {},
            "context": asdict(context),
            "created_at": datetime.utcnow().isoformat()
        }
        
        await self.redis_store.store(
            memory_key,
            json.dumps(memory_data),
            ttl=ttl_seconds
        )
        
        return memory_key
    
    async def retrieve_short_term_memory(
        self,
        context: MemoryContext,
        memory_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Retrieve short-term memories from Redis"""
        pattern = f"short:{context.session_id}:{context.agent_id}"
        if memory_type:
            pattern += f":{memory_type}:*"
        else:
            pattern += ":*"
        
        keys = await self.redis_store.search(pattern)
        memories = []
        
        for key in keys:
            data = await self.redis_store.retrieve(key)
            if data:
                memories.append(json.loads(data))
        
        return sorted(memories, key=lambda x: x['created_at'], reverse=True)
    
    # Persistent Memory Management
    async def store_persistent_memory(
        self,
        context: MemoryContext,
        memory_type: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        expires_at: Optional[datetime] = None
    ) -> str:
        """Store persistent memory in database"""
        async with self.get_session() as session:
            memory_record = MemoryRecord(
                session_id=context.session_id,
                agent_id=context.agent_id,
                memory_type=memory_type,
                content=content,
                metadata=metadata or {},
                expires_at=expires_at
            )
            
            session.add(memory_record)
            await session.flush()
            
            return str(memory_record.id)
    
    async def retrieve_persistent_memory(
        self,
        context: MemoryContext,
        memory_type: Optional[str] = None,
        limit: int = 100
    ) -> List[MemoryRecord]:
        """Retrieve persistent memories from database"""
        async with self.get_session() as session:
            query = session.query(MemoryRecord).filter(
                MemoryRecord.session_id == context.session_id,
                MemoryRecord.agent_id == context.agent_id
            )
            
            if memory_type:
                query = query.filter(MemoryRecord.memory_type == memory_type)
            
            # Filter out expired memories
            query = query.filter(
                (MemoryRecord.expires_at.is_(None)) |
                (MemoryRecord.expires_at > datetime.utcnow())
            )
            
            query = query.order_by(MemoryRecord.created_at.desc()).limit(limit)
            
            result = await query.all()
            return result
    
    # Context Management
    async def get_compressed_context(
        self,
        context: MemoryContext,
        include_short_term: bool = True,
        include_persistent: bool = True
    ) -> Dict[str, Any]:
        """Get compressed context for agent interactions"""
        all_memories = []
        
        if include_short_term:
            short_term = await self.retrieve_short_term_memory(context)
            all_memories.extend(short_term)
        
        if include_persistent:
            persistent = await self.retrieve_persistent_memory(context)
            all_memories.extend([{
                "content": mem.content,
                "metadata": mem.metadata,
                "created_at": mem.created_at.isoformat(),
                "memory_type": mem.memory_type
            } for mem in persistent])
        
        # Compress context
        compressed_memories = await self.context_compressor.compress_context(all_memories)
        
        return {
            "context": context,
            "memories": compressed_memories,
            "total_original": len(all_memories),
            "total_compressed": len(compressed_memories),
            "compression_ratio": len(compressed_memories) / max(len(all_memories), 1)
        }
    
    # State Management
    async def store_module_state(
        self,
        workflow_id: str,
        module_id: str,
        state_key: str,
        state_data: Any
    ) -> str:
        """Store module state with versioning"""
        async with self.get_session() as session:
            # Check for existing state
            existing = await session.query(StateRecord).filter(
                StateRecord.workflow_id == workflow_id,
                StateRecord.module_id == module_id,
                StateRecord.state_key == state_key
            ).first()
            
            version = 1
            if existing:
                version = existing.version + 1
            
            state_record = StateRecord(
                workflow_id=workflow_id,
                module_id=module_id,
                state_key=state_key,
                state_data=pickle.dumps(state_data),
                version=version
            )
            
            session.add(state_record)
            await session.flush()
            
            return str(state_record.id)
    
    async def retrieve_module_state(
        self,
        workflow_id: str,
        module_id: str,
        state_key: str
    ) -> Optional[Any]:
        """Retrieve latest module state"""
        async with self.get_session() as session:
            state_record = await session.query(StateRecord).filter(
                StateRecord.workflow_id == workflow_id,
                StateRecord.module_id == module_id,
                StateRecord.state_key == state_key
            ).order_by(StateRecord.version.desc()).first()
            
            if state_record:
                return pickle.loads(state_record.state_data)
            
            return None
    
    # Cleanup Operations
    async def cleanup_expired_memories(self):
        """Remove expired memories from database"""
        async with self.get_session() as session:
            await session.query(MemoryRecord).filter(
                MemoryRecord.expires_at < datetime.utcnow()
            ).delete()
            
            logger.info("Cleaned up expired memories")
    
    async def optimize_memory_storage(self):
        """Optimize memory storage by compressing old memories"""
        # Implementation for memory optimization
        pass

# FastAPI Application for Memory Module
def create_memory_app(memory_manager: AdvancedMemoryManager) -> FastAPI:
    app = FastAPI(title="Memory & State Management Module", version="1.0.0")
    
    @app.on_event("startup")
    async def startup():
        await memory_manager.initialize()
    
    @app.on_event("shutdown")
    async def shutdown():
        await memory_manager.cleanup()
    
    @app.post("/memory/store", response_model=dict)
    async def store_memory(request: MemoryRequest):
        context = MemoryContext(
            session_id=request.session_id,
            agent_id=request.agent_id,
            workflow_id=request.session_id  # Using session_id as workflow_id for now
        )
        
        if request.ttl_seconds:
            memory_id = await memory_manager.store_short_term_memory(
                context, request.memory_type, request.content,
                request.metadata, request.ttl_seconds
            )
        else:
            memory_id = await memory_manager.store_persistent_memory(
                context, request.memory_type, request.content, request.metadata
            )
        
        return {"memory_id": memory_id, "status": "stored"}
    
    @app.get("/memory/retrieve/{session_id}/{agent_id}")
    async def retrieve_memory(
        session_id: str,
        agent_id: str,
        memory_type: Optional[str] = None,
        include_short_term: bool = True,
        include_persistent: bool = True
    ):
        context = MemoryContext(
            session_id=session_id,
            agent_id=agent_id,
            workflow_id=session_id
        )
        
        return await memory_manager.get_compressed_context(
            context, include_short_term, include_persistent
        )
    
    @app.post("/state/store", response_model=dict)
    async def store_state(request: StateRequest):
        state_id = await memory_manager.store_module_state(
            request.workflow_id,
            request.module_id,
            request.state_key,
            request.state_data
        )
        
        return {"state_id": state_id, "status": "stored"}
    
    @app.get("/state/retrieve/{workflow_id}/{module_id}/{state_key}")
    async def retrieve_state(workflow_id: str, module_id: str, state_key: str):
        state_data = await memory_manager.retrieve_module_state(
            workflow_id, module_id, state_key
        )
        
        if state_data is None:
            raise HTTPException(status_code=404, detail="State not found")
        
        return {"state_data": state_data}
    
    @app.post("/maintenance/cleanup")
    async def cleanup_memories():
        await memory_manager.cleanup_expired_memories()
        return {"status": "cleanup completed"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
    
    return app

# Example usage and testing
if __name__ == "__main__":
    import asyncio
    
    async def test_memory_manager():
        manager = AdvancedMemoryManager()
        await manager.initialize()
        
        # Test context
        context = MemoryContext(
            session_id="test_session_001",
            agent_id="research_agent",
            workflow_id="research_workflow_001"
        )
        
        # Store some memories
        await manager.store_short_term_memory(
            context, "conversation", "User asked about AutoGen", ttl_seconds=3600
        )
        
        await manager.store_persistent_memory(
            context, "knowledge", "AutoGen is a multi-agent framework"
        )
        
        # Retrieve context
        compressed_context = await manager.get_compressed_context(context)
        print(f"Compressed context: {compressed_context}")
        
        await manager.cleanup()
    
    # Run test
    # asyncio.run(test_memory_manager())
    
    # Run the FastAPI app
    memory_manager = AdvancedMemoryManager()
    app = create_memory_app(memory_manager)
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
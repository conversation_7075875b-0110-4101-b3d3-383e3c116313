import re
from typing import Dict, Any, List
from datetime import datetime
from urllib.parse import urlparse

from .content_processor import ContentProcessor, ProcessedContent, ContentMetadata, ContentType

class TextContentProcessor(ContentProcessor):
    """Processor for plain text content"""
    
    def __init__(self):
        self.name = "TextContentProcessor"
    
    async def process(self, content: str, metadata: ContentMetadata) -> ProcessedContent:
        """Process plain text content"""
        
        processing_steps = []
        
        # Clean and normalize text
        cleaned_content = await self._clean_text(content)
        processing_steps.append("text_cleaning")
        
        # Extract entities
        entities = await self._extract_entities(cleaned_content)
        processing_steps.append("entity_extraction")
        
        # Extract key points
        key_points = await self._extract_key_points(cleaned_content)
        processing_steps.append("key_point_extraction")
        
        # Generate summary
        summary = await self._generate_summary(cleaned_content)
        processing_steps.append("summarization")
        
        # Extract citations
        citations = await self._extract_citations(cleaned_content)
        processing_steps.append("citation_extraction")
        
        # Calculate quality score
        quality_score = await self._calculate_quality_score(cleaned_content, entities, key_points)
        
        return ProcessedContent(
            original_content=content,
            processed_content=cleaned_content,
            metadata=metadata,
            quality_score=quality_score,
            processing_steps=processing_steps,
            extracted_entities=entities,
            key_points=key_points,
            summary=summary,
            citations=citations
        )
    
    def can_process(self, content_type: ContentType) -> bool:
        """Check if can process content type"""
        return content_type in [ContentType.TEXT, ContentType.MARKDOWN]
    
    def get_processor_name(self) -> str:
        """Get processor name"""
        return self.name
    
    async def _clean_text(self, content: str) -> str:
        """Clean and normalize text content"""
        
        # Remove excessive whitespace
        cleaned = re.sub(r'\s+', ' ', content)
        
        # Remove special characters but keep punctuation
        cleaned = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\"\']+', '', cleaned)
        
        # Normalize line endings
        cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove empty lines
        lines = [line.strip() for line in cleaned.split('\n') if line.strip()]
        cleaned = '\n'.join(lines)
        
        return cleaned.strip()
    
    async def _extract_entities(self, content: str) -> Dict[str, List[str]]:
        """Extract named entities from content"""
        
        entities = {
            "technical_terms": [],
            "organizations": [],
            "urls": [],
            "email_addresses": [],
            "file_names": [],
            "api_endpoints": []
        }
        
        # Extract URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`[\]]+'
        entities["urls"] = re.findall(url_pattern, content)
        
        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["email_addresses"] = re.findall(email_pattern, content)
        
        # Extract file names (common extensions)
        file_pattern = r'\b\w+\.(py|js|html|css|json|xml|txt|md|pdf|doc|docx|xlsx|csv)\b'
        entities["file_names"] = re.findall(file_pattern, content, re.IGNORECASE)
        
        # Extract technical terms (simplified)
        technical_terms = [
            "API", "REST", "GraphQL", "JSON", "XML", "HTTP", "HTTPS",
            "database", "SQL", "NoSQL", "MongoDB", "PostgreSQL", "MySQL",
            "JavaScript", "Python", "Java", "C++", "TypeScript", "Go", "Rust",
            "React", "Vue", "Angular", "Node.js", "Express", "Django", "Flask",
            "Docker", "Kubernetes", "AWS", "Azure", "GCP", "CI/CD",
            "machine learning", "AI", "neural network", "algorithm"
        ]
        
        found_terms = []
        content_lower = content.lower()
        for term in technical_terms:
            if term.lower() in content_lower:
                found_terms.append(term)
        
        entities["technical_terms"] = found_terms
        
        # Extract organizations (simplified pattern)
        org_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s+(?:Inc|Corp|Ltd|LLC|Company))?\b'
        entities["organizations"] = re.findall(org_pattern, content)
        
        return entities
    
    async def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from content"""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        key_points = []
        
        # Look for sentences with key indicators
        key_indicators = [
            "important", "key", "main", "primary", "essential", "critical",
            "note that", "remember", "keep in mind", "it's worth",
            "first", "second", "third", "finally", "in conclusion",
            "however", "therefore", "consequently", "as a result"
        ]
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            if any(indicator in sentence_lower for indicator in key_indicators):
                key_points.append(sentence.strip())
        
        # If no key indicators found, take longest sentences as potentially important
        if not key_points:
            sentences_by_length = sorted(sentences, key=len, reverse=True)
            key_points = sentences_by_length[:5]  # Top 5 longest sentences
        
        return key_points[:10]  # Maximum 10 key points
    
    async def _generate_summary(self, content: str) -> str:
        """Generate content summary"""
        
        # Simple extractive summarization
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 20]
        
        if len(sentences) <= 3:
            return content[:500] + "..." if len(content) > 500 else content
        
        # Take first sentence, middle sentence, and last sentence
        summary_sentences = []
        if sentences:
            summary_sentences.append(sentences[0])
            if len(sentences) > 2:
                summary_sentences.append(sentences[len(sentences) // 2])
            summary_sentences.append(sentences[-1])
        
        summary = ". ".join(summary_sentences)
        
        # Limit summary length
        if len(summary) > 500:
            summary = summary[:497] + "..."
        
        return summary
    
    async def _extract_citations(self, content: str) -> List[Dict[str, Any]]:
        """Extract citations and references from content"""
        
        citations = []
        
        # Extract URLs as potential citations
        url_pattern = r'https?://[^\s<>"{}|\\^`[\]]+'
        urls = re.findall(url_pattern, content)
        
        for url in urls:
            citation = {
                "type": "url",
                "url": url,
                "title": self._extract_title_from_url(url),
                "extracted_at": datetime.utcnow().isoformat()
            }
            citations.append(citation)
        
        # Extract academic-style citations (simplified)
        academic_pattern = r'\([A-Za-z]+\s+et\s+al\.,\s+\d{4}\)'
        academic_citations = re.findall(academic_pattern, content)
        
        for citation_text in academic_citations:
            citation = {
                "type": "academic",
                "text": citation_text,
                "extracted_at": datetime.utcnow().isoformat()
            }
            citations.append(citation)
        
        return citations
    
    def _extract_title_from_url(self, url: str) -> str:
        """Extract potential title from URL"""
        
        try:
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            
            if path:
                # Convert dashes/underscores to spaces and title case
                title = path.replace('-', ' ').replace('_', ' ').replace('/', ' > ')
                return title.title()
            else:
                return parsed.netloc
        except:
            return url
    
    async def _calculate_quality_score(
        self, 
        content: str, 
        entities: Dict[str, List[str]], 
        key_points: List[str]
    ) -> float:
        """Calculate content quality score"""
        
        score = 0.0
        
        # Length score (0-0.3)
        content_length = len(content)
        if content_length > 1000:
            score += 0.3
        elif content_length > 500:
            score += 0.2
        elif content_length > 100:
            score += 0.1
        
        # Entity richness score (0-0.3)
        total_entities = sum(len(entity_list) for entity_list in entities.values())
        if total_entities > 10:
            score += 0.3
        elif total_entities > 5:
            score += 0.2
        elif total_entities > 0:
            score += 0.1
        
        # Structure score (0-0.2)
        if key_points:
            score += 0.2
        
        # Technical content score (0-0.2)
        if entities.get("technical_terms"):
            score += 0.2
        
        return min(score, 1.0)

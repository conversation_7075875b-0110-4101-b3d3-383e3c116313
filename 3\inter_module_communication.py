# inter_module_communication/communication_framework.py
import asyncio
import aiohttp
import httpx
import logging
import json
import time
import random
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import jwt
from cryptography.fernet import <PERSON><PERSON>t
import hashlib
import uuid
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel
import uvicorn
import redis.asyncio as redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Enums
class MessagePriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class ModuleStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"

# Data Classes
@dataclass
class ModuleInfo:
    module_id: str
    name: str
    version: str
    base_url: str
    health_endpoint: str = "/health"
    capabilities: List[str] = None
    dependencies: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []
        if self.dependencies is None:
            self.dependencies = []
        if self.metadata is None:
            self.metadata = {}

@dataclass
class InterModuleMessage:
    message_id: str
    source_module: str
    target_module: str
    endpoint: str
    payload: Dict[str, Any]
    priority: MessagePriority = MessagePriority.NORMAL
    timeout: int = 30
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = None
    expires_at: datetime = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.expires_at is None:
            self.expires_at = self.created_at + timedelta(minutes=5)

@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 5
    success_threshold: int = 3
    timeout_seconds: int = 60
    slow_call_threshold: float = 1.0  # seconds
    slow_call_rate_threshold: float = 0.5  # 50%

# Circuit Breaker Implementation
class CircuitBreaker:
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.slow_calls = 0
        self.total_calls = 0
        self.call_times: List[float] = []
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute a function with circuit breaker protection"""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN")
        
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            call_duration = time.time() - start_time
            
            self._record_success(call_duration)
            return result
            
        except Exception as e:
            call_duration = time.time() - start_time
            self._record_failure(call_duration)
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt to reset"""
        if self.last_failure_time is None:
            return True
        
        return (datetime.utcnow() - self.last_failure_time).total_seconds() > self.config.timeout_seconds
    
    def _record_success(self, call_duration: float):
        """Record a successful call"""
        self.total_calls += 1
        self.call_times.append(call_duration)
        
        if call_duration > self.config.slow_call_threshold:
            self.slow_calls += 1
        
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.success_count = 0
                logger.info("Circuit breaker CLOSED after successful recovery")
        
        # Clean old call times (keep last 100 calls)
        if len(self.call_times) > 100:
            self.call_times = self.call_times[-100:]
            if self.total_calls > 100:
                self.slow_calls = sum(1 for t in self.call_times if t > self.config.slow_call_threshold)
                self.total_calls = len(self.call_times)
    
    def _record_failure(self, call_duration: float):
        """Record a failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            logger.warning("Circuit breaker OPEN after failure in HALF_OPEN state")
        elif self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker OPEN after {self.failure_count} failures")
    
    def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status"""
        slow_call_rate = self.slow_calls / max(self.total_calls, 1)
        
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "slow_call_rate": slow_call_rate,
            "is_slow_calls_threshold_exceeded": slow_call_rate > self.config.slow_call_rate_threshold
        }

# Retry Mechanism
class RetryPolicy:
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, retry_count: int) -> float:
        """Calculate delay for retry attempt"""
        if retry_count <= 0:
            return 0
        
        delay = self.base_delay * (self.exponential_base ** (retry_count - 1))
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay

# Authentication and Security
class AuthenticationManager:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.fernet = Fernet(Fernet.generate_key())
    
    def generate_token(self, module_id: str, capabilities: List[str]) -> str:
        """Generate JWT token for module authentication"""
        payload = {
            "module_id": module_id,
            "capabilities": capabilities,
            "issued_at": datetime.utcnow().timestamp(),
            "expires_at": (datetime.utcnow() + timedelta(hours=24)).timestamp()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm="HS256")
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])
            
            if payload["expires_at"] < datetime.utcnow().timestamp():
                raise jwt.ExpiredSignatureError("Token expired")
            
            return payload
        
        except jwt.InvalidTokenError as e:
            raise Exception(f"Invalid token: {e}")
    
    def encrypt_payload(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.fernet.encrypt(data.encode()).decode()
    
    def decrypt_payload(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.fernet.decrypt(encrypted_data.encode()).decode()

# Service Discovery
class ServiceRegistry:
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.modules: Dict[str, ModuleInfo] = {}
        self.health_check_interval = 30  # seconds
    
    async def register_module(self, module_info: ModuleInfo) -> bool:
        """Register a module in the service registry"""
        try:
            self.modules[module_info.module_id] = module_info
            
            # Store in Redis for persistence
            await self.redis_client.hset(
                "service_registry",
                module_info.module_id,
                json.dumps(asdict(module_info), default=str)
            )
            
            logger.info(f"Registered module: {module_info.module_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register module {module_info.module_id}: {e}")
            return False
    
    async def unregister_module(self, module_id: str) -> bool:
        """Unregister a module from the service registry"""
        try:
            if module_id in self.modules:
                del self.modules[module_id]
            
            await self.redis_client.hdel("service_registry", module_id)
            
            logger.info(f"Unregistered module: {module_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister module {module_id}: {e}")
            return False
    
    async def discover_module(self, module_id: str) -> Optional[ModuleInfo]:
        """Discover a module by ID"""
        if module_id in self.modules:
            return self.modules[module_id]
        
        # Try to load from Redis
        try:
            data = await self.redis_client.hget("service_registry", module_id)
            if data:
                module_data = json.loads(data)
                module_info = ModuleInfo(**module_data)
                self.modules[module_id] = module_info
                return module_info
        
        except Exception as e:
            logger.error(f"Error discovering module {module_id}: {e}")
        
        return None
    
    async def discover_modules_by_capability(self, capability: str) -> List[ModuleInfo]:
        """Discover modules that provide a specific capability"""
        matching_modules = []
        
        for module_info in self.modules.values():
            if capability in module_info.capabilities:
                matching_modules.append(module_info)
        
        return matching_modules
    
    async def get_all_modules(self) -> List[ModuleInfo]:
        """Get all registered modules"""
        return list(self.modules.values())

# Health Monitoring
class HealthMonitor:
    def __init__(self, service_registry: ServiceRegistry):
        self.service_registry = service_registry
        self.health_status: Dict[str, ModuleStatus] = {}
        self.monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self):
        """Start health monitoring background task"""
        if self.monitor_task is None or self.monitor_task.done():
            self.monitor_task = asyncio.create_task(self._monitor_loop())
            logger.info("Health monitoring started")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        if self.monitor_task and not self.monitor_task.done():
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            logger.info("Health monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while True:
            try:
                modules = await self.service_registry.get_all_modules()
                
                for module in modules:
                    await self._check_module_health(module)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(30)
    
    async def _check_module_health(self, module: ModuleInfo):
        """Check health of a specific module"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{module.base_url}{module.health_endpoint}")
                
                if response.status_code == 200:
                    self.health_status[module.module_id] = ModuleStatus.HEALTHY
                else:
                    self.health_status[module.module_id] = ModuleStatus.DEGRADED
                    
        except httpx.TimeoutException:
            self.health_status[module.module_id] = ModuleStatus.DEGRADED
            logger.warning(f"Health check timeout for module: {module.module_id}")
            
        except Exception as e:
            self.health_status[module.module_id] = ModuleStatus.UNHEALTHY
            logger.error(f"Health check failed for module {module.module_id}: {e}")
    
    def get_module_status(self, module_id: str) -> ModuleStatus:
        """Get current health status of a module"""
        return self.health_status.get(module_id, ModuleStatus.OFFLINE)
    
    def get_all_statuses(self) -> Dict[str, ModuleStatus]:
        """Get health status of all modules"""
        return self.health_status.copy()

# Message Queue with Priority
class PriorityMessageQueue:
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.queue_key = "inter_module_message_queue"
    
    async def enqueue_message(self, message: InterModuleMessage) -> bool:
        """Add message to priority queue"""
        try:
            message_data = json.dumps(asdict(message), default=str)
            priority_score = message.priority.value * 1000 + int(time.time())
            
            await self.redis_client.zadd(
                self.queue_key,
                {message_data: priority_score}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enqueue message: {e}")
            return False
    
    async def dequeue_message(self) -> Optional[InterModuleMessage]:
        """Get highest priority message from queue"""
        try:
            # Get highest priority message (highest score)
            result = await self.redis_client.zpopmax(self.queue_key)
            
            if result:
                message_data, _ = result[0]
                message_dict = json.loads(message_data)
                return InterModuleMessage(**message_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to dequeue message: {e}")
            return None
    
    async def get_queue_size(self) -> int:
        """Get current queue size"""
        return await self.redis_client.zcard(self.queue_key)

# Main Communication Manager
class InterModuleCommunicationManager:
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        secret_key: str = "your-secret-key-here"
    ):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.auth_manager = AuthenticationManager(secret_key)
        self.service_registry: Optional[ServiceRegistry] = None
        self.health_monitor: Optional[HealthMonitor] = None
        self.message_queue: Optional[PriorityMessageQueue] = None
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_policy = RetryPolicy()
        
        # Performance metrics
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "circuit_breaker_trips": 0
        }
    
    async def initialize(self):
        """Initialize the communication manager"""
        self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
        await self.redis_client.ping()
        
        self.service_registry = ServiceRegistry(self.redis_client)
        self.health_monitor = HealthMonitor(self.service_registry)
        self.message_queue = PriorityMessageQueue(self.redis_client)
        
        await self.health_monitor.start_monitoring()
        
        logger.info("Inter-module communication manager initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.health_monitor:
            await self.health_monitor.stop_monitoring()
        
        if self.redis_client:
            await self.redis_client.close()
    
    def get_circuit_breaker(self, module_id: str) -> CircuitBreaker:
        """Get or create circuit breaker for module"""
        if module_id not in self.circuit_breakers:
            config = CircuitBreakerConfig()
            self.circuit_breakers[module_id] = CircuitBreaker(config)
        
        return self.circuit_breakers[module_id]
    
    async def send_message(
        self,
        source_module: str,
        target_module: str,
        endpoint: str,
        payload: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        timeout: int = 30,
        use_queue: bool = False
    ) -> Dict[str, Any]:
        """Send message to another module"""
        
        message = InterModuleMessage(
            message_id=str(uuid.uuid4()),
            source_module=source_module,
            target_module=target_module,
            endpoint=endpoint,
            payload=payload,
            priority=priority,
            timeout=timeout
        )
        
        if use_queue:
            success = await self.message_queue.enqueue_message(message)
            return {"queued": success, "message_id": message.message_id}
        else:
            return await self._send_direct_message(message)
    
    async def _send_direct_message(self, message: InterModuleMessage) -> Dict[str, Any]:
        """Send message directly to target module"""
        start_time = time.time()
        self.metrics["total_requests"] += 1
        
        try:
            # Discover target module
            target_module = await self.service_registry.discover_module(message.target_module)
            if not target_module:
                raise Exception(f"Module {message.target_module} not found in registry")
            
            # Check module health
            module_status = self.health_monitor.get_module_status(message.target_module)
            if module_status == ModuleStatus.OFFLINE:
                raise Exception(f"Module {message.target_module} is offline")
            
            # Get circuit breaker
            circuit_breaker = self.get_circuit_breaker(message.target_module)
            
            # Execute with circuit breaker and retry
            result = await self._execute_with_retry(circuit_breaker, message, target_module)
            
            # Update metrics
            response_time = time.time() - start_time
            self.metrics["successful_requests"] += 1
            self._update_average_response_time(response_time)
            
            return result
            
        except Exception as e:
            self.metrics["failed_requests"] += 1
            logger.error(f"Failed to send message to {message.target_module}: {e}")
            raise e
    
    async def _execute_with_retry(
        self,
        circuit_breaker: CircuitBreaker,
        message: InterModuleMessage,
        target_module: ModuleInfo
    ) -> Dict[str, Any]:
        """Execute message with retry logic"""
        
        async def make_request():
            url = f"{target_module.base_url}{message.endpoint}"
            
            # Generate authentication token
            token = self.auth_manager.generate_token(
                message.source_module,
                ["inter_module_communication"]
            )
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
                "X-Message-ID": message.message_id,
                "X-Source-Module": message.source_module,
                "X-Correlation-ID": message.correlation_id or str(uuid.uuid4())
            }
            
            async with httpx.AsyncClient(timeout=message.timeout) as client:
                response = await client.post(url, json=message.payload, headers=headers)
                response.raise_for_status()
                return response.json()
        
        # Try with circuit breaker
        for attempt in range(message.max_retries + 1):
            try:
                return await circuit_breaker.call(make_request)
                
            except Exception as e:
                message.retry_count = attempt + 1
                
                if attempt < message.max_retries:
                    delay = self.retry_policy.get_delay(attempt + 1)
                    logger.warning(f"Retry {attempt + 1} for message {message.message_id} after {delay}s delay")
                    await asyncio.sleep(delay)
                else:
                    raise e
    
    def _update_average_response_time(self, response_time: float):
        """Update average response time metric"""
        total_requests = self.metrics["successful_requests"]
        current_avg = self.metrics["average_response_time"]
        
        self.metrics["average_response_time"] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    async def process_message_queue(self):
        """Process messages from the queue (for background processing)"""
        while True:
            try:
                message = await self.message_queue.dequeue_message()
                
                if message:
                    await self._send_direct_message(message)
                else:
                    await asyncio.sleep(1)  # No messages, wait briefly
                    
            except Exception as e:
                logger.error(f"Error processing message queue: {e}")
                await asyncio.sleep(1)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        success_rate = 0.0
        if self.metrics["total_requests"] > 0:
            success_rate = self.metrics["successful_requests"] / self.metrics["total_requests"]
        
        return {
            **self.metrics,
            "success_rate": success_rate,
            "circuit_breaker_status": {
                module_id: cb.get_status() 
                for module_id, cb in self.circuit_breakers.items()
            }
        }

# FastAPI Application
def create_communication_app(comm_manager: InterModuleCommunicationManager) -> FastAPI:
    app = FastAPI(title="Inter-Module Communication Framework", version="1.0.0")
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    security = HTTPBearer()
    
    async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
        try:
            payload = comm_manager.auth_manager.verify_token(credentials.credentials)
            return payload
        except Exception as e:
            raise HTTPException(status_code=401, detail=str(e))
    
    @app.on_event("startup")
    async def startup():
        await comm_manager.initialize()
    
    @app.on_event("shutdown")
    async def shutdown():
        await comm_manager.cleanup()
    
    # Pydantic models
    class SendMessageRequest(BaseModel):
        target_module: str
        endpoint: str
        payload: Dict[str, Any]
        priority: str = "normal"
        timeout: int = 30
        use_queue: bool = False
    
    class RegisterModuleRequest(BaseModel):
        module_id: str
        name: str
        version: str
        base_url: str
        health_endpoint: str = "/health"
        capabilities: List[str] = []
        dependencies: List[str] = []
        metadata: Dict[str, Any] = {}
    
    @app.post("/send")
    async def send_message(
        request: SendMessageRequest,
        token_data: dict = Depends(verify_token)
    ):
        priority = MessagePriority[request.priority.upper()]
        
        result = await comm_manager.send_message(
            source_module=token_data["module_id"],
            target_module=request.target_module,
            endpoint=request.endpoint,
            payload=request.payload,
            priority=priority,
            timeout=request.timeout,
            use_queue=request.use_queue
        )
        
        return result
    
    @app.post("/registry/register")
    async def register_module(
        request: RegisterModuleRequest,
        token_data: dict = Depends(verify_token)
    ):
        module_info = ModuleInfo(
            module_id=request.module_id,
            name=request.name,
            version=request.version,
            base_url=request.base_url,
            health_endpoint=request.health_endpoint,
            capabilities=request.capabilities,
            dependencies=request.dependencies,
            metadata=request.metadata
        )
        
        success = await comm_manager.service_registry.register_module(module_info)
        return {"success": success}
    
    @app.delete("/registry/{module_id}")
    async def unregister_module(
        module_id: str,
        token_data: dict = Depends(verify_token)
    ):
        success = await comm_manager.service_registry.unregister_module(module_id)
        return {"success": success}
    
    @app.get("/registry/discover/{module_id}")
    async def discover_module(module_id: str, token_data: dict = Depends(verify_token)):
        module = await comm_manager.service_registry.discover_module(module_id)
        if module:
            return asdict(module)
        raise HTTPException(status_code=404, detail="Module not found")
    
    @app.get("/registry/capability/{capability}")
    async def discover_by_capability(
        capability: str,
        token_data: dict = Depends(verify_token)
    ):
        modules = await comm_manager.service_registry.discover_modules_by_capability(capability)
        return [asdict(module) for module in modules]
    
    @app.get("/health/status")
    async def get_health_status(token_data: dict = Depends(verify_token)):
        return comm_manager.health_monitor.get_all_statuses()
    
    @app.get("/metrics")
    async def get_metrics(token_data: dict = Depends(verify_token)):
        return comm_manager.get_metrics()
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
    
    return app

# Example usage
if __name__ == "__main__":
    comm_manager = InterModuleCommunicationManager()
    app = create_communication_app(comm_manager)
    
    uvicorn.run(app, host="0.0.0.0", port=8003)
from .processors.content_processor import ProcessedContent
from common.module_client import <PERSON><PERSON>le<PERSON><PERSON>
from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime, timedelta
import hashlib
import json
import sys
from pathlib import Path

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))


class ContentMemoryManager:
    """Manage content processing memory and caching"""

    def __init__(self, module_client: ModuleClient):
        self.module_client = module_client
        self.local_cache = {}
        self.cache_expiry = {}
        self.max_cache_size = 1000
        self.default_ttl = 3600  # 1 hour

    async def get_processed_content(self, content_hash: str) -> Optional[ProcessedContent]:
        """Retrieve processed content from memory"""

        # Check local cache first
        if content_hash in self.local_cache:
            if datetime.utcnow() < self.cache_expiry.get(content_hash, datetime.utcnow()):
                return self.local_cache[content_hash]
            else:
                # Remove expired content
                self._remove_from_cache(content_hash)

        # Check memory module
        try:
            memory_result = await self.module_client.call_module(
                target_module="memory-state-management",
                operation="get",
                parameters={
                    "key": f"processed_content:{content_hash}",
                    "namespace": "content_processing"
                }
            )

            if memory_result.get("success") and memory_result.get("data"):
                # Deserialize and cache locally
                content_data = memory_result["data"]
                processed_content = self._deserialize_processed_content(
                    content_data)
                self._add_to_cache(content_hash, processed_content)
                return processed_content

        except Exception as e:
            print(f"Failed to retrieve from memory module: {e}")

        return None

    async def store_processed_content(self, processed_content: ProcessedContent, ttl: int = None) -> bool:
        """Store processed content in memory"""

        content_hash = processed_content.metadata.content_hash
        ttl = ttl or self.default_ttl

        # Store in local cache
        self._add_to_cache(content_hash, processed_content, ttl)

        # Store in memory module
        try:
            serialized_content = self._serialize_processed_content(
                processed_content)

            memory_result = await self.module_client.call_module(
                target_module="memory-state-management",
                operation="set",
                parameters={
                    "key": f"processed_content:{content_hash}",
                    "value": serialized_content,
                    "namespace": "content_processing",
                    "ttl": ttl
                }
            )

            return memory_result.get("success", False)

        except Exception as e:
            print(f"Failed to store in memory module: {e}")
            return False

    async def get_content_statistics(self) -> Dict[str, Any]:
        """Get content processing statistics from memory"""

        try:
            stats_result = await self.module_client.call_module(
                target_module="memory-state-management",
                operation="get",
                parameters={
                    "key": "processing_statistics",
                    "namespace": "content_processing"
                }
            )

            if stats_result.get("success"):
                return stats_result.get("data", {})

        except Exception as e:
            print(f"Failed to get statistics from memory: {e}")

        return {}

    async def update_content_statistics(self, stats: Dict[str, Any]) -> bool:
        """Update content processing statistics in memory"""

        try:
            stats_result = await self.module_client.call_module(
                target_module="memory-state-management",
                operation="set",
                parameters={
                    "key": "processing_statistics",
                    "value": stats,
                    "namespace": "content_processing",
                    "ttl": 86400  # 24 hours
                }
            )

            return stats_result.get("success", False)

        except Exception as e:
            print(f"Failed to update statistics in memory: {e}")
            return False

    async def search_similar_content(self, content_hash: str, similarity_threshold: float = 0.8) -> List[str]:
        """Search for similar processed content"""

        try:
            search_result = await self.module_client.call_module(
                target_module="memory-state-management",
                operation="search",
                parameters={
                    "pattern": "processed_content:*",
                    "namespace": "content_processing",
                    "limit": 50
                }
            )

            if search_result.get("success"):
                similar_hashes = []
                content_entries = search_result.get("data", [])

                for entry in content_entries:
                    entry_hash = entry.get("key", "").replace(
                        "processed_content:", "")
                    if entry_hash != content_hash:
                        # Simple similarity check based on hash similarity
                        # In production, use more sophisticated similarity measures
                        similarity = self._calculate_hash_similarity(
                            content_hash, entry_hash)
                        if similarity >= similarity_threshold:
                            similar_hashes.append(entry_hash)

                return similar_hashes

        except Exception as e:
            print(f"Failed to search similar content: {e}")

        return []

    def _add_to_cache(self, content_hash: str, processed_content: ProcessedContent, ttl: int = None):
        """Add content to local cache"""

        # Ensure cache size limit
        if len(self.local_cache) >= self.max_cache_size:
            self._evict_oldest_cache_entry()

        self.local_cache[content_hash] = processed_content
        self.cache_expiry[content_hash] = datetime.utcnow(
        ) + timedelta(seconds=ttl or self.default_ttl)

    def _remove_from_cache(self, content_hash: str):
        """Remove content from local cache"""

        if content_hash in self.local_cache:
            del self.local_cache[content_hash]
        if content_hash in self.cache_expiry:
            del self.cache_expiry[content_hash]

    def _evict_oldest_cache_entry(self):
        """Evict oldest cache entry"""

        if not self.cache_expiry:
            return

        oldest_hash = min(self.cache_expiry.keys(),
                          key=lambda k: self.cache_expiry[k])
        self._remove_from_cache(oldest_hash)

    def _serialize_processed_content(self, processed_content: ProcessedContent) -> Dict[str, Any]:
        """Serialize processed content for storage"""

        return {
            "original_content": processed_content.original_content,
            "processed_content": processed_content.processed_content,
            "quality_score": processed_content.quality_score,
            "processing_steps": processed_content.processing_steps,
            "extracted_entities": processed_content.extracted_entities,
            "key_points": processed_content.key_points,
            "summary": processed_content.summary,
            "citations": processed_content.citations,
            "metadata": {
                "content_type": processed_content.metadata.content_type.value,
                "source_url": processed_content.metadata.source_url,
                "source_type": processed_content.metadata.source_type,
                "extracted_at": processed_content.metadata.extracted_at.isoformat(),
                "content_hash": processed_content.metadata.content_hash,
                "language": processed_content.metadata.language,
                "encoding": processed_content.metadata.encoding,
                "size_bytes": processed_content.metadata.size_bytes
            }
        }

    def _deserialize_processed_content(self, data: Dict[str, Any]) -> ProcessedContent:
        """Deserialize processed content from storage"""

        from .processors.content_processor import ContentMetadata, ContentType

        metadata = ContentMetadata(
            content_type=ContentType(data["metadata"]["content_type"]),
            source_url=data["metadata"]["source_url"],
            source_type=data["metadata"]["source_type"],
            extracted_at=datetime.fromisoformat(
                data["metadata"]["extracted_at"]),
            content_hash=data["metadata"]["content_hash"],
            language=data["metadata"]["language"],
            encoding=data["metadata"]["encoding"],
            size_bytes=data["metadata"]["size_bytes"]
        )

        return ProcessedContent(
            original_content=data["original_content"],
            processed_content=data["processed_content"],
            metadata=metadata,
            quality_score=data["quality_score"],
            processing_steps=data["processing_steps"],
            extracted_entities=data["extracted_entities"],
            key_points=data["key_points"],
            summary=data["summary"],
            citations=data["citations"]
        )

    def _calculate_hash_similarity(self, hash1: str, hash2: str) -> float:
        """Calculate similarity between two content hashes"""

        # Simple similarity based on common characters
        # In production, use more sophisticated algorithms

        if len(hash1) != len(hash2):
            return 0.0

        common_chars = sum(1 for a, b in zip(hash1, hash2) if a == b)
        return common_chars / len(hash1)

    async def cleanup_expired_cache(self):
        """Clean up expired cache entries"""

        current_time = datetime.utcnow()
        expired_hashes = [
            content_hash for content_hash, expiry_time in self.cache_expiry.items()
            if current_time >= expiry_time
        ]

        for content_hash in expired_hashes:
            self._remove_from_cache(content_hash)

        print(f"Cleaned up {len(expired_hashes)} expired cache entries")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get local cache statistics"""

        current_time = datetime.utcnow()
        active_entries = sum(
            1 for expiry_time in self.cache_expiry.values()
            if current_time < expiry_time
        )

        return {
            "total_entries": len(self.local_cache),
            "active_entries": active_entries,
            "expired_entries": len(self.local_cache) - active_entries,
            "cache_size_limit": self.max_cache_size,
            "cache_utilization": len(self.local_cache) / self.max_cache_size
        }

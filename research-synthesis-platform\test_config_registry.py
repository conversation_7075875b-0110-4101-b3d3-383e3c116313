#!/usr/bin/env python3
"""
Simple test script to verify the Configuration Registry module works
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_config_registry():
    """Test the Configuration Registry module"""
    
    base_url = "http://localhost:8000"
    
    # Test API key (this would be from the auth manager)
    api_key = "test-api-key"
    
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        
        # Test health check
        print("Testing health check...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"Health check: {response.status_code}")
            if response.status_code == 200:
                print(f"Health response: {response.json()}")
        except Exception as e:
            print(f"Health check failed: {e}")
        
        # Test info endpoint
        print("\nTesting info endpoint...")
        try:
            response = await client.get(f"{base_url}/info")
            print(f"Info: {response.status_code}")
            if response.status_code == 200:
                print(f"Info response: {response.json()}")
        except Exception as e:
            print(f"Info check failed: {e}")
        
        print("\nConfiguration Registry test completed!")


if __name__ == "__main__":
    asyncio.run(test_config_registry())

from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta
import jwt
import secrets
import hashlib
from dataclasses import dataclass
from enum import Enum


class Permission(str, Enum):
    READ_CONFIG = "config:read"
    WRITE_CONFIG = "config:write"
    REGISTER_SERVICE = "service:register"
    DISCOVER_SERVICE = "service:discover"
    ADMIN = "admin"


@dataclass
class ModuleCredentials:
    module_id: str
    api_key: str
    permissions: List[Permission]
    created_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool = True


class AuthenticationManager:
    """Handle authentication and authorization for inter-module communication"""
    
    def __init__(self, jwt_secret: str):
        self.jwt_secret = jwt_secret
        self.module_credentials: Dict[str, ModuleCredentials] = {}
        self.active_tokens: Dict[str, Dict[str, Any]] = {}
        
        # Initialize default module credentials
        self._initialize_default_credentials()
    
    def _initialize_default_credentials(self):
        """Initialize credentials for all modules"""
        default_modules = [
            ("core-agent-orchestration", [Permission.READ_CONFIG, Permission.DISCOVER_SERVICE]),
            ("external-integration", [Permission.READ_CONFIG, Permission.DISCOVER_SERVICE]),
            ("content-processing", [Permission.READ_CONFIG, Permission.DISCOVER_SERVICE]),
            ("research-synthesis", [Permission.READ_CONFIG, Permission.DISCOVER_SERVICE]),
            ("content-generation", [Permission.READ_CONFIG, Permission.DISCOVER_SERVICE]),
            ("memory-state-management", [Permission.READ_CONFIG, Permission.WRITE_CONFIG, Permission.DISCOVER_SERVICE]),
            ("configuration-registry", [Permission.ADMIN])
        ]
        
        for module_id, permissions in default_modules:
            self.create_module_credentials(module_id, permissions)
    
    def create_module_credentials(self, module_id: str, permissions: List[Permission]) -> ModuleCredentials:
        """Create credentials for a module"""
        api_key = self._generate_api_key()
        
        credentials = ModuleCredentials(
            module_id=module_id,
            api_key=api_key,
            permissions=permissions,
            created_at=datetime.utcnow()
        )
        
        self.module_credentials[module_id] = credentials
        return credentials
    
    def generate_jwt_token(self, module_id: str, expires_in_hours: int = 24) -> Optional[str]:
        """Generate JWT token for a module"""
        credentials = self.module_credentials.get(module_id)
        if not credentials or not credentials.is_active:
            return None
        
        expiration = datetime.utcnow() + timedelta(hours=expires_in_hours)
        
        payload = {
            "module_id": module_id,
            "permissions": [p.value for p in credentials.permissions],
            "iat": datetime.utcnow(),
            "exp": expiration
        }
        
        token = jwt.encode(payload, self.jwt_secret, algorithm="HS256")
        
        # Store active token
        self.active_tokens[token] = {
            "module_id": module_id,
            "expires_at": expiration,
            "permissions": credentials.permissions
        }
        
        return token
    
    def validate_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate JWT token and return payload"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            
            # Check if token is in active tokens
            if token in self.active_tokens:
                token_info = self.active_tokens[token]
                if datetime.utcnow() < token_info["expires_at"]:
                    return payload
                else:
                    # Remove expired token
                    del self.active_tokens[token]
            
            return None
            
        except jwt.InvalidTokenError:
            return None
    
    def validate_api_key(self, api_key: str) -> Optional[ModuleCredentials]:
        """Validate API key and return module credentials"""
        for credentials in self.module_credentials.values():
            if credentials.api_key == api_key and credentials.is_active:
                return credentials
        return None
    
    def check_permission(self, module_id: str, required_permission: Permission) -> bool:
        """Check if module has required permission"""
        credentials = self.module_credentials.get(module_id)
        if not credentials or not credentials.is_active:
            return False
        
        return (required_permission in credentials.permissions or 
                Permission.ADMIN in credentials.permissions)
    
    def revoke_module_access(self, module_id: str) -> bool:
        """Revoke access for a module"""
        if module_id in self.module_credentials:
            self.module_credentials[module_id].is_active = False
            
            # Remove active tokens for this module
            tokens_to_remove = [
                token for token, info in self.active_tokens.items()
                if info["module_id"] == module_id
            ]
            
            for token in tokens_to_remove:
                del self.active_tokens[token]
            
            return True
        return False
    
    def refresh_api_key(self, module_id: str) -> Optional[str]:
        """Refresh API key for a module"""
        if module_id in self.module_credentials:
            new_api_key = self._generate_api_key()
            self.module_credentials[module_id].api_key = new_api_key
            return new_api_key
        return None
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key"""
        return secrets.token_urlsafe(32)
    
    def get_module_permissions(self, module_id: str) -> List[Permission]:
        """Get permissions for a module"""
        credentials = self.module_credentials.get(module_id)
        return credentials.permissions if credentials else []
    
    def list_active_modules(self) -> List[str]:
        """List all active modules"""
        return [
            module_id for module_id, credentials in self.module_credentials.items()
            if credentials.is_active
        ]

from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# Add shared path for imports
shared_path = Path(__file__).parent.parent.parent.parent.parent / "shared"
sys.path.insert(0, str(shared_path))

from common.api_framework import create_api_app, create_success_response, create_error_response
from common.module_client import ModuleClient
from .core.processors.content_processor import ContentProcessingPipeline, ContentType, ProcessedContent

# Pydantic models for API
class ContentProcessingRequest(BaseModel):
    content: str
    content_type: str = "text"
    source_url: str = ""
    source_type: str = "web"
    processing_options: Optional[Dict[str, Any]] = None

class BatchProcessingRequest(BaseModel):
    content_items: List[Dict[str, Any]]
    processing_options: Optional[Dict[str, Any]] = None

class ContentAnalysisRequest(BaseModel):
    content: str
    analysis_types: List[str] = ["entities", "key_points", "summary", "quality"]

# Global instances
module_client: Optional[ModuleClient] = None
processing_pipeline: Optional[ContentProcessingPipeline] = None

# Create FastAPI app
app = create_api_app(
    title="Content Processing Module",
    description="Intelligent content analysis and processing pipeline",
    version="1.0.0",
    module_name="content-processing"
)

@app.on_event("startup")
async def startup():
    """Initialize module on startup"""
    global module_client, processing_pipeline
    
    try:
        # Initialize module client
        module_client = ModuleClient(
            module_id="content-processing",
            config_registry_url=os.getenv("CONFIG_REGISTRY_URL", "http://localhost:8000"),
            api_key=os.getenv("MODULE_API_KEY", "default-key")
        )
        
        # Initialize processing pipeline
        processing_pipeline = ContentProcessingPipeline()
        
        # Register with service registry
        await module_client.register_self(
            name="content-processing",
            version="1.0.0",
            host=os.getenv("HOST", "localhost"),
            port=int(os.getenv("PORT", "8003")),
            capabilities=[
                "content_analysis",
                "text_processing",
                "html_processing",
                "entity_extraction",
                "summarization",
                "quality_assessment"
            ],
            metadata={
                "supported_content_types": [ct.value for ct in ContentType],
                "processor_count": len(processing_pipeline.processors)
            }
        )
        
        print("Content Processing Module started successfully")
        
    except Exception as e:
        print(f"Failed to start module: {e}")
        raise

@app.post("/api/process", response_model=Dict[str, Any])
async def process_content(request: ContentProcessingRequest):
    """Process content through the analysis pipeline"""
    try:
        start_time = datetime.utcnow()
        
        # Convert string content type to enum
        content_type = ContentType(request.content_type.lower())
        
        # Process content
        result = await processing_pipeline.process_content(
            content=request.content,
            content_type=content_type,
            source_url=request.source_url,
            source_type=request.source_type
        )
        
        execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        # Format result for API response
        processed_result = {
            "original_content_length": len(result.original_content),
            "processed_content_length": len(result.processed_content),
            "quality_score": result.quality_score,
            "processing_steps": result.processing_steps,
            "extracted_entities": result.extracted_entities,
            "key_points": result.key_points,
            "summary": result.summary,
            "citations": result.citations,
            "metadata": {
                "content_type": result.metadata.content_type.value,
                "source_url": result.metadata.source_url,
                "source_type": result.metadata.source_type,
                "content_hash": result.metadata.content_hash,
                "extracted_at": result.metadata.extracted_at.isoformat(),
                "size_bytes": result.metadata.size_bytes
            }
        }
        
        return create_success_response(
            data=processed_result,
            message="Content processed successfully",
            execution_time_ms=execution_time
        ).dict()
        
    except ValueError as e:
        return create_error_response(
            message="Invalid content type",
            errors=[str(e)]
        ).dict()
    except Exception as e:
        return create_error_response(
            message="Failed to process content",
            errors=[str(e)]
        ).dict()

@app.post("/api/batch-process", response_model=Dict[str, Any])
async def batch_process_content(request: BatchProcessingRequest):
    """Process multiple content items in batch"""
    try:
        start_time = datetime.utcnow()
        
        processed_items = []
        errors = []
        
        for i, item in enumerate(request.content_items):
            try:
                content_type = ContentType(item.get("content_type", "text").lower())
                
                result = await processing_pipeline.process_content(
                    content=item["content"],
                    content_type=content_type,
                    source_url=item.get("source_url", ""),
                    source_type=item.get("source_type", "web")
                )
                
                processed_items.append({
                    "index": i,
                    "quality_score": result.quality_score,
                    "key_points_count": len(result.key_points),
                    "entities_count": sum(len(entities) for entities in result.extracted_entities.values()),
                    "summary_length": len(result.summary),
                    "processing_steps": result.processing_steps
                })
                
            except Exception as e:
                errors.append({
                    "index": i,
                    "error": str(e)
                })
        
        execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        return create_success_response(
            data={
                "total_items": len(request.content_items),
                "processed_successfully": len(processed_items),
                "errors": len(errors),
                "processed_items": processed_items,
                "processing_errors": errors
            },
            message=f"Batch processing completed: {len(processed_items)}/{len(request.content_items)} successful",
            execution_time_ms=execution_time
        ).dict()
        
    except Exception as e:
        return create_error_response(
            message="Failed to process batch",
            errors=[str(e)]
        ).dict()

@app.post("/api/analyze", response_model=Dict[str, Any])
async def analyze_content(request: ContentAnalysisRequest):
    """Perform specific analysis on content"""
    try:
        start_time = datetime.utcnow()
        
        # Process content to get full analysis
        result = await processing_pipeline.process_content(
            content=request.content,
            content_type=ContentType.TEXT,
            source_url="",
            source_type="direct_input"
        )
        
        # Extract requested analysis types
        analysis_result = {}
        
        if "entities" in request.analysis_types:
            analysis_result["entities"] = result.extracted_entities
        
        if "key_points" in request.analysis_types:
            analysis_result["key_points"] = result.key_points
        
        if "summary" in request.analysis_types:
            analysis_result["summary"] = result.summary
        
        if "quality" in request.analysis_types:
            analysis_result["quality_assessment"] = {
                "quality_score": result.quality_score,
                "content_length": len(result.original_content),
                "processed_length": len(result.processed_content),
                "citations_found": len(result.citations)
            }
        
        if "citations" in request.analysis_types:
            analysis_result["citations"] = result.citations
        
        execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        return create_success_response(
            data=analysis_result,
            message="Content analysis completed",
            execution_time_ms=execution_time
        ).dict()
        
    except Exception as e:
        return create_error_response(
            message="Failed to analyze content",
            errors=[str(e)]
        ).dict()

@app.get("/api/supported-types", response_model=Dict[str, Any])
async def get_supported_content_types():
    """Get list of supported content types"""
    try:
        supported_types = processing_pipeline.list_supported_content_types()
        
        return create_success_response(
            data={
                "supported_types": [ct.value for ct in supported_types],
                "processor_count": len(processing_pipeline.processors),
                "capabilities": [
                    "entity_extraction",
                    "key_point_identification",
                    "content_summarization",
                    "quality_assessment",
                    "citation_extraction",
                    "text_cleaning",
                    "html_processing"
                ]
            },
            message="Supported content types retrieved"
        ).dict()
        
    except Exception as e:
        return create_error_response(
            message="Failed to get supported types",
            errors=[str(e)]
        ).dict()

@app.get("/api/statistics", response_model=Dict[str, Any])
async def get_processing_statistics():
    """Get processing pipeline statistics"""
    try:
        stats = processing_pipeline.get_processing_statistics()
        
        return create_success_response(
            data=stats,
            message="Processing statistics retrieved"
        ).dict()
        
    except Exception as e:
        return create_error_response(
            message="Failed to get statistics",
            errors=[str(e)]
        ).dict()

@app.post("/api/validate", response_model=Dict[str, Any])
async def validate_content(request: Dict[str, Any]):
    """Validate content before processing"""
    try:
        content = request.get("content", "")
        content_type = request.get("content_type", "text")
        
        validation_result = {
            "valid": True,
            "warnings": [],
            "recommendations": []
        }
        
        # Content length validation
        if len(content) < 10:
            validation_result["warnings"].append("Content is very short (< 10 characters)")
        elif len(content) > 100000:
            validation_result["warnings"].append("Content is very long (> 100,000 characters)")
            validation_result["recommendations"].append("Consider splitting into smaller chunks")
        
        # Content type validation
        try:
            ContentType(content_type.lower())
        except ValueError:
            validation_result["valid"] = False
            validation_result["warnings"].append(f"Unsupported content type: {content_type}")
        
        # Content quality checks
        if not content.strip():
            validation_result["valid"] = False
            validation_result["warnings"].append("Content is empty or whitespace only")
        
        # Encoding checks
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            validation_result["warnings"].append("Content contains non-UTF-8 characters")
        
        return create_success_response(
            data=validation_result,
            message="Content validation completed"
        ).dict()
        
    except Exception as e:
        return create_error_response(
            message="Failed to validate content",
            errors=[str(e)]
        ).dict()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
